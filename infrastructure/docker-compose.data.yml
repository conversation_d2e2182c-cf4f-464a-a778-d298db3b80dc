services:
  # PostgreSQL for application services (using port 5433 to avoid conflict)
  postgres:
    image: postgres:16.3
    container_name: kaelum-postgres
    environment:
      - POSTGRES_USER=kaelum_user
      - POSTGRES_PASSWORD=kaelum_secure_password_2025
      - POSTGRES_DB=kaelum_main
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5433:5432" # Changed port to avoid conflict with dev PostgreSQL
    networks:
      - kaelum-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kaelum_user -d kaelum_main"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for application services (using port 6380 to avoid conflict)
  redis:
    image: redis:7.2.5-alpine
    container_name: kaelum-redis
    command: redis-server --appendonly yes --requirepass kaelum_redis_password_2025
    volumes:
      - redisdata:/data
    ports:
      - "6380:6379" # Changed port to avoid conflict with dev Redis
    networks:
      - kaelum-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "kaelum_redis_password_2025", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

networks:
  kaelum-network:
    driver: bridge

volumes:
  pgdata:
    name: kaelum_pgdata
  redisdata:
    name: kaelum_redisdata
