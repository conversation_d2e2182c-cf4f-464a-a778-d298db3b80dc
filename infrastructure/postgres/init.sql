-- Kaelum Database Initialization Script
-- This script sets up the basic database structure for Kaelum services

-- Create additional databases for different services if needed
-- CREATE DATABASE kaelum_auth;
-- CREATE DATABASE kaelum_productivity;
-- CREATE DATABASE kaelum_analytics;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create schemas for better organization
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS productivity;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS system;

-- Create a basic users table (placeholder until auth service is implemented)
CREATE TABLE IF NOT EXISTS auth.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an audit log table for tracking changes
CREATE TABLE IF NOT EXISTS system.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON auth.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON auth.users(username);
CREATE INDEX IF NOT EXISTS idx_users_active ON auth.users(is_active);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON system.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON system.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON system.audit_log(user_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert a default admin user (for testing purposes only)
-- Password is 'admin123' (should be changed in production)
INSERT INTO auth.users (email, username, password_hash, is_active, is_verified) 
VALUES (
    '<EMAIL>',
    'admin',
    '$2b$12$rQZ8J8jGx8kKr.vF4F8.duO1N.2YvF8.2YvF8.2YvF8.2YvF8.2YvF8',
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- Create a health check function
CREATE OR REPLACE FUNCTION health_check()
RETURNS TABLE(status TEXT, timestamp TIMESTAMP WITH TIME ZONE) AS $$
BEGIN
    RETURN QUERY SELECT 'healthy'::TEXT, NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant permissions to the kaelum_user
GRANT USAGE ON SCHEMA auth TO kaelum_user;
GRANT USAGE ON SCHEMA productivity TO kaelum_user;
GRANT USAGE ON SCHEMA analytics TO kaelum_user;
GRANT USAGE ON SCHEMA system TO kaelum_user;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA productivity TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA analytics TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA system TO kaelum_user;

GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA productivity TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA analytics TO kaelum_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA system TO kaelum_user;
