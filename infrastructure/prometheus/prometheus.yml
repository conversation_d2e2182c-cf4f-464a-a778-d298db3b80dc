global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]
    metrics_path: /metrics
    scrape_interval: 15s

  # cAdvisor for container metrics
  - job_name: "cadvisor"
    static_configs:
      - targets: ["cadvisor:8080"]
    metrics_path: /metrics
    scrape_interval: 30s

  # APISIX metrics
  - job_name: "apisix"
    static_configs:
      - targets: ["apisix:9091"]
    metrics_path: /apisix/prometheus/metrics
    scrape_interval: 15s

  # PostgreSQL metrics (when pg_exporter is added)
  # - job_name: 'postgres'
  #   static_configs:
  #     - targets: ['postgres-exporter:9187']
  #   metrics_path: /metrics
  #   scrape_interval: 30s

  # Redis metrics (when redis_exporter is added)
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis-exporter:9121']
  #   metrics_path: /metrics
  #   scrape_interval: 30s

  # Application services (to be added when services are implemented)
  # - job_name: 'auth-service'
  #   static_configs:
  #     - targets: ['auth-service:8080']
  #   metrics_path: /metrics
  #   scrape_interval: 15s

  # - job_name: 'smtp-gateway'
  #   static_configs:
  #     - targets: ['smtp-gateway:8080']
  #   metrics_path: /metrics
  #   scrape_interval: 15s

  # - job_name: 'ai-ml-service'
  #   static_configs:
  #     - targets: ['ai-ml-service:8080']
  #   metrics_path: /metrics
  #   scrape_interval: 15s

  # - job_name: 'productivity-backend'
  #   static_configs:
  #     - targets: ['productivity-backend:8080']
  #   metrics_path: /metrics
  #   scrape_interval: 15s
