services:
  # Grafana for visualization
  grafana:
    image: grafana/grafana:11.1.0
    container_name: kaelum-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=kaelum_grafana_admin_2025
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - kaelum-network
    restart: unless-stopped

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:v2.53.0
    container_name: kaelum-prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--web.enable-lifecycle"
      - "--web.enable-admin-api"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - kaelum-network
    restart: unless-stopped

  # Loki for log aggregation
  loki:
    image: grafana/loki:3.1.0
    container_name: kaelum-loki
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki
      - ./loki/loki-config.yaml:/etc/loki/local-config.yaml:ro
    networks:
      - kaelum-network
    restart: unless-stopped

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.58.1
    container_name: kaelum-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686" # UI
      - "14269:14268" # HTTP collector (changed port to avoid conflict)
      - "6831:6831/udp" # Jaeger agent
      - "6832:6832/udp" # Jaeger agent
      - "5778:5778" # Agent configs
      - "4317:4317" # OTLP gRPC
      - "4318:4318" # OTLP HTTP
    networks:
      - kaelum-network
    restart: unless-stopped

  # cAdvisor for container metrics (optimized for macOS)
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.49.1
    container_name: kaelum-cadvisor
    privileged: true
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    ports:
      - "8081:8080" # Changed port to avoid conflicts
    networks:
      - kaelum-network
    restart: unless-stopped

  # Promtail for log collection
  promtail:
    image: grafana/promtail:3.1.0
    container_name: kaelum-promtail
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./promtail/config.yml:/etc/promtail/config.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - kaelum-network
    restart: unless-stopped

networks:
  kaelum-network:
    driver: bridge

volumes:
  grafana-data:
    name: kaelum_grafana-data
  prometheus-data:
    name: kaelum_prometheus-data
  loki-data:
    name: kaelum_loki-data
