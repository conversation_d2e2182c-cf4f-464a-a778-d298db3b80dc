#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

apisix:
  node_listen: 9080 # APISIX listening port
  enable_admin: true
  enable_dev_mode: false # Sets nginx worker_processes to 1 if set to true
  enable_reuseport: true # Enable nginx SO_REUSEPORT switch if set to true
  enable_ipv6: false

deployment:
  role: traditional
  role_traditional:
    config_provider: etcd
  admin:
    admin_key:
      - name: admin
        key: edd1c9f034335f136f87ad84b625c8f1
        role: admin
    allow_admin:
      - 0.0.0.0/0 # Allow from any IP (for Docker networking)
    admin_listen:
      ip: 0.0.0.0
      port: 9180
  etcd:
    host: # it's possible to define multiple etcd hosts addresses of the same etcd cluster.
      - "http://etcd:2379" # multiple etcd address
    prefix: "/apisix" # apisix configurations prefix
    timeout: 30 # 30 seconds

plugins: # plugin list (sorted by priority)
  - real-ip # priority: 23000
  - ai # priority: 22900
  - client-control # priority: 22000
  - proxy-control # priority: 21990
  - request-id # priority: 12015
  - zipkin # priority: 12011
  #- skywalking                    # priority: 12010
  #- opentelemetry                 # priority: 12009
  - jaeger # priority: 12008
  - prometheus # priority: 12001
  - datadog # priority: 12000
  - echo # priority: 11000
  - loggly # priority: 10005
  - http-logger # priority: 10003
  - splunk-hec-logging # priority: 10002
  - ALiCloudSLS # priority: 10001
  - syslog # priority: 10000
  - udp-logger # priority: 9999
  - file-logger # priority: 9998
  - clickhouse-logger # priority: 9997
  - tencent-cloud-cls # priority: 9996
  - inspect # priority: 9995
  #- skywalking-logger             # priority: 9994
  - google-cloud-logging # priority: 9993
  - sls-logger # priority: 9992
  - tcp-logger # priority: 9991
  - kafka-logger # priority: 9990
  - rocketmq-logger # priority: 9989
  - elasticsearch-logger # priority: 9988
  - openwhisk # priority: 9087
  - azure-functions # priority: 9086
  - openfunction # priority: 9085
  - tencent-cloud-function # priority: 9084
  - aws-lambda # priority: 9083
  - function-terminator # priority: 9082
  - lua-resty-http # priority: 9081
  - forward-auth # priority: 9002
  - opa # priority: 9001
  - authz-casbin # priority: 9000
  #- authz-casdoor                 # priority: 8999
  - wolf-rbac # priority: 8998
  - ldap-auth # priority: 8997
  - hmac-auth # priority: 8996
  - basic-auth # priority: 8995
  - jwt-auth # priority: 8994
  - key-auth # priority: 8993
  - consumer-restriction # priority: 8992
  - authz-keycloak # priority: 8991
  #- cas-auth                      # priority: 8990
  - openid-connect # priority: 8989
  - ua-restriction # priority: 8988
  - referer-restriction # priority: 8987
  - csrf # priority: 8986
  - uri-blocker # priority: 8985
  - ip-restriction # priority: 8984
  - limit-req # priority: 8983
  - limit-conn # priority: 8982
  - limit-count # priority: 8981
  - rate-limit # priority: 8980
  #- gm                            # priority: 8979
  - waf # priority: 8978
  - multi-auth # priority: 8977
  - api-breaker # priority: 8976
  - traffic-split # priority: 8975
  - request-validation # priority: 8974
  - workflow # priority: 8973
  - proxy-mirror # priority: 8972
  - kafka-proxy # priority: 8971
  #- dubbo-proxy                   # priority: 8970
  - grpc-transcode # priority: 8969
  - grpc-web # priority: 8968
  - public-api # priority: 8967
  - prometheus # priority: 8966
  - brotli # priority: 8965
  - cors # priority: 8964
  - error-log-logger # priority: 8963
  - proxy-cache # priority: 8962
  - body-transformer # priority: 8961
  - fault-injection # priority: 8960
  - mocking # priority: 8959
  - degraphql # priority: 8958
  - response-rewrite # priority: 8957
  - kafka-logger # priority: 8956
  - proxy-rewrite # priority: 8955
  - workflow # priority: 8954
  - redirect # priority: 8953
  - response-rewrite # priority: 8952
  - workflow # priority: 8951
  - serverless-pre-function # priority: 8950
  - batch-requests # priority: 8949
  - cors # priority: 8948
  - ip-restriction # priority: 8947
  - referer-restriction # priority: 8946
  - uri-blocker # priority: 8945
  - request-validation # priority: 8944
  - chaitin-waf # priority: 8943
  - degraphql # priority: 8942
  - zipkin # priority: 8941
  #- skywalking                    # priority: 8940
  #- opentelemetry                 # priority: 8939
  - jaeger # priority: 8938
  - serverless-post-function # priority: 8937
  - ext-plugin-pre-req # priority: 8936
  - ext-plugin-post-req # priority: 8935

stream_plugins:
  - ip-restriction
  - limit-conn
  - mqtt-proxy
  #- prometheus

plugin_attr:
  prometheus:
    export_addr:
      ip: "0.0.0.0"
      port: 9091
