# CrabShield Auth Service

A comprehensive, enterprise-grade authentication and authorization service built with Rust and Actix-web. This service provides secure user authentication, multi-factor authentication (MFA), role-based access control (RBAC), session management, and email verification.

## 🚀 Features

- **User Authentication**: Secure login/logout with Argon2 password hashing
- **Multi-Factor Authentication (MFA)**: TOTP-based 2FA with backup codes
- **Role-Based Access Control (RBAC)**: Hierarchical roles and permissions
- **Session Management**: Redis-backed sessions with device tracking
- **Email Verification**: Secure email verification with rate limiting
- **Password Reset**: JWT-based password reset with email integration
- **Account Security**: Account lockout, rate limiting, and suspicious activity detection
- **JWT Tokens**: Secure token-based authentication
- **Enterprise Security**: CORS, security headers, input validation

## 📋 Prerequisites

- **Rust** 1.70+ with Cargo
- **PostgreSQL** 14+
- **Redis** 6+
- **SMTP Server** (for email functionality)

## 🛠️ Installation & Setup

### 1. Clone and Build

```bash
cd CrabShield/services/auth-service
cargo build --release
```

### 2. Environment Configuration

Create a `.env` file in the auth-service directory:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/crabshield_auth
DATABASE_MAX_CONNECTIONS=10

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=10

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_ISSUER=crabshield-auth
JWT_AUDIENCE=crabshield-app

# Server Configuration
HOST=0.0.0.0
PORT=8080
ENVIRONMENT=development
LOG_LEVEL=info

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CrabShield

# Security Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://app.crabshield.com
RATE_LIMIT_REQUESTS_PER_MINUTE=60
SESSION_TIMEOUT_HOURS=24
MFA_ISSUER=CrabShield
```

### 3. Database Setup

Run database migrations:

```bash
# Install sqlx-cli if not already installed
cargo install sqlx-cli

# Run migrations
sqlx migrate run
```

### 4. Start the Service

```bash
# Development
cargo run

# Production
cargo run --release
```

The service will start on `http://localhost:8080` by default.

## 🔧 Configuration

### Environment Variables

| Variable               | Description                  | Default       | Required |
|------------------------|------------------------------|---------------|----------|
| `DATABASE_URL`         | PostgreSQL connection string | -             | ✅        |
| `REDIS_URL`            | Redis connection string      | -             | ✅        |
| `JWT_SECRET`           | Secret key for JWT signing   | -             | ✅        |
| `HOST`                 | Server bind address          | `0.0.0.0`     | ❌        |
| `PORT`                 | Server port                  | `8080`        | ❌        |
| `ENVIRONMENT`          | Environment (dev/prod)       | `development` | ❌        |
| `LOG_LEVEL`            | Logging level                | `info`        | ❌        |
| `SMTP_HOST`            | SMTP server hostname         | -             | ✅        |
| `SMTP_PORT`            | SMTP server port             | `587`         | ❌        |
| `SMTP_USERNAME`        | SMTP username                | -             | ✅        |
| `SMTP_PASSWORD`        | SMTP password                | -             | ✅        |
| `CORS_ALLOWED_ORIGINS` | Comma-separated CORS origins | -             | ✅        |

### Security Configuration

The service includes several security features that can be configured:

- **Rate Limiting**: Configurable per-endpoint rate limits
- **Session Timeouts**: Configurable session expiration
- **Password Policies**: Minimum length, complexity requirements
- **Account Lockout**: Progressive lockout durations
- **CORS**: Configurable allowed origins

## 📚 API Documentation

### Base URL
```
http://localhost:8080/auth
```

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Response (201 Created):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "email_verified": false,
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "access_token": "jwt-token-here",
  "refresh_token": "refresh-token-here",
  "expires_in": 3600,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

### Email Verification

#### Send Verification Email
```http
POST /auth/email/send-verification
Content-Type: application/json

{
  "email": "<EMAIL>",
  "resend": false
}
```

**Response (200 OK):**
```json
{
  "message": "Verification email sent successfully",
  "rate_limit": {
    "attempts_remaining": 2,
    "reset_time": "2024-01-01T01:00:00Z",
    "window_duration_seconds": 3600
  }
}
```

#### Verify Email
```http
POST /auth/email/verify
Content-Type: application/json

{
  "token": "verification-token-from-email"
}
```

**Response (200 OK):**
```json
{
  "message": "Email verified successfully",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "email_verified": true
  }
}
```

### Multi-Factor Authentication (MFA)

#### Setup MFA
```http
POST /auth/mfa/setup
Authorization: Bearer <access_token>
```

**Response (200 OK):**
```json
{
  "secret": "base32-encoded-secret",
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "backup_codes": ["123456", "789012", "345678"]
}
```

#### Verify MFA Setup
```http
POST /auth/mfa/verify-setup
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "code": "123456"
}
```

#### MFA Login
```http
POST /auth/mfa/verify
Content-Type: application/json

{
  "user_id": "uuid",
  "code": "123456"
}
```

### Password Reset

#### Request Password Reset
```http
POST /auth/password/reset-request
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Reset Password
```http
POST /auth/password/reset
Content-Type: application/json

{
  "token": "reset-token-from-email",
  "new_password": "NewSecurePassword123!"
}
```

### Session Management

#### Get Current Session
```http
GET /auth/session
Authorization: Bearer <access_token>
```

#### Refresh Token
```http
POST /auth/session/refresh
Content-Type: application/json

{
  "refresh_token": "refresh-token-here"
}
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <access_token>
```

### Role-Based Access Control (RBAC)

#### Create Role
```http
POST /auth/roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "admin",
  "description": "Administrator role",
  "permissions": ["user:read", "user:write", "admin:access"]
}
```

#### Assign Role to User
```http
POST /auth/users/{user_id}/roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "role_id": "role-uuid"
}
```

#### Check User Permissions
```http
GET /auth/users/{user_id}/permissions
Authorization: Bearer <access_token>
```

## 🔐 Security Features

### Password Security
- **Argon2** hashing with salt
- **Password strength** validation
- **Password history** prevention
- **Secure password generation**

### Rate Limiting
- **Per-IP rate limiting** for authentication endpoints
- **Per-user rate limiting** for sensitive operations
- **Progressive backoff** for failed attempts
- **Configurable limits** per endpoint

### Session Security
- **Redis-backed sessions** for scalability
- **Device fingerprinting** and tracking
- **Concurrent session limits**
- **Session timeout** and cleanup
- **Secure session tokens**

### Account Protection
- **Account lockout** after failed attempts
- **Progressive lockout durations**
- **Suspicious activity detection**
- **IP-based blocking**
- **Admin unlock capabilities**

## 🧪 Testing

### Run Tests
```bash
# Run all tests
cargo test

# Run specific test suite
cargo test --test unit
cargo test --test integration

# Run with coverage
cargo test --coverage
```

### Test Categories
- **Unit Tests**: Model validation, business logic
- **Integration Tests**: API endpoints, database operations
- **Security Tests**: Authentication, authorization, rate limiting

## 🚀 Deployment

### Docker Deployment

#### 1. **Build Docker Image**:
```bash
docker build -t crabshield-auth-service .
```

#### 2. **Run with Docker Compose**:
```yaml
version: '3.8'
services:
  auth-service:
    image: crabshield-auth-service
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=******************************/crabshield_auth
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
```

### Production Considerations

1. **Environment Variables**: Use secure secret management
2. **Database**: Use connection pooling and read replicas
3. **Redis**: Use Redis Cluster for high availability
4. **Load Balancing**: Use multiple service instances
5. **Monitoring**: Implement health checks and metrics
6. **Logging**: Structured logging with correlation IDs

## 🔍 Monitoring & Health Checks

### Health Check Endpoint
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "version": "1.0.0"
}
```

### Metrics
The service exposes metrics for monitoring:
- Request counts and latencies
- Authentication success/failure rates
- Active sessions count
- Database connection pool status
- Redis connection status

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `DATABASE_URL` format
   - Verify PostgreSQL is running
   - Check network connectivity

2. **Redis Connection Failed**
   - Check `REDIS_URL` format
   - Verify Redis is running
   - Check Redis authentication

3. **Email Not Sending**
   - Verify SMTP configuration
   - Check SMTP credentials
   - Verify network access to SMTP server

4. **JWT Token Invalid**
   - Check `JWT_SECRET` configuration
   - Verify token hasn't expired
   - Check token format

### Logs
The service uses structured logging. Key log levels:
- **ERROR**: Critical errors requiring attention
- **WARN**: Warning conditions
- **INFO**: General information
- **DEBUG**: Detailed debugging information

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details

## 🔌 Client Integration

### JavaScript/TypeScript Client

```typescript
class AuthClient {
  private baseUrl: string;
  private accessToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async register(userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) {
    const response = await fetch(`${this.baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });
    return response.json();
  }

  async login(email: string, password: string) {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    if (response.ok) {
      const data = await response.json();
      this.accessToken = data.access_token;
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('refresh_token', data.refresh_token);
      return data;
    }
    throw new Error('Login failed');
  }

  async sendVerificationEmail(email: string, resend = false) {
    const response = await fetch(`${this.baseUrl}/auth/email/send-verification`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, resend })
    });
    return response.json();
  }

  async verifyEmail(token: string) {
    const response = await fetch(`${this.baseUrl}/auth/email/verify`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    });
    return response.json();
  }

  async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    const token = this.accessToken || localStorage.getItem('access_token');

    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 401) {
      // Token expired, try to refresh
      await this.refreshToken();
      // Retry the request
      return this.makeAuthenticatedRequest(url, options);
    }

    return response;
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) throw new Error('No refresh token available');

    const response = await fetch(`${this.baseUrl}/auth/session/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh_token: refreshToken })
    });

    if (response.ok) {
      const data = await response.json();
      this.accessToken = data.access_token;
      localStorage.setItem('access_token', data.access_token);
      return data;
    }

    // Refresh failed, redirect to login
    this.logout();
    throw new Error('Token refresh failed');
  }

  logout() {
    this.accessToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
}

// Usage example
const authClient = new AuthClient('http://localhost:8080');

// Register a new user
await authClient.register({
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  first_name: 'John',
  last_name: 'Doe'
});

// Send verification email
await authClient.sendVerificationEmail('<EMAIL>');

// Login
await authClient.login('<EMAIL>', 'SecurePassword123!');

// Make authenticated requests
const response = await authClient.makeAuthenticatedRequest('/api/protected-endpoint');
```

### React Hook Example

```typescript
import { useState, useEffect, createContext, useContext } from 'react';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const authClient = new AuthClient('http://localhost:8080');

  useEffect(() => {
    // Check for existing token on mount
    const token = localStorage.getItem('access_token');
    if (token) {
      // Validate token and get user info
      validateToken(token);
    } else {
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await authClient.login(email, password);
      setUser(response.user);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    authClient.logout();
    setUser(null);
  };

  const validateToken = async (token: string) => {
    try {
      const response = await authClient.makeAuthenticatedRequest('/auth/session');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: !!user,
      isLoading
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## 🔄 Error Handling

### Error Response Format

All API errors follow a consistent format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### Common Error Codes

| Code                 | HTTP Status | Description                 |
|----------------------|-------------|-----------------------------|
| `VALIDATION_ERROR`   | 400         | Request validation failed   |
| `UNAUTHORIZED`       | 401         | Authentication required     |
| `FORBIDDEN`          | 403         | Insufficient permissions    |
| `NOT_FOUND`          | 404         | Resource not found          |
| `RATE_LIMITED`       | 429         | Rate limit exceeded         |
| `INTERNAL_ERROR`     | 500         | Internal server error       |
| `EMAIL_NOT_VERIFIED` | 403         | Email verification required |
| `ACCOUNT_LOCKED`     | 423         | Account temporarily locked  |
| `MFA_REQUIRED`       | 428         | MFA verification required   |

### Client Error Handling

```typescript
async function handleApiCall<T>(apiCall: () => Promise<Response>): Promise<T> {
  try {
    const response = await apiCall();

    if (!response.ok) {
      const errorData = await response.json();

      switch (response.status) {
        case 401:
          // Redirect to login
          window.location.href = '/login';
          break;
        case 403:
          if (errorData.error.code === 'EMAIL_NOT_VERIFIED') {
            // Redirect to email verification
            window.location.href = '/verify-email';
          }
          break;
        case 429:
          // Show rate limit message
          showNotification('Too many requests. Please try again later.');
          break;
        default:
          showNotification(errorData.error.message);
      }

      throw new Error(errorData.error.message);
    }

    return response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}
```

## 📊 Performance & Scaling

### Performance Metrics

- **Response Time**: < 100ms for authentication endpoints
- **Throughput**: 1000+ requests/second per instance
- **Memory Usage**: ~50MB base memory footprint
- **Database Connections**: Configurable pool size (default: 10)

### Scaling Recommendations

1. **Horizontal Scaling**:
   - Deploy multiple service instances behind a load balancer
   - Use Redis for shared session storage
   - Implement sticky sessions if needed

2. **Database Optimization**:
   - Use read replicas for read-heavy operations
   - Implement connection pooling
   - Add database indexes for frequently queried fields

3. **Caching Strategy**:
   - Cache user permissions in Redis
   - Cache role hierarchies
   - Implement JWT token blacklisting

4. **Monitoring**:
   - Set up health checks
   - Monitor response times and error rates
   - Track authentication success/failure rates

## 🔒 Security Best Practices

### For Developers

1. **Token Storage**:
   - Store access tokens in memory (not localStorage)
   - Store refresh tokens in httpOnly cookies
   - Implement token rotation

2. **API Security**:
   - Always use HTTPS in production
   - Validate all input data
   - Implement proper CORS policies
   - Use rate limiting on all endpoints

3. **Password Security**:
   - Enforce strong password policies
   - Implement password strength meters
   - Never log passwords or tokens

4. **Session Management**:
   - Implement proper session timeouts
   - Invalidate sessions on logout
   - Monitor for concurrent sessions

### For Operations

1. **Infrastructure**:
   - Use TLS 1.3 for all connections
   - Implement network segmentation
   - Regular security updates

2. **Monitoring**:
   - Monitor failed authentication attempts
   - Set up alerts for suspicious activity
   - Log all security-relevant events

3. **Backup & Recovery**:
   - Regular database backups
   - Test recovery procedures
   - Implement disaster recovery plans

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
