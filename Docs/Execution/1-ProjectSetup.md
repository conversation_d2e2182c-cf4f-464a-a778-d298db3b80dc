Key Points
A local Docker network can be set up using Docker Compose to include observability tools (Grafana, Loki, Jaeger, Prometheus), data layer (Postgres, Redis), Apache APISIX, etcd, and GlitchTip.
The setup is designed for local development but can be adapted for VPS deployment.
Placeholder authentication using APISIX’s basic-auth plugin is recommended until the Rust auth service is ready.
Application services (auth-service, smtp-gateway, ai-ml-service, productivity-backend) can connect to this infrastructure using service names.
OpenTelemetry integration for application services will enable tracing with Jaeger.
Overview
This guide provides step-by-step instructions to set up a local Docker network for your monorepo, supporting observability, data storage, API gateway, and error tracking. The setup uses Docker Compose to orchestrate services, ensuring they communicate effectively within a single network. A placeholder authentication method is included, as the Rust auth service is still in development. The configuration is designed to be portable for future VPS deployment.

Prerequisites
Ensure your monorepo has the /infrastructure directory for configuration files.
Basic familiarity with Docker and YAML configuration.
Steps to Set Up the Docker Network
Create the Docker Compose File: Define all services in a docker-compose.yml file.
Configure APISIX: Set up the API gateway to connect with etcd.
Configure Observability: Set up Prometheus and Promtail for metrics and logs.
Run and Verify: Start the services and check their accessibility.
Set Up Placeholder Authentication: Use APISIX’s basic-auth plugin.
Prepare for Application Services: Provide guidance for integrating monorepo services.
Detailed Docker Network Setup Guide
This guide provides comprehensive instructions for setting up a local Docker network to support a monorepo with observability, data storage, API gateway, and error tracking components. The setup is designed for local development but is portable for future deployment to a VPS. It includes a placeholder authentication method using Apache APISIX’s basic-auth plugin, as the Rust auth service is still in development.

Prerequisites
Docker and Docker Compose: Ensure Docker and Docker Compose are installed.
Monorepo Structure: The /infrastructure directory in your monorepo should house configuration files.
Basic Knowledge: Familiarity with Docker, YAML, and basic networking concepts.
Step 1: Create the Docker Compose File
Create a docker-compose.yml file in the /infrastructure directory to define all services: observability (Grafana, Loki, Jaeger, Prometheus, cAdvisor, Promtail), data layer (Postgres, Redis), API gateway (Apache APISIX, etcd), and error tracking (GlitchTip with its own Postgres and Redis).

yaml

Collapse

Wrap

Copy
version: '3.8'

services:
  # etcd for APISIX configuration storage
  etcd:
    image: bitnami/etcd:3.5
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
    ports:
      - "2379:2379"

  # Apache APISIX API Gateway
  apisix:
    image: apache/apisix:3.11.0-debian
    depends_on:
      - etcd
    volumes:
      - ./apisix/config.yaml:/usr/local/apisix/conf/config.yaml:ro
    ports:
      - "9080:9080"
      - "9443:9443"

  # PostgreSQL for application services
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=myuser
      - POSTGRES_PASSWORD=mypassword
      - POSTGRES_DB=mydb
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Redis for application services
  redis:
    image: redis:6
    volumes:
      - redisdata:/data
    ports:
      - "6379:6379"

  # Grafana for visualization
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"

  # Loki for log aggregation
  loki:
    image: grafana/loki
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one
    ports:
      - "16686:16686"
      - "14268:14268"

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    ports:
      - "8080:8080"

  # Promtail for log collection
  promtail:
    image: grafana/promtail
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./promtail/config.yml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml

  # GlitchTip PostgreSQL
  glitchtip-postgres:
    image: postgres:12
    environment:
      - POSTGRES_USER=glitchtip
      - POSTGRES_PASSWORD=glitchtippassword
      - POSTGRES_DB=glitchtip
    volumes:
      - glitchtip-pgdata:/var/lib/postgresql/data

  # GlitchTip Redis
  glitchtip-redis:
    image: redis
    volumes:
      - glitchtip-redisdata:/data

  # GlitchTip Web
  glitchtip-web:
    image: glitchtip/glitchtip:latest
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************************************/glitchtip
      - SECRET_KEY=your-secret-here
      - PORT=8000
      - GLITCHTIP_DOMAIN=http://localhost:8000
      - EMAIL_URL=consolemail://
      - DEFAULT_FROM_EMAIL=admin@localhost
      - ENABLE_OPEN_USER_REGISTRATION=True
    depends_on:
      - glitchtip-postgres
      - glitchtip-redis

  # GlitchTip Worker
  glitchtip-worker:
    image: glitchtip/glitchtip:latest
    command: ./bin/run-celery-with-beat.sh
    environment:
      - DATABASE_URL=**************************************************************/glitchtip
      - SECRET_KEY=your-secret-here
    depends_on:
      - glitchtip-postgres
      - glitchtip-redis

  # GlitchTip Migrate
  glitchtip-migrate:
    image: glitchtip/glitchtip:latest
    command: ./manage.py migrate
    environment:
      - DATABASE_URL=**************************************************************/glitchtip
      - SECRET_KEY=your-secret-here
    depends_on:
      - glitchtip-postgres
      - glitchtip-redis

volumes:
  pgdata:
  redisdata:
  grafana-data:
  prometheus-data:
  loki-data:
  glitchtip-pgdata:
  glitchtip-redisdata:
Step 2: Configure Apache APISIX
Create a ./apisix/config.yaml file to configure APISIX to connect to etcd.

yaml

Collapse

Wrap

Copy
apisix:
  node_listen: 9080
  enable_admin: true

etcd:
  host:
    - "http://etcd:2379"
  prefix: "/apisix"
This configuration enables the APISIX Admin API and points to the etcd service within the Docker network.

Step 3: Configure Prometheus
Create a ./prometheus/prometheus.yml file to configure Prometheus to scrape metrics from itself and cAdvisor.

yaml

Collapse

Wrap

Copy
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
This setup collects metrics from Prometheus itself and container metrics via cAdvisor. You can extend this to include application services later.

Step 4: Configure Promtail
Create a ./promtail/config.yml file to configure Promtail to collect logs from Docker containers and send them to Loki.

yaml

Collapse

Wrap

Copy
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
- job_name: docker
  docker_sd_configs:
  - host: unix:///var/run/docker.sock
    refresh_interval: 5s
  relabel_configs:
  - source_labels: ['__meta_docker_container_name']
    target_label: 'container_name'
  - source_labels: ['__meta_docker_container_label_com_docker_compose_project']
    target_label: 'compose_project'
This configuration uses Docker service discovery to collect logs from all containers in the Compose project, labeling them with container names and project details.

Step 5: Run Docker Compose
Navigate to the /infrastructure directory and start the services:

bash

Collapse

Wrap

Run

Copy
docker-compose up -d
This command starts all services in detached mode. The services will communicate within the default Docker network created by Compose.

Step 6: Verify Services
Check that each service is running and accessible:


Service	URL/Port	Notes
Grafana	http://localhost:3000	Default credentials: admin/admin
Prometheus	http://localhost:9090	Metrics dashboard
Loki	http://localhost:3100	Log aggregation API
Jaeger	http://localhost:16686	Tracing UI
APISIX	http://localhost:9080	API Gateway; Admin API at /apisix/admin
GlitchTip	http://localhost:8000	Error tracking UI
Postgres	localhost:5432	Use myuser:mypassword for access
Redis	localhost:6379	No authentication by default
cAdvisor	http://localhost:8080	Container metrics
Access Grafana and configure data sources for Prometheus and Loki.
Verify Jaeger’s UI to ensure it’s ready to receive traces.
Check GlitchTip’s UI and note the DSN for application integration.
Step 7: Set Up Placeholder Authentication
Since the Rust auth service is in development, use APISIX’s basic-auth plugin as a placeholder. Configure it via the APISIX Admin API.

Obtain the Admin Key: Find the admin_key in ./apisix/config.yaml or use the default key provided in APISIX documentation .
Create a Consumer:
bash

Collapse

Wrap

Run

Copy
curl -i http://127.0.0.1:9080/apisix/admin/consumers \
-H 'X-API-KEY: your_admin_key' -X PUT -d '
{
  "username": "testuser",
  "plugins": {
    "basic-auth": {
      "username": "testuser",
      "password": "testpassword"
    }
  }
}'
Create a Route with Basic Authentication:
bash

Collapse

Wrap

Run

Copy
curl -i http://127.0.0.1:9080/apisix/admin/routes/1 \
-H 'X-API-KEY: your_admin_key' -X PUT -d '
{
  "uri": "/your_service/*",
  "upstream": {
    "type": "roundrobin",
    "nodes": {
      "your_service:80": 1
    }
  },
  "plugins": {
    "basic-auth": {}
  }
}'
Replace your_admin_key with the actual key and your_service:80 with the target service (e.g., auth-service:8080). This setup requires clients to provide testuser:testpassword in the Authorization header for routes under /your_service/*.

Step 8: Integrating Application Services
Your monorepo includes services (auth-service, smtp-gateway, ai-ml-service, productivity-backend) with their own Dockerfiles. To integrate them:

Add Services to Docker Compose: Update docker-compose.yml to include:
yaml

Collapse

Wrap

Copy
auth-service:
    build:
      context: ../services/auth-service
      dockerfile: Dockerfile
    depends_on:
      - postgres
      - redis
    ports:
      - "8081:8080" # Adjust port as needed

  smtp-gateway:
    build:
      context: ../services/smtp-gateway
      dockerfile: Dockerfile
    depends_on:
      - apisix
    ports:
      - "8082:8080" # Adjust port as needed

  ai-ml-service:
    build:
      context: ../services/ai-ml-service
      dockerfile: Dockerfile
    depends_on:
      - postgres
      - redis
    ports:
      - "8083:8080" # Adjust port as needed

  productivity-backend:
    build:
      context: ../services/productivity-backend
      dockerfile: Dockerfile
    depends_on:
      - postgres
      - redis
    ports:
      - "8084:8080" # Adjust port as needed
Connect to Infrastructure:
Postgres: Use postgres:5432 with credentials myuser:mypassword.
Redis: Use redis:6379.
APISIX: Route traffic through apisix:9080.
GlitchTip: Configure applications to send errors to GlitchTip’s DSN (available in the GlitchTip UI).
Jaeger: Send OpenTelemetry traces to jaeger:14268.
OpenTelemetry Integration: Instrument your services (Rust and Python) with OpenTelemetry SDKs to send traces to Jaeger. Refer to OpenTelemetry Documentation for setup.
Step 9: Configure Observability for Applications
Metrics: Expose Prometheus-compatible endpoints in your services (e.g., /metrics). Update prometheus.yml to scrape these endpoints:
yaml

Collapse

Wrap

Copy
- job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:8080']
Logs: Ensure services log to stdout/stderr, which Promtail will collect automatically via Docker service discovery.
Traces: Configure OpenTelemetry to send traces to jaeger:14268.
Step 10: Prepare for VPS Deployment
Secure Credentials: Replace default passwords and secrets (e.g., your-secret-here in GlitchTip, Postgres credentials).
Networking: Ensure ports are exposed only as needed on the VPS.
Persistence: Verify volume mounts (pgdata, glitchtip-pgdata, etc.) are backed up or mapped to persistent storage.
Authentication: Replace basic-auth with the Rust auth service when ready, integrating it via APISIX plugins (e.g., jwt-auth).
Checklist
 Create docker-compose.yml with all infrastructure services.
 Create ./apisix/config.yaml for APISIX-etcd integration.
 Create ./prometheus/prometheus.yml for metrics collection.
 Create ./promtail/config.yml for log collection.
 Run docker-compose up -d in /infrastructure.
 Verify all services are accessible (Grafana, Prometheus, Loki, Jaeger, APISIX, GlitchTip).
 Set up APISIX basic-auth for placeholder authentication.
 Add application services to docker-compose.yml when ready.
 Configure application services to connect to Postgres, Redis, APISIX, and GlitchTip.
 Instrument applications with OpenTelemetry for Jaeger tracing.
 Update Prometheus to scrape application metrics.
 Secure credentials and volumes for VPS deployment.
 Replace basic-auth with Rust auth service when available.
Key Citations
Apache APISIX Docker Deployment Guide
Grafana Loki Docker Installation
GlitchTip Installation Documentation
Promtail Docker Container Logging Gist
GlitchTip Docker Compose with PostgreSQL
APISIX Authentication Plugins Overview
OpenTelemetry Official Documentation