# Server Setup

## Monorepo Structure

The monorepo will be organized to house all first-party Rust services (Crab Shield, Crab Dispatch, Crab Stash, Crab Hub)
as a single Rust workspace, with clear separation of concerns. Third-party services will be managed via configuration
and Docker Compose. The structure supports maintainability and atomic commits, with the authentication service copied
into the repo.

   ```text
   /crabstack
   ├── /crates
   │   ├── /crab-shield
   │   │   ├── /src
   │   │   │   ├── /api            # Actix Web routes and handlers
   │   │   │   ├── /auth           # Authentication logic (JWT, OAuth2, etc.)
   │   │   │   ├── /models         # Data models and database schemas
   │   │   │   ├── /middleware     # Actix middleware for auth
   │   │   │   └── main.rs         # Service entry point
   │   │   ├── Cargo.toml          # Crate dependencies
   │   │   └── README.md
   │   ├── /crab-dispatch
   │   │   ├── /src
   │   │   │   ├── /api            # SMTP and email management endpoints
   │   │   │   ├── /email          # Email processing (parsing, AI/ML spam filtering)
   │   │   │   ├── /smtp           # SMTP server and relay logic
   │   │   │   ├── /storage        # Metadata and attachment handling
   │   │   │   ├── /models         # Email and account models
   │   │   │   └── main.rs         # Service entry point
   │   │   ├── Cargo.toml
   │   │   └── README.md
   │   ├── /crab-stash
   │   │   ├── /src
   │   │   │   ├── /api            # S3-compatible API endpoints
   │   │   │   ├── /storage        # Object storage logic (Contabo integration)
   │   │   │   ├── /models         # Bucket and object models
   │   │   │   └── main.rs         # Service entry point
   │   │   ├── Cargo.toml
   │   │   └── README.md
   │   ├── /crab-hub
   │   │   ├── /src
   │   │   │   ├── /api            # Database and Redis management endpoints
   │   │   │   ├── /postgres       # Postgres schema and connection management
   │   │   │   ├── /redis          # Redis connection and resource allocation
   │   │   │   ├── /models         # Project and resource models
   │   │   │   └── main.rs         # Service entry point
   │   │   ├── Cargo.toml
   │   │   └── README.md
   │   └── /common                 # Shared utilities (e.g., logging, config)
   │       ├── /src
   │       │   ├── /config.rs
   │       │   ├── /logging.rs
   │       │   └── /errors.rs
   │       ├── Cargo.toml
   │       └── README.md
   ├── /config
   │   ├── docker-compose.yml       # Third-party services (Postgres, Redis, SigNoz, etc.)
   │   └── /signoz                  # SigNoz configuration
   │       └── otel-collector.yaml  # Open Telemetry Collector configuration
   │       └── clickhouse.yaml      # ClickHouse configuration
   ├── /scripts
   │   ├── deploy.sh              # Deployment script for VPS
   │   ├── setup.sh               # Initial VPS setup (install Rust, Docker, etc.)
   │   └── backup.sh              # Backup script for data
   ├── /docs
   │   ├── architecture.md        # System architecture
   │   └── goals.md               # Project goals
   ├── Cargo.toml                 # Workspace root
   ├── Dockerfile                 # Docker image for Rust services
   ├── README.md
   └── rust-toolchain.toml        # Rust toolchain version
   ```

* **Workspace**: The root Cargo.toml defines a Rust workspace including all crates (crab-shield, crab-dispatch,
  crab-stash, crab-hub, common).
* **Crab Shield**: Copied from your existing auth service, organized for Actix Web with modular auth logic.
* **Crab Dispatch**: Structured for email hosting and SMTP relay, with AI/ML spam filtering logic separated.
* **Crab Stash**: Implements an S3-compatible API for Contabo's 250GB object storage.
* **Crab Hub**: Manages Postgres and Redis, providing APIs for project-based resource allocation.
* **Common Crate**: Shared utilities to reduce code duplication.
* **Config**: Docker Compose for third-party services and their configurations.
* **Scripts**: Automation for deployment, setup, and backups.
* **Dockerfile**: Single Dockerfile for all Rust services, with build arguments to select the crate.

## Service Management and Project Isolation

To manage services and create projects while using a single Postgres and Redis instance, we'll use logical separation
via schemas, namespaces, and configuration-driven access control. The Next.js dashboard on Vercel will interact with
`crab-hub` and `crab-shield` APIs to manage projects.

### Project Creation Workflow

#### 1. API Request: The Next.js dashboard sends a POST request to `crab-hub` API (
`/projects`) with project details (name, required resources).

#### 2. Authentication: `crab-shield` verifies the request using JWT or API key, ensuring only you can create projects.

#### 3. Project Setup:

* Database: `crab-hub` creates a new Postgres schema (e.g., `project_<uuid>`) for the project. Row-level security (RLS)
  policies are applied to restrict access to the project's schema.
* Redis: `crab-hub` assigns a Redis namespace (e.g., `project:<uuid>:*`) for keys, enforced via key prefixing in the
  API.
* Object Storage: `crab-stash` creates a bucket (e.g., `project-<uuid>`) with access restricted via client ID and
  secret.
* Auth: `crab-shield` generates a client ID and secret for the project, stored in Postgres (in a management schema,
  e.g., `admin.projects`).
* Email: `crab-dispatch` creates email accounts (e.g., `<project>-<account>@domain.com`) and assigns storage quotas.

#### 4. Response: `crab-hub` returns project details (connection strings, client ID, secret) to the dashboard.

#### 5. Observability: `crab-hub` configures Prometheus and Loki to filter metrics and logs by project ID.

### Postgres Management

* Single Instance: Use one Postgres instance with multiple schemas for isolation.
* Schema Creation: For each project, create a schema (`project_<uuid>`) using:

```sql
CREATE SCHEMA project_<uuid>;
GRANT
USAGE
ON
SCHEMA
project_<uuid> TO <project_role>;
```

**RLS**: Enable row-level security on tables to restrict access based on the project role.
**Logical Replication**: Configure replication per schema if enabled:

```sql
CREATE
PUBLICATION project_<uuid>_pub FOR ALL TABLES IN SCHEMA project_<uuid>;
```

* **Real-Time Streaming**: Use Postgres logical decoding with a WebSocket server (in crab-hub) to stream changes for
  schemas with streaming enabled.
* **Roles**: Create a Postgres role per project (project_<uuid>_role) with restricted permissions:

```sql
CREATE ROLE project_<uuid>_role WITH LOGIN PASSWORD '<secret>';
GRANT
CONNECT
ON DATABASE dev TO project_<uuid>_role;
GRANT USAGE ON SCHEMA
project_<uuid> TO project_<uuid>_role;
```

* **Connection Strings**: Provide project-specific connection strings (e.g.,
  `postgresql://project_<uuid>_role:<secret>@vps:5432/dev?schema=project_<uuid>`).

### Redis Management

* **Namespace Isolation**: Use key prefixes (e.g., `project:<uuid>:cache:<key>`) to logically separate project data.
* **Resource Limits**: Implement rate-limiting and quotas in `crab-hub` using Redis commands like `SETEX` and `INCR` to
  track usage.
* **API Access**: crab-hub exposes endpoints (e.g., `/redis/<project_id>/set`) that enforce namespace restrictions and
  authenticate via `crab-shield`.
* **Configuration**: Store project Redis quotas (e.g., max keys, memory) in Postgres (`admin.projects`) and enforce via
  `crab-hub`.

### Dashboard Integration (Future Integration)

* **Project Creation**: A form in the Next.js app collects project name and resource requirements, sending them to
  `crab-hub`.
* **Resource Management**: Display project details (connection strings, client IDs, quotas) and allow updates (e.g.,
  enable/disable replication).
* **Monitoring:** Embed Grafana dashboards with project-specific filters (e.g., `project_id=<uuid>`) using Grafana's
  iframe support.
* **Logs and Errors:** Filter Loki logs and GlitchTip errors by project ID in the dashboard.

## 3. Object Storage Sharing

The 250GB Contabo object storage will be shared across projects using `crab-stash`, which provides an S3-compatible API.

### Implementation

* **Bucket Creation**: Each project gets a dedicated bucket (`project-<uuid>`), created via `crab-stash` API:

```bash
curl -X POST /buckets -H "Authorization: Bearer <token>" -d '{"project_id": "<uuid>"}'
```

* **Access Control**: Use `crab-shield` to issue project-specific AWS-style access keys (access key ID and secret)
  stored in Postgres.
* **Quota Management**: Track storage usage per bucket using Contabo's API or manual tracking in `crab-stash`. Store
  quotas in Postgres (`admin.projects`).
* **API Endpoints**: `crab-stash` exposes S3-compatible endpoints (e.g.,
  `PUT /<bucket>/<object>, GET /<bucket>/<object>`), authenticated via `crab-shield`.
* **Attachment Storage**: For `crab-dispatch`, store email attachments in the project's bucket under a prefix (e.g.,
  `project-<uuid>/email/attachments/`).

### Configuration

* **Contabo Integration**: Configure `crab-stash` with Contabo credentials (stored securely in environment variables or
  a secrets manager).
* **Rate Limiting**: Use Redis (via `crab-hub`) to rate-limit API calls per project to prevent abuse.
* **Monitoring**: Export storage usage metrics to Prometheus (e.g., storage_bytes{project_id="<uuid>"}) and visualize in
  Grafana.

### Dashboard Features

* **Bucket Management**: Create, view, and delete buckets for projects.
* **Usage Tracking**: Display storage usage per project and enforce quotas (e.g., 10GB per project).
* **Access Keys**: Generate and rotate access keys for S3-compatible clients.

## 4. Mail Service Structure

`crab-dispatch` will handle email hosting and SMTP relay, with a modular structure to support AI/ML features and Contabo
object storage integration.

### Directory Structure

```text
   /crates/crab-dispatch
   ├── /src
   │   ├── /api
   │   │   ├── /v1
   │   │   │   ├── accounts.rs    # Endpoints for managing email accounts
   │   │   │   ├── logs.rs        # Endpoints for email logs and stats
   │   │   │   └── mod.rs
   │   │   └── mod.rs
   │   ├── /email
   │   │   ├── /ai               # AI/ML spam filtering and summarization
   │   │   │   ├── spam.rs
   │   │   │   ├── summarize.rs
   │   │   │   └── mod.rs
   │   │   ├── parser.rs         # Email parsing (MIME, headers)
   │   │   ├── processor.rs      # Email processing pipeline
   │   │   └── mod.rs
   │   ├── /smtp
   │   │   ├── server.rs         # SMTP server implementation
   │   │   ├── relay.rs          # SMTP relay for apps
   │   │   └── mod.rs
   │   ├── /storage
   │   │   ├── postgres.rs       # Metadata and message body storage
   │   │   ├── s3.rs             # Attachment storage in Contabo
   │   │   └── mod.rs
   │   ├── /models
   │   │   ├── account.rs        # Email account model
   │   │   ├── message.rs        # Email message model
   │   │   └── mod.rs
   │   └── main.rs               # Service entry point
   ├── Cargo.toml
   └── README.md
```

### Components

* **API**: Actix Web endpoints for managing accounts, viewing logs, and stats, accessible via the Next.js dashboard.
* **Email Processing**:
    * **Parser**: Parse MIME emails using a library like `mailparse`.
    * **AI/ML**: Implement spam filtering using a lightweight Rust ML library (e.g., `smartcore`) and summarization
      using a pre-trained model (e.g., via `rust-bert`).
    * **Processor**: Pipeline to classify, store, and route emails.

* **SMTP**:
    * **Server**: Handle incoming emails using a Rust SMTP library (e.g., `lettre`).
    * **Relay**: Provide an SMTP relay for apps, authenticated via `crab-shield` client ID/secret.

* **Storage**:
    * **Postgres**: Store email metadata (sender, recipient, subject) and message bodies in a project-specific schema.
    * **S3**: Store attachments in Contabo object storage under `project-<uuid>/email/attachments/`.


* **Models**: Define structs for accounts and messages, serialized for API and storage.

### Configuration

* **Accounts**: Store email accounts in Postgres (`project_<uuid>.email_accounts`) with fields for username, password
  hash, and quotas.
* **Quotas**: Enforce storage (e.g., 1GB per inbox) and email rate limits (e.g., 2500/day) using Redis.
* **AI/ML**: Configure models in `/email/ai` with pre-trained weights stored in object storage.
* **SMTP Relay**: Support third-party relays (e.g., Postmark) for production via environment variables.

### Dashboard Integration

* **Account Management**: Create and delete email accounts (e.g., `<EMAIL>`) with password and quota
  settings.
* **Logs and Stats**: Display email logs (filtered by project) and stats (e.g., sent/received counts) using Loki data.
* **SMTP Relay**: Provide relay credentials (host, port, username, password) for app integration.

### Notes

* **Licensing**: Since you're concerned about Stalwart's copyleft license, crab-dispatch will be a custom
  implementation, avoiding any licensing conflicts.
* **Multi-Tenancy**: Achieved via project-specific schemas and buckets, without requiring an enterprise license.
* **Performance**: Optimized for 15-25 inboxes and 2500 emails/day, with lightweight AI/ML models to fit within VPS
  constraints.
* sqlx Notes: 
  * **Setup**: Add `sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono"] }` to `Cargo.toml` for `Postgres`, `UUID`, and `timestamp` support.
  * **Connection Pooling**: Use `sqlx::Pool` for shared database connections, initialized in `main.rs` and passed to Actix Web’s app data.
  * **Schema Integration**: Store `Crab-Shield` data in a `crab_shield` schema (e.g., `users`, `social_logins`, `otp_codes tables`), setting `search_path` dynamically for queries if needed.
  * **Migrations**: Use `sqlx migrate` or a custom script in `/scripts` to manage schema changes, aligning with your `setup.sh` and `deploy.sh` scripts.
* **Example**: 
```rust
use sqlx::{Pool, Postgres};
use sqlx::prelude::*;

#[derive(sqlx::FromRow)]
struct User {
    id: uuid::Uuid,
    email: String,
    password_hash: String,
    created_at: chrono::DateTime<chrono::Utc>,
}

async fn find_user_by_email(pool: &Pool<Postgres>, email: &str) -> sqlx::Result<Option<User>> {
    sqlx::query_as!(User, "SELECT * FROM crab_shield.users WHERE email = $1", email)
        .fetch_optional(pool)
        .await
}
```
