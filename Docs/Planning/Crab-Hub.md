# Crab Hub Service Implementation Details
**Introduction**
Crab Hub is a core component of the Crab services suite, designed to provide managed database and caching resources for multiple projects in a development environment. It leverages Postgres for structured data storage and Redis for caching, queueing, rate-limiting, and session management. Each project is allocated an isolated Postgres schema and a dedicated Redis database number, ensuring data separation and security. Projects are identified by unique IDs and secrets, which are verified through the crab-shield authentication service. This guide outlines the steps to develop Crab Hub, mirroring the depth and structure of the Crab-Dispatch guide, to support your applications and internal tools.

### API Endpoints
Crab Hub uses Actix Web to expose RESTful API endpoints for managing projects, primarily accessed through a Next.js dashboard. The key endpoints are:

| Endpoint         | Method | Description                                                 | Authentication                     |
|:-----------------|:-------|:------------------------------------------------------------|:-----------------------------------|
| `/projects`      | POST   | Creates a new project, generating credentials and resources | Admin token                        |
| `/projects/{id}` | DELETE | Deletes a specified project and its resources               | Admin token                        |
| `/projects/{id}` | PUT    | Updates project settings or configurations                  | Admin token or project credentials |
| `/projects/{id}` | GET    | Retrieves project details, including connection information | Admin token or project credentials |
These endpoints are secured, requiring authentication via tokens or credentials verified by crab-shield.

### Database Management
Crab Hub manages a shared Postgres database, creating isolated schemas for each project to ensure data separation. The process includes:
  * **User Creation**: A new Postgres user is created with a securely generated password for each project.
  * **Schema Creation**: A dedicated schema is created, owned by the project’s user, to store its data.
  * **Permission Management**: The project user is granted full privileges on its schema, with access restricted to only that schema. The user’s search_path is set to its schema for convenience (e.g., ALTER USER project_user SET search_path TO project_schema;).

Applications connect directly to Postgres using the provided credentials (host, port, database name, username, password), accessing only their designated schema. (Via Client ID and secret)

### Redis Management
Crab Hub assigns a unique Redis database number (e.g., 0 to 15, as supported by Redis) to each project within a shared Redis instance. This logical separation allows projects to use Redis for caching, queueing, or other purposes without data overlap. Crab Hub tracks allocated database numbers to prevent conflicts, storing this information in its own Postgres schema.

### Integration with Crab-Shield
Crab Hub integrates with crab-shield for secure authentication and authorization:
  * **Project Creation**: When creating a project, Crab Hub sends a request to crab-shield’s API to generate a client ID and secret, which are used to identify and authenticate the project.
  * **Credential Verification:** For API requests requiring project-specific access, Crab Hub verifies the provided client ID and secret by querying crab-shield’s verification endpoint (e.g., a hypothetical POST /verify endpoint).
  * **Admin Authentication**: Dashboard requests to manage projects use an admin token, also verified through crab-shield.

This approach ensures that Crab Hub does not store sensitive secrets, relying on crab-shield for credential management.

### Data Models
The primary data model is the Project struct, which captures essential project information. It is stored in a dedicated crab_hub schema in Postgres, in a projects table. The struct includes:


| Field         | Type     | Description                                   |
|:--------------|:---------|:----------------------------------------------|
| `id`          | `String` | Unique identifier for the project             |
| `name`        | `String` | Human-readable project name                   |
| `client_id`   | `String` | Client ID generated by crab-shield            |
| `pg_username` | `String` | Postgres username for the project             |
| `schema_name` | `String` | Name of the Postgres schema                   |
| `redis_db`    | `i32`    | Redis database number assigned to the project |

The Project struct is serialized using the serde crate for API responses and database interactions. Notably, the Postgres password is generated during project creation, returned to the dashboard, but not stored in the database for security.

### Security Considerations
Security is a priority in Crab Hub’s design:
* **Data Isolation**: Each project’s data is isolated using separate Postgres schemas and Redis database numbers, preventing cross-project access.
* **Credential Management**: Project secrets are managed by crab-shield, and Crab Hub only stores the client ID, reducing the risk of secret exposure.
* **Authentication**: API endpoints require valid tokens or credentials, verified through crab-shield, ensuring only authorized access.
* **Postgres Permissions**: Project users are restricted to their own schemas, with no access to other schemas or administrative functions.
* **Network Security**: The service should be deployed behind a reverse proxy (e.g., Nginx) with HTTPS to secure API communication, especially since the dashboard connects remotely via Vercel.

### Deployment
Crab Hub is containerized using Docker, with its image built from the Rust codebase, as specified in your project’s `Dockerfile`. It is deployed alongside Postgres and Redis, managed by `docker-compose.yml`, on your Contabo VPS. The deployment scripts (`deploy.sh` and `setup.sh`) handle installation and configuration, ensuring compatibility with your existing setup.

### Implementation Steps
#### 1. Project Setup
  * Create a new Rust crate named `crab-hub` in your workspace’s root `Cargo.toml`.
  * Add dependencies in `crab-hub/Cargo.toml`:
    * `actix-web` for the API server
    * `sqlx` for Postgres interactions
    * `redis` for Redis connections
    * `reqwest` for HTTP requests to crab-shield
    * `serde` for serialization
    * `uuid` for generating unique IDs
    * `rand` for generating secure passwords
#### Define Data Models
* Implement the `Project` struct in Rust, deriving `Serialize` and `Deserialize` for API and database use.
* Example:
rust

Collapse

Wrap

Copy
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize, sqlx::FromRow)]
struct Project {
id: String,
name: String,
client_id: String,
pg_username: String,
schema_name: String,
redis_db: i32,
}
API Endpoints Implementation
Set up an Actix Web server with routes for the listed endpoints.
Implement middleware to verify authentication tokens or credentials by calling crab-shield’s verification API.
Example for POST /projects:
Generate a unique project ID using uuid.
Request a client ID and secret from crab-shield.
Create a Postgres user and schema.
Assign a Redis database number.
Store project details in the crab_hub.projects table.
Return connection details (Postgres credentials, Redis details) to the dashboard.
Database Interactions
Use sqlx to connect to Postgres with administrative credentials (e.g., a crab_hub user).
Implement functions to:
Create a user: CREATE USER project_user WITH PASSWORD 'random_password';
Create a schema: CREATE SCHEMA project_schema AUTHORIZATION project_user;
Set search path: ALTER USER project_user SET search_path TO project_schema;
Drop resources for deletion: DROP SCHEMA project_schema CASCADE; DROP USER project_user;
Redis Allocation
Maintain a list of allocated Redis database numbers in the crab_hub schema to ensure uniqueness.
Assign an unused number (e.g., 0 to 15) to each project and store it in the Project record.
Integration with Crab-Shield
Use reqwest to call crab-shield’s API (e.g., POST /clients for creating a client, POST /verify for credential verification).
Example verification flow:
Receive client ID and secret in an API request.
Send a request to crab-shield’s verification endpoint.
Proceed only if crab-shield confirms validity.
Store Project Details
Create a crab_hub schema in Postgres with a projects table to store Project records.
Use sqlx to insert and query project data, ensuring the Postgres password is not stored.
Error Handling and Logging
Implement robust error handling for API requests, database operations, and crab-shield interactions.
Integrate with Loki for logging and Prometheus for metrics, aligning with your observability stack.
Additional Considerations
Advanced Features: Support for logical replication or real-time data streaming (e.g., via Postgres LISTEN/NOTIFY) can be added by configuring publications or notifications per project schema, though these may be deferred for initial implementation.
Backup and Restore: Plan for schema-level backups using tools like pg_dump for each project’s schema, integrated with your backup.sh script.
Monitoring: Use Prometheus and Grafana to track resource usage (e.g., number of schemas, Redis database allocations) and API performance.
Scalability: While not critical for your development server, ensure the design allows adding more projects without significant overhead.
Redis Security: Consider using Redis ACLs for stronger isolation if needed, though a shared password or network-level security may suffice for development.
Example Workflow
When the dashboard creates a project:

The dashboard authenticates with crab-shield using admin credentials, receiving a token.
It calls POST /projects with the token and project details (e.g., name).
Crab Hub verifies the token with crab-shield.
Crab Hub requests a client ID and secret from crab-shield.
It creates a Postgres user, schema, and assigns a Redis database number.
Project details are stored in the crab_hub.projects table.
Connection details (Postgres host, port, username, password, schema; Redis host, port, database number) are returned to the dashboard.
You use these details to configure your applications, connecting directly to Postgres and Redis.
This guide provides a comprehensive plan to develop Crab Hub, ensuring it meets your needs for managing project resources securely and efficiently. It aligns with your existing Rust-based, Docker-deployed architecture and integrates seamlessly with crab-shield and your dashboard.