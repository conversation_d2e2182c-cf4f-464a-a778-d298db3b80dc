# Crab-Dispatch Service Implementation Details
This document provides a comprehensive breakdown of the crab-dispatch service, a core component of the Crab services suite, responsible for email hosting, SMTP relay, and AI/ML-driven email processing. Below, we address the requested details for API endpoints, email processing libraries, protocol support, SMTP and relay logic, storage mechanisms, data models, security requirements, and additional considerations.
## 1. API Endpoints
   The crab-dispatch service uses Actix Web to provide RESTful API endpoints for administrative tasks, accessible via a Next.js dashboard. These endpoints manage email accounts, logs, and statistics, operating within project-specific contexts to support multi-tenancy. The following endpoints are proposed based on the implementation plan’s structure (/crates/crab-dispatch/src/api/v1/accounts.rs and logs.rs):



| Endpoint                        | Method | Description                                                                                            |
|---------------------------------|--------|--------------------------------------------------------------------------------------------------------|
| `/api/v1/accounts`              | POST   | Create a new email account (e.g., <EMAIL>) with username, password, and quota settings. |
| `/api/v1/accounts/{account_id}` | DELETE | Delete an email account by ID.                                                                         |
| `/api/v1/accounts/{account_id}` | PUT    | Update account details, such as password or storage quota.                                             |
| `/api/v1/accounts/{account_id}` | GET    | Retrieve details of a specific email account.                                                          |
| `/api/v1/accounts`              | GET    | List all email accounts for a project, with pagination support.                                        |
| `/api/v1/logs`                  | GET    | Retrieve email logs, filterable by date, sender, recipient, etc.                                       |
| `/api/v1/stats`                 | GET    | Fetch statistics, such as sent/received email counts and storage usage.                                |


These endpoints assume authentication via the dashboard, which likely provides project context to ensure data isolation across tenants.
## 2. Email Processing Libraries and Protocol Support
   Email Processing
   The `crab-dispatch` service handles spam analysis, threat detection, email classification, and email summarization, with processing occurring on the server for optimal performance. The following Rust crates and AI/ML models are recommended:

### Email Parsing:

* [`mailparse`](https://crates.io/crates/mailparse): Parses MIME emails to extract headers, body, and attachments, as specified in the plan (`/src/email/parser.rs`).

### Spam Analysis:

* [`smartcore`](https://crates.io/crates/smartcore) or [`linfa`](https://crates.io/crates/linfa): Lightweight machine learning libraries for training classifiers (e.g., Naive Bayes, logistic regression) to detect spam. These are suitable for the plan’s goal of using lightweight ML models.
* Alternatively, [`rust-bert`](https://crates.io/crates/rust-bert) can be used for transformer-based models if higher accuracy is needed, though it’s more resource-intensive.

### Threat Detection:

[`clamav-client`](https://crates.io/crates/clamav-client): Integrates with a ClamAV daemon to scan emails and attachments for malware, providing robust threat detection.

### Email Classification:

Use `smartcore` or `linfa` to categorize emails (e.g., into “Promotions,” “Social”) for enhanced client filtering, similar to spam analysis.

### Email Summaries:

`rust-bert`: Leverages pre-trained transformer models for text summarization, generating concise summaries displayed below the subject line in the client.



Server-side processing is recommended due to the computational demands of AI/ML tasks, especially summarization, aligning with the plan’s architecture (`/src/email/ai`).
Protocol Support
The service must support SMTP, IMAP, JMAP, and potentially other email protocols. Here’s a detailed breakdown:

SMTP:

Server: Use mailin to implement an SMTP server for receiving emails, as lettre is primarily for sending.
Sending: Use lettre for sending emails during relay or from local accounts.


IMAP:

The IMAPServer crate is a work-in-progress IMAP server implementation in Rust. It can serve as a starting point, though further development may be needed.
Alternatively, use imap-codec for low-level IMAP protocol handling, building custom server logic around it.


JMAP:

No mature JMAP server crate exists in Rust. The stalwartlabs/jmap-client is client-focused, and Stalwart JMAP server is AGPLv3-licensed, which conflicts with the plan’s licensing concerns.
Implementing JMAP requires building a server based on the JMAP specification, potentially using jmap-rs for parsing and generating JMAP data structures.



Given the complexity of IMAP and JMAP server implementations, consider prioritizing SMTP and API-based email access initially, adding IMAP/JMAP support in future iterations.
3. SMTP and Relay Logic
   The SMTP component handles incoming emails and provides a relay for authenticated apps. The structure and crates are as follows:

Crates:

mailin: Implements the SMTP server for receiving emails.
lettre: Sends emails for relay or local account sending.
mailparse: Parses incoming emails for processing.


Logic:

SMTP Server (/src/smtp/server.rs):
Set up mailin to listen for incoming SMTP connections.
For unauthenticated connections, accept emails only if the recipient is a local account (verified via the email_accounts table in the project’s Postgres schema).
Process accepted emails by parsing with mailparse, storing metadata and body in Postgres, and attachments in S3.


SMTP Relay (/src/smtp/relay.rs):
Accept connections from authenticated clients using crab-shield client ID and secret.
Verify credentials by calling a crab-shield API endpoint (e.g., /verify_credentials).
For authenticated clients, accept emails and use lettre to forward them to the recipient’s mail server.
Log relay events in the project’s schema for auditing.




Structure:

Implement mailin callbacks for SMTP commands (e.g., MAIL FROM, RCPT TO, DATA).
In the authentication callback, verify credentials with crab-shield.
In the data callback, determine if the email is for local delivery (store in Postgres/S3) or relay (forward via lettre).



This approach ensures secure and efficient email handling, with authentication integrated via crab-shield.
4. Storage Logic
   The storage system uses Postgres for email metadata and bodies and Contabo’s S3-compatible storage for attachments, supporting multi-tenancy through project-specific schemas and buckets.

Postgres (/src/storage/postgres.rs):

Each project has a dedicated schema (e.g., project_<uuid>).
Tables include:
email_accounts: Stores account details (account_id, username, password_hash, quota, created_at).
messages: Stores email metadata (message_id, account_id, sender, recipient, subject, date, body).
attachments: Stores attachment metadata (attachment_id, message_id, s3_key, filename, content_type, size).


Use sqlx or diesel for database interactions.
Set the Postgres search path to the project’s schema before queries to ensure data isolation.


S3 (/src/storage/s3.rs):

Store attachments in Contabo object storage under project-<uuid>/email/attachments/<message_id>/<attachment_id>.
Use rusoto_s3 or aws-sdk-s3 for S3 interactions.
Upload attachments during email processing and store the S3 key in the attachments table.


Processing Flow:

Parse incoming email with mailparse.
Identify the project based on the recipient’s account.
Store metadata and body in the project’s messages table.
Upload attachments to S3 and store keys in the attachments table.
For relayed emails, log events in the project’s schema without storing the email unless required.



5. Data Models
   The data models, defined in /src/models, represent email accounts, messages, and attachments, serialized for API responses and database storage. Suggested Rust structs include:
   use serde::{Deserialize, Serialize};
   use uuid::Uuid;
   use chrono::{DateTime, Utc};

#[derive(Serialize, Deserialize)]
struct EmailAccount {
id: Uuid,
username: String,
password_hash: String,
quota: i64, // Storage quota in bytes
created_at: DateTime<Utc>,
}

#[derive(Serialize, Deserialize)]
struct Message {
id: Uuid,
account_id: Uuid,
sender: String,
recipient: String,
subject: String,
date: DateTime<Utc>,
body: String, // Or separate text/html fields
}

#[derive(Serialize, Deserialize)]
struct Attachment {
id: Uuid,
message_id: Uuid,
filename: String,
content_type: String,
s3_key: String,
size: i64,
}

These structs use serde for JSON serialization and include fields aligned with the storage schema.
6. Additional Concerns and Suggestions

IMAP/JMAP Complexity: Implementing IMAP and JMAP servers is resource-intensive due to limited mature Rust crates. Consider focusing on SMTP and API-based access initially, adding IMAP/JMAP later or exploring integration with existing solutions if licensing permits.
AI/ML Performance: Transformer models in rust-bert for summarization are computationally heavy. Optimize by using distilled models or ensure sufficient server resources (e.g., VPS with adequate CPU/GPU).
Data Isolation: Strictly enforce project-specific schemas in Postgres and S3 buckets to prevent data leakage between tenants. Validate schema selection in all database operations.
Security: Beyond crab-shield authentication, implement encryption for sensitive data (e.g., email bodies in Postgres) and secure S3 access with proper credentials.
Scalability: The plan targets 15-25 inboxes and 2500 emails/day. Monitor performance under load and consider Redis for rate limiting and quotas, as mentioned in the plan.

7. Crab Shield Security Requirements
   The crab-shield service must provide:

Credential Verification API: An endpoint (e.g., /verify_credentials) to validate client ID and secret for SMTP relay authentication.
Potential Features: Depending on its scope, crab-shield could offer rate limiting, abuse detection, or API authentication for the dashboard, enhancing overall security.

This setup ensures secure authentication for relay operations, aligning with the plan’s use of crab-shield for client ID/secret verification.
Conclusion
The crab-dispatch service can be implemented with the proposed endpoints, crates, and structures, leveraging Rust’s performance and safety. Prioritizing SMTP and API functionality, optimizing AI/ML models, and ensuring robust data isolation will create a scalable and secure email service. Future iterations can address IMAP/JMAP support as needed.
