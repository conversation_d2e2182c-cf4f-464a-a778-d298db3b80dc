# Crab-Shield Development Plan
This document outlines a comprehensive plan for developing the crab-shield module, a centralized authentication service for the crab-stack monorepo. Crab-Shield will handle social logins, password/email credentials, OTP (assumed instead of TOTP for email/SMS delivery), password reset, and passkey authentication, integrating with a shared Postgres database and Redis cache, and providing authentication for other services.

## 1. Project Setup
### Objective
Set up the crab-shield Rust crate with the necessary dependencies and directory structure to support authentication features.

**Steps**
* **Directory Structure**: Use the provided structure:

```text
├── Cargo.toml
├── README.md
└── src
    ├── api
    ├── auth
    ├── main.rs
    ├── middleware
    └── models
```

* **Dependencies**: Add to `Cargo.toml`:
```toml
[dependencies]
actix-web = "4"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres"] }
redis = { version = "0.25", features = ["tokio-comp"] }
jsonwebtoken = "9"
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
oauth2 = "4"
webauthn-rs = "0.4"
argon2 = "0.5" 
```

* **Common Utilities:** Leverage the common utilities crate for shared logging, configuration, and connection pooling.

## 2.. Configuration
### Objective
Configure environment variables and settings for database, Redis, OAuth2 providers, and JWT keys.

**Steps**
* Use a library like `config` or `envy` to load settings from environment variables or a config file.
* Define configurations for:
  * Postgres database URL
  * Redis URL
  * OAuth2 client IDs and secrets for Google, X, Microsoft, GitHub
  * JWT private and public keys (RS256 algorithm)
  * Crab-Dispatch API endpoint for email sending
## 3. Database Models
### Objective
Define data models and database interactions for user authentication data.

**Tables**

| Table                   | Fields                                                                     |
|-------------------------|----------------------------------------------------------------------------|
| `users`                 | `id`, `email`, `password_hash`, `created_at`, `updated_at`                 |
| `social_logins`         | `user_id`, `provider`, `provider_user_id`, `access_token`, `refresh_token` |
| `otp_codes`             | `user_id`, `code`, `expires_at`                                            |
| `password_reset_tokens` | `user_id`, `token`, `expires_at`                                           |
| `passkeys`              | `user_id`, `credential_id`, `public_key`, `counter`                        |
| `clients`               | `client_id`, `client_secret`, `project_id`, `created_at`                   |

**Implementation**
Use `sqlx` or `diesel` for database operations.
Define structs in models with methods for CRUD operations, e.g., User::find_by_email, User::create.

## 4. Authentication Logic
### Objective
Implement modular authentication logic for each method in the `auth` directory.

### Structure
**auth/social**: Submodules for each provider (`google.rs`, `twitter.rs`, `microsoft.rs`, `github.rs`).
  * Use `oauth2` crate for OAuth2 flows .
  * **Functions**: `get_auth_url`, `handle_callback`, `get_user_info`.
* **auth/password**: Password-based authentication with Argon2 hashing.
* **auth/otp**: Generate and verify OTP codes sent via email.
* **auth/passkey**: WebAuthn implementation using `webauthn-rs` for biometric authentication, including Apple’s Face ID.
* **auth/reset**: Password reset logic with token generation and email sending.
* **auth/client**: OAuth2 client credentials flow for project authentication.

### Details
* **Social Logins**: For each provider, configure OAuth2 endpoints, handle authorization code exchange, and link provider accounts to users.
* **Password**: Verify passwords against hashed values in the database.
* **OTP**: Generate random codes, store in Redis or database with expiration, send via Crab-Dispatch API.
* **Passkey**: Implement WebAuthn registration and authentication flows.
* **Reset**: Generate reset tokens, send reset links via Crab-Dispatch, and update passwords.
**Client**: Issue access tokens for client credentials, used by other services.
## 5. API Routes
### Objective
Define RESTful API endpoints for authentication flows.

#### Routes

| Endpoint                          | Method | Description                          |
|-----------------------------------|--------|--------------------------------------|
| `/auth/login/password`            | POST   | Authenticate with email and password |
| `/auth/login/{provider}`          | GET    | Initiate OAuth2 flow for provider    |
| `/auth/login/{provider}/callback` | GET    | Handle OAuth2 callback               |
| `/auth/register`                  | POST   | Register new user                    |
| `/auth/reset/request`             | POST   | Request password reset               |
| `/auth/reset`                     | POST   | Reset password with token            |
| `/auth/passkey/register`          | POST   | Register WebAuthn passkey            |
| `/auth/passkey/authenticate`      | POST   | Authenticate with passkey            |
| `/auth/token`                     | POST   | Issue token for client credentials   |
| `/auth/jwks`                      | GET    | Provide JSON Web Key Set             |

**Implementation**
Define routes in `api/mod.rs` using Actix Web scopes.
Example:

```rust
use actix_web::web;

mod auth;

pub fn auth_routes() -> actix_web::Scope {
    web::scope("/auth")
        .route("/login/password", web::post().to(auth::login_password))
        .route("/login/google", web::get().to(auth::login_google))
        .route("/login/google/callback", web::get().to(auth::google_callback))
}
```

## 6. Middleware
### Objective
Implement middleware for authentication, CORS, and logging.

### Details
* **Authentication Middleware**: Verify JWT tokens or session IDs for protected routes.
* **CORS Middleware**: Allow requests from the Next.js dashboard domain.
* **Logging Middleware**: Log requests and errors using the common utilities crate.

## 7. Session Management
### Objective
Manage user sessions, potentially using JWT or Redis-backed sessions.

### Details
* **JWT Approach**: Issue JWT tokens with RS256 algorithm, including user ID and claims. Other services verify tokens using the public key from `/auth/jwks`.
* **Session Approach**: If sessions are preferred, use `actix-session` with Redis to store session data, setting a session ID cookie.
## 8. Security Features
**Objective**
Ensure robust security for the authentication service.

### Details
* **Rate Limiting**: Implement rate limiting to prevent brute-force attacks.
* **Password Hashing**: Use Argon2 for secure password storage .
* **Input Validation**: Validate all inputs to prevent injection attacks.
* **Error Handling**: Avoid leaking sensitive information in error messages.
* **HTTPS**: Ensure all communications use HTTPS.

## 9. Integration with Other Services
### Objective
Enable seamless integration with Crab-Hub, Crab-Dispatch, and the Next.js dashboard.

## Details
* **Crab-Dispatch**: Call its API to send OTP codes and password reset emails.
* **Crab-Hub**: Provide authentication for project creation and resource access.
* **Dashboard**: Support API calls and OAuth2 redirects for user authentication.

## 10. Testing
### Objective
Ensure reliability through comprehensive testing.

### Details
* **Unit Tests**: Test authentication logic in auth modules.
* **Integration Tests**: Test API endpoints and database interactions.
Use actix-web testing utilities for HTTP request simulation.

## 11. Documentation
### Objective
Provide clear documentation for developers and integrators.

### Details
* Document API endpoints with examples.
* Provide integration guides for other services.
* Update `README.md` with setup and usage instructions.

## 12. Additional Considerations
**Future SMS Support**: Abstract OTP delivery to support SMS providers later.
**Scalability**: Ensure the service can handle multiple projects with isolated data.
**Monitoring**: Integrate with SigNoz, as mentioned in the monorepo setup.
This plan leverages Rust’s performance and safety, Actix Web’s flexibility, and best practices for microservices authentication .


Key Citations:

[Best Practices for Authorization in Microservices](https://www.osohq.com/post/microservices-authorization-patterns)
[Authentication in Microservices: Approaches and Techniques](https://frontegg.com/blog/authentication-in-microservices)
[How to Implement Google OAuth2 in Rust](https://codevoweb.com/how-to-implement-google-oauth2-in-rust/)
[OAuth2 Crate for Rust](https://docs.rs/oauth2/latest/oauth2/)