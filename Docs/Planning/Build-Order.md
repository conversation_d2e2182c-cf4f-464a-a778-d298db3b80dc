# Build Order for the Crab Stack

### **Summary**: Setup --> Crab-Shield --> Crab-Hub --> Crab-Dispatch

## 1. Setup.md
  * **Why First**: This sets up the monorepo, Docker, and initial database (Postgres with schemas like `crab_shield`, `crab_hub`). Without it, you can’t run or test services effectively.
  * **Tasks**: Configure the Rust workspace, Docker Compose (Postgres, Redis), and scripts (setup.sh, deploy.sh). Create initial schemas for Crab-Shield and Crab-Hub.
  * **TDD Benefit**: Provides the real environment for testing subsequent services.
## 2. Crab-Shield.md
  * **Why Second**: As the authentication service, it’s a dependency for Crab-Hub and Crab-Dispatch. It can be developed and tested independently with its own schema (e.g., crab_shield.users).
  * **Tasks**: Implement core authentication (password login, client credentials, token issuance) and APIs (e.g., /auth/token, /verify). Set up its database tables and Redis for caching.
  * **TDD Benefit**: You can write tests for login flows and token verification using the real database, avoiding mocks.
## 3. Crab-Hub.md
  * **Why Third**: Depends on Crab-Shield for authentication (e.g., verifying tokens, generating client credentials). Once Crab-Shield is functional, Crab-Hub can integrate with it.
  * **Tasks**: Build project management APIs (e.g., POST /projects), create project schemas, and assign Redis databases. Use Crab-Shield’s real APIs for authentication.
  * **TDD Benefit**: Tests can use Crab-Shield’s live authentication, ensuring realistic integration.
## 4. Crab-Dispatch
  * **Why Last**: Relies on Crab-Shield for authentication and Crab-Hub for project schemas. It’s the most dependent service, so building it last leverages the others.
  * **Tasks**: Implement SMTP server, email processing, and storage (Postgres schemas via Crab-Hub, S3). Integrate with Crab-Shield for client verification.
  * **TDD Benefit**: Tests can use real Crab-Shield tokens and Crab-Hub schemas, aligning with your no-mocks preference.