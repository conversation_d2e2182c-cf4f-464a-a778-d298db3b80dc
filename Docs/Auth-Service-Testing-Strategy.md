# CrabShield Auth Service - Comprehensive Testing Strategy

**Date:** June 15, 2025  
**Target:** 100% Quality Coverage + Security + Performance  
**Current Status:** 4% Coverage → 100% Quality Coverage  
**Approach:** Retroactive + Progressive Testing Implementation

---

## 🎯 Testing Philosophy & Requirements

### Quality Over Coverage
- **100% Line Coverage** is the minimum baseline
- **100% Branch Coverage** for all conditional logic
- **100% Edge Case Coverage** for security-critical functions
- **Real-World Testing** with actual database and Redis instances
- **Security-First Testing** covering OWASP Top 10 vulnerabilities

### Testing Pyramid Structure
```
    🔺 E2E Tests (5%)
      - Complete user flows
      - Cross-browser testing
      - Performance under load
    
  🔺🔺 Integration Tests (25%)
    - Multi-module interactions
    - Database + Redis + External APIs
    - Service-to-service communication
    
🔺🔺🔺 Unit Tests (70%)
  - Individual function testing
  - Edge cases and error conditions
  - Security vulnerability testing
```

---

## 📋 Phase 1: Retroactive Unit Testing (Existing Code)

### 1.1 Authentication Core (`src/services/auth_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/auth_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    use mockall::predicate::*;
    
    // ✅ Happy Path Tests
    #[tokio::test]
    async fn test_authenticate_user_success() {}
    
    #[tokio::test] 
    async fn test_create_user_success() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_authenticate_user_sql_injection_attempt() {}
    
    #[tokio::test]
    async fn test_authenticate_user_timing_attack_resistance() {}
    
    #[tokio::test]
    async fn test_password_hash_verification_constant_time() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_authenticate_user_account_locked() {}
    
    #[tokio::test]
    async fn test_authenticate_user_rate_limited() {}
    
    #[tokio::test]
    async fn test_create_user_duplicate_email() {}
    
    #[tokio::test]
    async fn test_create_user_invalid_email_formats() {}
    
    // 🚨 Error Conditions
    #[tokio::test]
    async fn test_authenticate_user_database_failure() {}
    
    #[tokio::test]
    async fn test_authenticate_user_redis_failure() {}
}
```

#### Critical Edge Cases to Test
1. **Input Validation**
   - Email formats: `test@`, `@domain.com`, `<EMAIL>`
   - Password edge cases: empty, too long (>128 chars), unicode characters
   - SQL injection attempts in email/username fields
   - XSS attempts in user input fields

2. **Security Vulnerabilities**
   - Timing attacks on password verification
   - Account enumeration through error messages
   - Rate limiting bypass attempts
   - Concurrent login attempts

3. **Database Edge Cases**
   - Connection failures during authentication
   - Transaction rollback scenarios
   - Constraint violations
   - Deadlock conditions

### 1.2 Session Management (`src/services/session_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/session_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    // ✅ Core Functionality
    #[tokio::test]
    async fn test_create_session_success() {}
    
    #[tokio::test]
    async fn test_validate_session_success() {}
    
    #[tokio::test]
    async fn test_refresh_session_success() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_session_token_uniqueness() {}
    
    #[tokio::test]
    async fn test_session_token_entropy() {}
    
    #[tokio::test]
    async fn test_session_hijacking_prevention() {}
    
    #[tokio::test]
    async fn test_concurrent_session_limits() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_expired_session_handling() {}
    
    #[tokio::test]
    async fn test_invalid_session_token_formats() {}
    
    #[tokio::test]
    async fn test_redis_failover_session_recovery() {}
    
    // 🚨 Error Conditions
    #[tokio::test]
    async fn test_redis_connection_failure() {}
    
    #[tokio::test]
    async fn test_database_session_fallback() {}
}
```

#### Critical Edge Cases
1. **Session Security**
   - Session fixation attacks
   - Session token prediction attempts
   - Concurrent session management
   - Session cleanup on logout

2. **Redis Failure Scenarios**
   - Redis connection loss during session creation
   - Redis data corruption
   - Redis memory limits exceeded
   - Failover to database storage

### 1.3 MFA Service (`src/services/mfa_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/mfa_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    // ✅ TOTP Functionality
    #[tokio::test]
    async fn test_generate_totp_secret() {}
    
    #[tokio::test]
    async fn test_verify_totp_code_success() {}
    
    #[tokio::test]
    async fn test_generate_backup_codes() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_totp_time_window_validation() {}
    
    #[tokio::test]
    async fn test_backup_code_single_use() {}
    
    #[tokio::test]
    async fn test_mfa_brute_force_protection() {}
    
    #[tokio::test]
    async fn test_qr_code_secret_exposure() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_clock_skew_tolerance() {}
    
    #[tokio::test]
    async fn test_invalid_totp_formats() {}
    
    #[tokio::test]
    async fn test_expired_backup_codes() {}
    
    // 🚨 Device Trust
    #[tokio::test]
    async fn test_device_fingerprinting() {}
    
    #[tokio::test]
    async fn test_trusted_device_management() {}
}
```

### 1.4 Password Service (`src/services/password_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/password_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    // ✅ Core Functionality
    #[tokio::test]
    async fn test_hash_password_argon2() {}
    
    #[tokio::test]
    async fn test_verify_password_success() {}
    
    #[tokio::test]
    async fn test_password_strength_validation() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_password_hash_uniqueness() {}
    
    #[tokio::test]
    async fn test_timing_attack_resistance() {}
    
    #[tokio::test]
    async fn test_common_password_rejection() {}
    
    #[tokio::test]
    async fn test_password_history_enforcement() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_unicode_password_handling() {}
    
    #[tokio::test]
    async fn test_maximum_password_length() {}
    
    #[tokio::test]
    async fn test_empty_password_rejection() {}
    
    // 🚨 Performance Tests
    #[tokio::test]
    async fn test_argon2_performance_limits() {}
}
```

### 1.5 RBAC Service (`src/services/rbac_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/rbac_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    // ✅ Core Functionality
    #[tokio::test]
    async fn test_assign_role_to_user() {}
    
    #[tokio::test]
    async fn test_check_user_permission() {}
    
    #[tokio::test]
    async fn test_role_inheritance() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_privilege_escalation_prevention() {}
    
    #[tokio::test]
    async fn test_role_assignment_authorization() {}
    
    #[tokio::test]
    async fn test_permission_cache_poisoning() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_circular_role_inheritance() {}
    
    #[tokio::test]
    async fn test_role_expiration_handling() {}
    
    #[tokio::test]
    async fn test_invalid_permission_formats() {}
    
    // 🚨 Performance Tests
    #[tokio::test]
    async fn test_permission_check_performance() {}
    
    #[tokio::test]
    async fn test_redis_cache_efficiency() {}
}
```

### 1.6 JWT Service (`src/services/jwt_service.rs`)

#### Test Coverage Requirements
```rust
// src/services/jwt_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    // ✅ Core Functionality
    #[tokio::test]
    async fn test_create_token_pair() {}
    
    #[tokio::test]
    async fn test_validate_access_token() {}
    
    #[tokio::test]
    async fn test_refresh_token_flow() {}
    
    // 🔒 Security Tests
    #[tokio::test]
    async fn test_token_signature_validation() {}
    
    #[tokio::test]
    async fn test_token_expiration_enforcement() {}
    
    #[tokio::test]
    async fn test_token_revocation() {}
    
    #[tokio::test]
    async fn test_jwt_algorithm_confusion() {}
    
    // ⚠️ Edge Cases
    #[tokio::test]
    async fn test_malformed_jwt_handling() {}
    
    #[tokio::test]
    async fn test_clock_skew_tolerance() {}
    
    #[tokio::test]
    async fn test_token_size_limits() {}
    
    // 🚨 Attack Scenarios
    #[tokio::test]
    async fn test_none_algorithm_attack() {}
    
    #[tokio::test]
    async fn test_key_confusion_attack() {}
}
```

---

## 📋 Phase 2: Integration Testing Strategy

### 2.1 Database Integration Tests

#### Test Environment Setup
```rust
// tests/integration/database_integration.rs
use sqlx::PgPool;
use testcontainers::clients::Cli;
use testcontainers::images::postgres::Postgres;

async fn setup_test_database() -> PgPool {
    // Use testcontainers for isolated database testing
    let docker = Cli::default();
    let postgres_image = Postgres::default();
    let node = docker.run(postgres_image);
    
    let connection_string = format!(
        "postgres://postgres:postgres@127.0.0.1:{}/postgres",
        node.get_host_port_ipv4(5432)
    );
    
    let pool = PgPool::connect(&connection_string).await.unwrap();
    
    // Run migrations
    sqlx::migrate!("./migrations").run(&pool).await.unwrap();
    
    pool
}
```

#### Critical Integration Test Scenarios
```rust
// tests/integration/auth_flow_integration.rs
#[tokio::test]
async fn test_complete_user_registration_flow() {
    // 1. Create user
    // 2. Send email verification
    // 3. Verify email token
    // 4. Login user
    // 5. Create session
    // 6. Validate session
}

#[tokio::test]
async fn test_mfa_setup_and_login_flow() {
    // 1. Register user
    // 2. Setup TOTP
    // 3. Generate backup codes
    // 4. Login with MFA
    // 5. Test backup code usage
}

#[tokio::test]
async fn test_password_reset_complete_flow() {
    // 1. Request password reset
    // 2. Generate reset token
    // 3. Validate reset token
    // 4. Reset password
    // 5. Invalidate old sessions
}

#[tokio::test]
async fn test_oauth_account_linking_flow() {
    // 1. Create user with email/password
    // 2. Initiate OAuth flow
    // 3. Link OAuth account
    // 4. Login via OAuth
    // 5. Unlink OAuth account
}
```

### 2.2 Redis Integration Tests

#### Redis Test Scenarios
```rust
// tests/integration/redis_integration.rs
#[tokio::test]
async fn test_session_redis_failover() {
    // 1. Create session in Redis
    // 2. Simulate Redis failure
    // 3. Verify database fallback
    // 4. Restore Redis
    // 5. Verify session sync
}

#[tokio::test]
async fn test_rate_limiting_redis_integration() {
    // 1. Make requests within limit
    // 2. Exceed rate limit
    // 3. Verify blocking
    // 4. Wait for reset
    // 5. Verify access restored
}
```

### 2.3 External Service Integration Tests

#### Email Service Integration
```rust
// tests/integration/email_integration.rs
#[tokio::test]
async fn test_resend_email_integration() {
    // Test with actual RESEND API (test mode)
    // Verify email delivery
    // Test email template rendering
    // Test client-specific branding
}

#[tokio::test]
async fn test_email_service_fallback() {
    // Test RESEND failure
    // Verify fallback to internal service
    // Test error handling and retry logic
}
```

---

## 📋 Phase 3: End-to-End Testing Strategy

### 3.1 E2E Test Setup with Playwright

#### Test Environment Configuration
```typescript
// tests/e2e/playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

#### Critical E2E Test Flows
```typescript
// tests/e2e/auth-flows.spec.ts
import { test, expect } from '@playwright/test';

test.describe('User Registration Flow', () => {
  test('complete user registration with email verification', async ({ page }) => {
    // 1. Navigate to registration page
    await page.goto('/register');
    
    // 2. Fill registration form
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'SecurePass123!');
    await page.fill('[data-testid=confirm-password-input]', 'SecurePass123!');
    
    // 3. Submit form
    await page.click('[data-testid=register-button]');
    
    // 4. Verify email verification prompt
    await expect(page.locator('[data-testid=verification-message]')).toBeVisible();
    
    // 5. Simulate email verification (using test endpoint)
    const verificationToken = await getVerificationToken('<EMAIL>');
    await page.goto(`/verify-email?token=${verificationToken}`);
    
    // 6. Verify successful verification
    await expect(page.locator('[data-testid=verification-success]')).toBeVisible();
    
    // 7. Login with verified account
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'SecurePass123!');
    await page.click('[data-testid=login-button]');
    
    // 8. Verify successful login
    await expect(page.locator('[data-testid=dashboard]')).toBeVisible();
  });
});

test.describe('MFA Setup and Login', () => {
  test('setup TOTP and login with MFA', async ({ page }) => {
    // 1. Login to existing account
    await loginUser(page, '<EMAIL>', 'SecurePass123!');
    
    // 2. Navigate to MFA setup
    await page.goto('/settings/security');
    await page.click('[data-testid=setup-mfa-button]');
    
    // 3. Scan QR code (simulate)
    const totpSecret = await page.getAttribute('[data-testid=totp-secret]', 'data-secret');
    
    // 4. Enter TOTP code
    const totpCode = generateTOTPCode(totpSecret);
    await page.fill('[data-testid=totp-code-input]', totpCode);
    await page.click('[data-testid=verify-totp-button]');
    
    // 5. Save backup codes
    await expect(page.locator('[data-testid=backup-codes]')).toBeVisible();
    await page.click('[data-testid=save-backup-codes-button]');
    
    // 6. Logout and login with MFA
    await page.click('[data-testid=logout-button]');
    await loginUser(page, '<EMAIL>', 'SecurePass123!');
    
    // 7. Enter MFA code
    const newTotpCode = generateTOTPCode(totpSecret);
    await page.fill('[data-testid=mfa-code-input]', newTotpCode);
    await page.click('[data-testid=verify-mfa-button]');
    
    // 8. Verify successful MFA login
    await expect(page.locator('[data-testid=dashboard]')).toBeVisible();
  });
});

test.describe('OAuth Authentication Flows', () => {
  test('Google OAuth registration and login', async ({ page }) => {
    // 1. Navigate to login page
    await page.goto('/login');

    // 2. Click Google OAuth button
    await page.click('[data-testid=google-oauth-button]');

    // 3. Handle OAuth popup (mock Google OAuth)
    const [popup] = await Promise.all([
      page.waitForEvent('popup'),
      page.click('[data-testid=google-oauth-button]')
    ]);

    // 4. Simulate Google OAuth consent
    await popup.goto('/mock-oauth/google/consent');
    await popup.fill('[data-testid=email]', '<EMAIL>');
    await popup.click('[data-testid=consent-button]');

    // 5. Verify redirect back to app
    await page.waitForURL('/dashboard');
    await expect(page.locator('[data-testid=welcome-message]')).toContainText('<EMAIL>');
  });

  test('OAuth account linking', async ({ page }) => {
    // 1. Login with existing email/password account
    await loginUser(page, '<EMAIL>', 'SecurePass123!');

    // 2. Navigate to account settings
    await page.goto('/settings/account');

    // 3. Link Google account
    await page.click('[data-testid=link-google-button]');

    // 4. Complete OAuth flow
    const [popup] = await Promise.all([
      page.waitForEvent('popup'),
      page.click('[data-testid=link-google-button]')
    ]);

    await popup.goto('/mock-oauth/google/consent');
    await popup.fill('[data-testid=email]', '<EMAIL>');
    await popup.click('[data-testid=consent-button]');

    // 5. Verify account linking success
    await expect(page.locator('[data-testid=linked-accounts]')).toContainText('Google');

    // 6. Test login with linked OAuth account
    await page.click('[data-testid=logout-button]');
    await page.goto('/login');
    await page.click('[data-testid=google-oauth-button]');

    // 7. Verify automatic login
    await page.waitForURL('/dashboard');
  });
});

test.describe('Security and Error Scenarios', () => {
  test('account lockout after failed attempts', async ({ page }) => {
    await page.goto('/login');

    // Attempt multiple failed logins
    for (let i = 0; i < 5; i++) {
      await page.fill('[data-testid=email-input]', '<EMAIL>');
      await page.fill('[data-testid=password-input]', 'wrongpassword');
      await page.click('[data-testid=login-button]');
      await expect(page.locator('[data-testid=error-message]')).toBeVisible();
    }

    // Verify account lockout
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'correctpassword');
    await page.click('[data-testid=login-button]');
    await expect(page.locator('[data-testid=lockout-message]')).toBeVisible();
  });

  test('session timeout handling', async ({ page }) => {
    // 1. Login user
    await loginUser(page, '<EMAIL>', 'SecurePass123!');

    // 2. Simulate session expiration
    await page.evaluate(() => {
      localStorage.setItem('session_expires_at', Date.now() - 1000);
    });

    // 3. Try to access protected page
    await page.goto('/dashboard');

    // 4. Verify redirect to login
    await page.waitForURL('/login');
    await expect(page.locator('[data-testid=session-expired-message]')).toBeVisible();
  });
});
```

---

## 📋 Phase 4: Performance Testing Strategy

### 4.1 Load Testing with wrk

#### Authentication Endpoint Load Tests
```bash
#!/bin/bash
# scripts/load-test-auth.sh

# Test login endpoint under load
echo "Testing login endpoint..."
wrk -t12 -c400 -d30s -s scripts/login-load.lua http://localhost:8080/auth/login

# Test session validation under load
echo "Testing session validation..."
wrk -t12 -c400 -d30s -s scripts/session-load.lua http://localhost:8080/api/v1/sessions/validate

# Test registration endpoint under load
echo "Testing registration endpoint..."
wrk -t12 -c400 -d30s -s scripts/register-load.lua http://localhost:8080/auth/register
```

#### wrk Lua Scripts
```lua
-- scripts/login-load.lua
wrk.method = "POST"
wrk.body   = '{"email":"<EMAIL>","password":"SecurePass123!"}'
wrk.headers["Content-Type"] = "application/json"

function response(status, headers, body)
  if status ~= 200 then
    print("Error: " .. status .. " " .. body)
  end
end
```

### 4.2 Micro-benchmarks with Criterion

#### Password Hashing Benchmarks
```rust
// benches/password_benchmarks.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use auth_service::services::PasswordService;

fn password_hashing_benchmark(c: &mut Criterion) {
    let password_service = PasswordService::new();

    c.bench_function("argon2_hash_password", |b| {
        b.iter(|| {
            password_service.hash_password(black_box("SecurePassword123!"))
        })
    });

    c.bench_function("argon2_verify_password", |b| {
        let hash = password_service.hash_password("SecurePassword123!").unwrap();
        b.iter(|| {
            password_service.verify_password(
                black_box("SecurePassword123!"),
                black_box(&hash)
            )
        })
    });
}

fn jwt_token_benchmark(c: &mut Criterion) {
    let jwt_service = JwtService::new("test_secret", "issuer".to_string(), "audience".to_string());

    c.bench_function("jwt_create_token", |b| {
        b.iter(|| {
            jwt_service.create_token_pair(
                black_box(Uuid::new_v4()),
                black_box("<EMAIL>".to_string()),
                black_box(vec!["user".to_string()])
            )
        })
    });

    c.bench_function("jwt_validate_token", |b| {
        let token_pair = jwt_service.create_token_pair(
            Uuid::new_v4(),
            "<EMAIL>".to_string(),
            vec!["user".to_string()]
        ).unwrap();

        b.iter(|| {
            jwt_service.validate_token(black_box(&token_pair.access_token))
        })
    });
}

criterion_group!(benches, password_hashing_benchmark, jwt_token_benchmark);
criterion_main!(benches);
```

### 4.3 Database Performance Tests

#### Connection Pool Stress Tests
```rust
// tests/performance/database_performance.rs
#[tokio::test]
async fn test_concurrent_database_operations() {
    let pool = setup_test_database().await;
    let auth_service = AuthService::new(pool.clone(), PasswordService::new(), LockoutService::new());

    // Simulate 1000 concurrent user registrations
    let tasks: Vec<_> = (0..1000)
        .map(|i| {
            let auth_service = auth_service.clone();
            tokio::spawn(async move {
                let request = CreateUserRequest {
                    email: format!("user{}@example.com", i),
                    password: "SecurePass123!".to_string(),
                    first_name: Some("Test".to_string()),
                    last_name: Some("User".to_string()),
                };
                auth_service.create_user(request).await
            })
        })
        .collect();

    let results = futures::future::join_all(tasks).await;

    // Verify all operations completed successfully
    let successful_operations = results.iter()
        .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
        .count();

    assert!(successful_operations >= 950); // Allow for some failures under extreme load
}
```

---

## 📋 Phase 5: Security Testing Strategy

### 5.1 OWASP Top 10 Testing

#### A01: Broken Access Control
```rust
// tests/security/access_control_tests.rs
#[tokio::test]
async fn test_horizontal_privilege_escalation() {
    // 1. Create two users
    let user1 = create_test_user("<EMAIL>").await;
    let user2 = create_test_user("<EMAIL>").await;

    // 2. Login as user1
    let user1_session = login_user(&user1).await;

    // 3. Attempt to access user2's data using user1's session
    let response = client
        .get(&format!("/api/v1/users/{}/profile", user2.id))
        .header("Authorization", format!("Bearer {}", user1_session.access_token))
        .send()
        .await;

    // 4. Verify access is denied
    assert_eq!(response.status(), 403);
}

#[tokio::test]
async fn test_vertical_privilege_escalation() {
    // 1. Create regular user
    let user = create_test_user("<EMAIL>").await;
    let session = login_user(&user).await;

    // 2. Attempt to access admin endpoint
    let response = client
        .get("/api/v1/admin/users")
        .header("Authorization", format!("Bearer {}", session.access_token))
        .send()
        .await;

    // 3. Verify access is denied
    assert_eq!(response.status(), 403);
}
```

#### A02: Cryptographic Failures
```rust
// tests/security/crypto_tests.rs
#[tokio::test]
async fn test_password_storage_security() {
    let password_service = PasswordService::new();
    let password = "SecurePassword123!";

    // 1. Hash the same password multiple times
    let hash1 = password_service.hash_password(password).unwrap();
    let hash2 = password_service.hash_password(password).unwrap();

    // 2. Verify hashes are different (salt working)
    assert_ne!(hash1, hash2);

    // 3. Verify both hashes validate correctly
    assert!(password_service.verify_password(password, &hash1).unwrap());
    assert!(password_service.verify_password(password, &hash2).unwrap());

    // 4. Verify wrong password fails
    assert!(!password_service.verify_password("wrongpassword", &hash1).unwrap());
}

#[tokio::test]
async fn test_jwt_token_security() {
    let jwt_service = JwtService::new("test_secret", "issuer".to_string(), "audience".to_string());

    // 1. Create token
    let token_pair = jwt_service.create_token_pair(
        Uuid::new_v4(),
        "<EMAIL>".to_string(),
        vec!["user".to_string()]
    ).unwrap();

    // 2. Verify token tampering detection
    let mut tampered_token = token_pair.access_token.clone();
    tampered_token.push('x'); // Tamper with token

    let result = jwt_service.validate_token(&tampered_token);
    assert!(result.is_err());

    // 3. Test algorithm confusion attack
    let none_algorithm_token = create_none_algorithm_token();
    let result = jwt_service.validate_token(&none_algorithm_token);
    assert!(result.is_err());
}
```

#### A03: Injection Attacks
```rust
// tests/security/injection_tests.rs
#[tokio::test]
async fn test_sql_injection_prevention() {
    let auth_service = setup_auth_service().await;

    // SQL injection attempts in email field
    let injection_attempts = vec![
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'/*",
        "' UNION SELECT * FROM users --",
    ];

    for injection in injection_attempts {
        let request = LoginRequest {
            email: injection.to_string(),
            password: "password".to_string(),
        };

        let result = auth_service.authenticate_user(request, "127.0.0.1").await;

        // Should fail safely without causing SQL injection
        assert!(result.is_err() || result.unwrap().is_none());
    }
}

#[tokio::test]
async fn test_xss_prevention() {
    let auth_service = setup_auth_service().await;

    // XSS attempts in user input
    let xss_attempts = vec![
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "';alert('xss');//",
    ];

    for xss in xss_attempts {
        let request = CreateUserRequest {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
            first_name: Some(xss.to_string()),
            last_name: Some("User".to_string()),
        };

        let result = auth_service.create_user(request).await;

        if let Ok(user) = result {
            // Verify XSS payload is properly escaped/sanitized
            assert!(!user.first_name.unwrap_or_default().contains("<script>"));
            assert!(!user.first_name.unwrap_or_default().contains("javascript:"));
        }
    }
}
```

### 5.2 Rate Limiting and DoS Protection Tests

```rust
// tests/security/rate_limiting_tests.rs
#[tokio::test]
async fn test_login_rate_limiting() {
    let client = setup_test_client().await;

    // Attempt rapid login requests
    let mut successful_requests = 0;
    let mut rate_limited_requests = 0;

    for _ in 0..100 {
        let response = client
            .post("/auth/login")
            .json(&json!({
                "email": "<EMAIL>",
                "password": "wrongpassword"
            }))
            .send()
            .await;

        match response.status().as_u16() {
            401 => successful_requests += 1, // Authentication failed (expected)
            429 => rate_limited_requests += 1, // Rate limited
            _ => {}
        }
    }

    // Verify rate limiting kicks in
    assert!(rate_limited_requests > 0);
    assert!(successful_requests < 100);
}

#[tokio::test]
async fn test_registration_rate_limiting() {
    let client = setup_test_client().await;

    // Attempt rapid registration requests from same IP
    let mut responses = Vec::new();

    for i in 0..20 {
        let response = client
            .post("/auth/register")
            .json(&json!({
                "email": format!("test{}@example.com", i),
                "password": "SecurePass123!"
            }))
            .send()
            .await;

        responses.push(response.status().as_u16());
    }

    // Verify some requests are rate limited
    let rate_limited_count = responses.iter().filter(|&&status| status == 429).count();
    assert!(rate_limited_count > 0);
}
```

---

## 📋 Phase 6: Test Implementation Timeline

### 6.1 Week 1-2: Unit Test Foundation (Retroactive)

#### Priority 1: Critical Security Functions
- [ ] `password_service.rs` - Password hashing and verification
- [ ] `jwt_service.rs` - Token creation and validation
- [ ] `auth_service.rs` - Core authentication logic
- [ ] `session_service.rs` - Session management

#### Priority 2: Core Business Logic
- [ ] `mfa_service.rs` - TOTP and backup codes
- [ ] `rbac_service.rs` - Role and permission management
- [ ] `lockout_service.rs` - Account lockout logic
- [ ] `email_verification_service.rs` - Email verification

#### Priority 3: Supporting Services
- [ ] `password_reset_service.rs` - Password reset flows
- [ ] All handler modules (`handlers/*.rs`)
- [ ] Middleware modules (`middleware/*.rs`)
- [ ] Model validation (`models/*.rs`)

### 6.2 Week 3: Integration Testing

#### Database Integration Tests
- [ ] User registration → email verification → login flow
- [ ] MFA setup → login with TOTP → backup code usage
- [ ] Password reset → token validation → password change
- [ ] OAuth account linking → OAuth login → account unlinking
- [ ] RBAC role assignment → permission checking → role inheritance

#### Redis Integration Tests
- [ ] Session creation → Redis storage → validation → cleanup
- [ ] Rate limiting → Redis counters → limit enforcement → reset
- [ ] Account lockout → Redis tracking → progressive timeouts
- [ ] RBAC caching → Redis performance → cache invalidation

#### External Service Integration Tests
- [ ] Email service integration (RESEND + internal fallback)
- [ ] SMS service integration (Twilio)
- [ ] OAuth provider integration (Google, GitHub, Microsoft)

### 6.3 Week 4: E2E and Performance Testing

#### E2E Test Implementation
- [ ] Complete user registration flow (with email verification)
- [ ] MFA setup and login flow
- [ ] OAuth registration and login flows
- [ ] Password reset flow
- [ ] Account lockout and recovery
- [ ] Multi-client authentication flows
- [ ] Cross-browser compatibility testing

#### Performance Testing
- [ ] Load testing with wrk (authentication endpoints)
- [ ] Micro-benchmarks with Criterion (crypto operations)
- [ ] Database performance under concurrent load
- [ ] Redis performance and failover testing
- [ ] Memory usage and leak detection

### 6.4 Week 5: Security Testing and Hardening

#### OWASP Top 10 Testing
- [ ] A01: Broken Access Control
- [ ] A02: Cryptographic Failures
- [ ] A03: Injection Attacks
- [ ] A04: Insecure Design
- [ ] A05: Security Misconfiguration
- [ ] A06: Vulnerable Components
- [ ] A07: Authentication Failures
- [ ] A08: Software Integrity Failures
- [ ] A09: Logging Failures
- [ ] A10: Server-Side Request Forgery

#### Additional Security Tests
- [ ] Rate limiting and DoS protection
- [ ] Session security (fixation, hijacking)
- [ ] Token security (JWT attacks)
- [ ] Input validation and sanitization
- [ ] Error handling and information disclosure

---

## 📊 Quality Metrics and Success Criteria

### Coverage Requirements
- **Line Coverage**: 100% (no exceptions)
- **Branch Coverage**: 100% (all conditional paths)
- **Function Coverage**: 100% (all functions tested)
- **Integration Coverage**: 100% (all service interactions)

### Security Testing Requirements
- **OWASP Top 10**: 100% coverage with specific test cases
- **Input Validation**: All inputs tested with malicious payloads
- **Authentication Security**: All auth flows tested for vulnerabilities
- **Authorization Security**: All permission checks tested for bypasses

### Performance Requirements
- **Authentication**: <100ms response time (95th percentile)
- **Session Validation**: <50ms response time (95th percentile)
- **Password Hashing**: <500ms per operation
- **Concurrent Users**: Support 1000+ concurrent sessions
- **Database Operations**: <200ms for complex queries

### Reliability Requirements
- **Test Stability**: 99.9% test pass rate (flaky tests not acceptable)
- **Error Handling**: 100% error scenarios tested
- **Failover Testing**: All external service failures tested
- **Data Integrity**: All database operations tested for consistency

---

## 🔧 Test Infrastructure Setup

### Required Dependencies
```toml
# Add to Cargo.toml [dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
testcontainers = "0.15"
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"
fake = "2.9"
wiremock = "0.5"
```

### Test Database Setup
```rust
// tests/common/mod.rs
use testcontainers::{clients::Cli, images::postgres::Postgres};
use sqlx::PgPool;

pub async fn setup_test_database() -> PgPool {
    let docker = Cli::default();
    let postgres_image = Postgres::default()
        .with_db_name("auth_test")
        .with_user("test_user")
        .with_password("test_password");

    let node = docker.run(postgres_image);
    let connection_string = format!(
        "postgres://test_user:test_password@127.0.0.1:{}/auth_test",
        node.get_host_port_ipv4(5432)
    );

    let pool = PgPool::connect(&connection_string).await.unwrap();
    sqlx::migrate!("./migrations").run(&pool).await.unwrap();

    pool
}
```

### Mock Services Setup
```rust
// tests/mocks/mod.rs
use wiremock::{MockServer, Mock, ResponseTemplate};
use wiremock::matchers::{method, path};

pub async fn setup_mock_email_service() -> MockServer {
    let mock_server = MockServer::start().await;

    Mock::given(method("POST"))
        .and(path("/send-email"))
        .respond_with(ResponseTemplate::new(200).set_body_json(json!({
            "success": true,
            "message_id": "test_message_id"
        })))
        .mount(&mock_server)
        .await;

    mock_server
}
```

---

## ✅ Implementation Checklist

### Unit Tests (Week 1-2)
- [ ] All service modules have comprehensive unit tests
- [ ] All edge cases and error conditions covered
- [ ] All security-critical functions tested
- [ ] Mock dependencies properly isolated
- [ ] Property-based testing for complex logic

### Integration Tests (Week 3)
- [ ] All multi-service interactions tested
- [ ] Database integration with real PostgreSQL
- [ ] Redis integration with real Redis instance
- [ ] External service integration with mocks
- [ ] Error handling and failover scenarios

### E2E Tests (Week 4)
- [ ] Complete user flows tested end-to-end
- [ ] Cross-browser compatibility verified
- [ ] Mobile responsiveness tested
- [ ] Accessibility requirements met
- [ ] Performance under realistic load

### Security Tests (Week 5)
- [ ] OWASP Top 10 vulnerabilities tested
- [ ] Penetration testing scenarios covered
- [ ] Input validation comprehensive
- [ ] Authentication and authorization secure
- [ ] Error handling doesn't leak information

### Performance Tests (Week 4-5)
- [ ] Load testing with realistic traffic patterns
- [ ] Stress testing beyond normal capacity
- [ ] Memory usage profiling completed
- [ ] Database query optimization verified
- [ ] Caching effectiveness measured

**Final Target**: Enterprise-grade test suite with 100% quality coverage, comprehensive security testing, and performance validation suitable for production deployment.
```
```
