# Sprints Overview
This document is intended to serve as a high level overview for building out the project.
For more detail consult the individual sprint documents.

## Sprint 1: Goal - Infrastructure and shared resources configured
### 1. Configure shared infrastructure via docker
* Grafana, Loki, Prometheus, Jaeger, for observability and monitoring
* GlitchTip for error tracking
* PostgreSQL for data storage
* Redis for caching
* APISIX for API gateway
* etcd for configuration management
