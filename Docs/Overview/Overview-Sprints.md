# Sprints Overview
This document is intended to serve as a high level overview for building out the project.
For more detail consult the individual sprint documents.

## Sprint 1: Goal - Infrastructure and shared resources configured
### 1. Configure shared infrastructure via docker
* Data Layer: PostgreSQL, Redis
* API Gateway: APISIX
* Observability: Prometheus, Grafana, Loki, Jaeger
* Error Tracking: GlitchTip
### 2. Configure Authentication and Authorization
* Using CrabShield for authentication
* Setup Integration points and link to database