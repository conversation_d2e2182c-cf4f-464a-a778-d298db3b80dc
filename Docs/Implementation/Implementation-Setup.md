# Setup Guide Overview
This guide breaks down the setup process for the crab-stack, focusing on the tasks outlined in Setup.md. It includes step-by-step directions and prompts for AI assistants to implement each part, ensuring clarity for development.


## Steps: 
* **Create Initial Database Schemas**: Set up Postgres schemas for Crab-Shield and Crab-Hub, ensuring logical data separation.
  * Use the SQL script example to create and secure schemas.
* **Set Up Automation Scripts**: Create setup.sh, deploy.sh, and backup.sh for initial setup, deployment, and data backup.
  * Implement basic scripts using the provided examples, customizing as necessary.
* **Configure Local Development with MinIO**: Use MinIO for local S3-compatible storage, mirroring Contabo's setup for production.
  * Follow the instructions to start MinIO and set environment variables.
* **Verify Setup**: Run a verification script to ensure all components are correctly installed and configured.
  * Use the example script to check installations and service status.
* **Dashboard and Configuration**:
  * Since the dashboard is deferred, focus on configuration files and environment variables for now. Ensure logging is printed to logs for short-term monitoring, using SigNoz dashboards if needed.

* **Environment Matching for Contabo**
  * Using MinIO locally, which is S3 compatible like Contabo's object storage, should require minimal configuration changes. Set environment variables for endpoints and credentials to switch between local and production environments seamlessly.

**Tests During Setup**
  * Given the focus on TDD, verification scripts can serve as initial checks rather than formal tests, ensuring the environment is ready for service development.


Background and Context
The crab-stack is a Rust-based monorepo project comprising services like Crab-Shield (authentication), Crab-Hub (resource management), Crab-Dispatch (email handling), and Crab-Stash (object storage), with shared utilities in a common crate. The setup phase, as detailed in the Build-Order.md, involves configuring the monorepo, Docker services, and initial database schemas, setting the foundation for subsequent service development. The user intends to use AI assistants for implementation, necessitating clear prompts and examples, and has specific considerations for dashboard integration, environment matching with Contabo, and Test-Driven Development (TDD) during setup.


### 1. Initial Database Schemas
Initial schemas in Postgres are necessary for Crab-Shield and Crab-Hub, ensuring logical separation. An example SQL script is:

```sql
-- Create schemas
CREATE SCHEMA crab_shield;
CREATE SCHEMA crab_hub;

-- Create roles
CREATE ROLE crab_shield_role WITH LOGIN PASSWORD 'password';
CREATE ROLE crab_hub_role WITH LOGIN PASSWORD 'password';

-- Grant permissions
GRANT ALL ON SCHEMA crab_shield TO crab_shield_role;
GRANT ALL ON SCHEMA crab_hub TO crab_hub_role;

-- Set search path
ALTER ROLE crab_shield_role SET search_path TO crab_shield;
ALTER ROLE crab_hub_role SET search_path TO crab_hub;
```

**Prompt for AI Assistant**:
"Write a SQL script to create 'crab_shield' and 'crab_hub' schemas in Postgres, including roles and permissions as shown above, and save it for execution after starting the Postgres service."

This step ensures data isolation, with AI assistance ensuring correct SQL syntax.

### 2. Automation Scripts
Scripts automate initial setup, deployment, and backups. Examples include:
* `setup.sh`

```bash
#!/bin/bash

# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install Docker
sudo apt-get update
sudo apt-get install -y docker.io

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

* `deploy.sh`:

```bash
#!/bin/bash

# Build Rust services
cargo build --release

# Build Docker images
docker build -t crabstack .

# Start services with Docker Compose
docker-compose -f config/docker-compose.yml up -d
```

* `backup.sh`:

```bash
#!/bin/bash

# Backup Postgres
docker exec -t postgres pg_dumpall -c -U admin > backup.sql

# Backup object storage (example for MinIO)
mc mirror local/minio s3/backup-bucket
```

**Prompt for AI Assistant**:
"Create setup.sh, deploy.sh, and backup.sh in the scripts/ directory with the above implementations, ensuring they are executable and include basic error handling."

This automates key processes, with AI assistance ensuring script functionality.

### 3. MinIO for Local Development
For local development, use MinIO as an S3-compatible storage solution, mirroring Contabo's object storage. Instructions include:

* **Pull the MinIO Docker image:**

```bash
docker pull minio/minio
```

* **Run MinIO:**

```bash
docker run -p 9000:9000 -p 9001:9001 minio/minio server /data --console-address ":9001"
```

* Access the console at [invalid url, do not cite] and log in with default credentials (`minioadmin/minioadmin`).
* Create a bucket, e.g., "crabstack-dev".
* Set environment variables:

```bash
export S3_ENDPOINT=[invalid url, do not cite]
export S3_ACCESS_KEY=minioadmin
export S3_SECRET_KEY=minioadmin
export S3_BUCKET=crabstack-dev
```

Prompt for AI Assistant:
"Provide detailed instructions for setting up MinIO locally using Docker, including starting the service, creating a bucket, and setting environment variables for S3 compatibility, as shown above."

This ensures local development aligns with production, with minimal configuration differences due to S3 compatibility.

### 4. Setup Verification
Verify the setup with a script to ensure all components are correctly installed and configured. An example script is:

```bash
#!/bin/bash

# Check Rust installation
if ! command -v cargo &> /dev/null; then
    echo "Rust is not installed."
    exit 1
fi

# Check Docker installation
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed."
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed."
    exit 1
fi

# Check if Docker Compose services are running
docker-compose -f config/docker-compose.yml ps | grep Up
if [ $? -ne 0 ]; then
    echo "Docker Compose services are not running."
    exit 1
fi

# Check Postgres schemas
docker exec -it postgres psql -U admin -d dev -c "\dn" | grep crab_shield
if [ $? -ne 0 ]; then
    echo "Schema crab_shield does not exist."
    exit 1
fi

docker exec -it postgres psql -U admin -d dev -c "\dn" | grep crab_hub
if [ $? -ne 0 ]; then
    echo "Schema crab_hub does not exist."
    exit 1
fi

echo "Setup verification passed."
```
**Prompt for AI Assistant:**
"Create a bash script named verify_setup.sh in the scripts/ directory with the above implementation to verify Rust, Docker, Docker Compose, service status, and Postgres schemas."

This ensures the setup is ready for development, with AI assistance ensuring script accuracy.

**Dashboard and Configuration Considerations**
The user has decided to defer dashboard integration, opting for configuration files and environment variables for now. Logging should be printed to logs for short-term monitoring, with SigNoz dashboards available for data if needed. Ensure all services are configured to log appropriately, using Loki for log aggregation as part of the SigNoz setup.

**Environment Matching for Contabo**
Given the user's intention to deploy on Contabo, using MinIO locally for development is appropriate due to its S3 compatibility with Contabo's object storage. Configuration differences are minimal, primarily involving setting environment variables for the S3 endpoint, access keys, and bucket names. For example, in code, use:

```rust
let s3_endpoint = std::env::var("S3_ENDPOINT").unwrap_or("[invalid url, do not cite]");
```

Ensure the code is flexible to switch between MinIO ([invalid url, do not cite]) for local and Contabo for production, with credentials managed via environment variables.

### **TDD and Tests During Setup**
Given the user's focus on TDD, tests during the setup phase are less about unit tests and more about verification. The provided verification script serves as a basic check, ensuring the environment is correctly configured. For subsequent service development, TDD will apply, with integration tests using the real database and services, aligning with the no-mocks preference.

#### Summary Table of Setup Tasks


<table>
  <thead>
    <tr>
      <th>Task</th>
      <th>Description</th>
      <th>Key Output</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Monorepo Structure</td>
      <td>Create directory layout for all services and utils</td>
      <td>Organized project directories</td>
    </tr>
    <tr>
      <td>Rust Workspace Configuration</td>
      <td>Set up Cargo.toml for workspace</td>
      <td>Configured workspace for builds</td>
    </tr>
    <tr>
      <td>Toolchain Specification</td>
      <td>Define Rust version in rust-toolchain.toml</td>
      <td>Consistent toolchain setup</td>
    </tr>
    <tr>
      <td>Docker Compose Setup</td>
      <td>Configure services like Postgres, Redis, SigNoz</td>
      <td>Running third-party services</td>
    </tr>
    <tr>
      <td>Initial Schemas</td>
      <td>Create Postgres schemas for Crab-Shield, Crab-Hub</td>
      <td>Isolated database schemas</td>
    </tr>
    <tr>
      <td>Automation Scripts</td>
      <td>Create setup.sh, deploy.sh, backup.sh</td>
      <td>Automated processes for setup</td>
    </tr>
    <tr>
      <td>MinIO for Local Development</td>
      <td>Set up S3-compatible storage locally</td>
      <td>Local storage mirroring production</td>
    </tr>
    <tr>
      <td>Setup Verification</td>
      <td>Verify all components are correctly installed</td>
      <td>Confirmation of setup readiness</td>
    </tr>
  </tbody>
</table>
<p>This table summarizes the setup tasks, ensuring clarity and organization for implementation.</p>

