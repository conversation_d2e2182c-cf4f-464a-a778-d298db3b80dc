# Goals and Overview
Goals: Build a set of tools deployed to a VPS covering the most common cloud subscriptions that I would normally end up paying for:


## What this is designed to be:
1. A set of tools that I can use while in development, conveniently located on a remote VPS, enabling me to work anywhere.
2. A way to save money on the myriad of cloud services that I use. I don't like paying money for these services while my apps are in development.
3. This should be 100% Production stable, while we aren't seeking scale, we will need reliability. I'll be deploying to Contabo VPS, so I'm not expecting 4 nines of availability, but this should be as stable as we can get it.
4. This should be composable, meaning that if I wanted to use this as a base for a production server, I would deploy to a different VPS or cloud provider and using configuration, I could use the same tools to manage a production server with production resources.

## What this is NOT designed to be: 
1. Scalable, I don't need scalability for this server. This is supporting my development projects, not production workloads.
2. This doesn't need to be super easy to use or have a beautiful admin dashboard. I'm the only one using it.

## The Components & Services:
The tools that I will be relying on this VPS to provide are free, open sourced or built by myself.

### Data Layer: 
1. PostgresDB - A postgres instance with row-level security, logical replication and real-time data streaming. (Similar to Supabase)
2. Redis - A redis instance for caching, queueing, rate-limiting, and session management.
3. Object Storage - I'll use a 250GB object storage instance from contabo for this.

### Observability, Monitoring & Error Tracking: 
1. Prometheus - A prometheus instance for monitoring.
2. Grafana - A grafana instance for visualization.
3. Loki - A loki instance for logging.
4. Jaeger - A jaeger instance for tracing.
5. GlitchTip - A glitchtip instance for error tracking.

### Authentication & Authorization: 
1. I've built a custom rust authentication & authorization service That will secure this server as well as provide these services to other applications.

### Email Server: 
* I'm considering forking the stalwart email server since it is built in Rust and I can add the features I need to it. (Advanced AI and ML features).
  * I am concerned with their copy left license, and eventually I'll have my own server built out anyway. They also don't allow multi tenancy without an enterprise license.
* SMTP Relay - I will serve an SMTP relay for my applications to use while in development. For production, I will use a third party service.

### Dashboard:
This will be a next.js app deployed to Vercel but connected to this VPS to manage all of this:

#### Data Layer:
1. The ability to create and manage projects
2. Create new "virtual" databases for an app to use, managed by password and client IDs
3. I should be able to create a new Postgres Instance with a configuration that can be set to: 
  - Enable logical replication for that project
  - Enable real time data streaming via web sockets
4. Assign Redis Resources to the projects
5. Assign Object Storage Resources to the projects

#### Observability, Monitoring & Error Tracking:
1. Create a new project so that logs, etc are filtered to just that project, but these don't need isolation since it's just me using it
2. Track usage and view aggregate logs, etc for all of my projects. 
3. This is essentially just a filter applied to the data in the observability tools.

#### Authentication & Authorization:
1. The ability to create and manage projects, which will create and assign a new client ID and secret to the project
2. The ability to link to the projects's database for user tables, etc.

#### Email Server:
1. The ability to create and manage email accounts for the projects
2. The ability to view email logs and statistics for the project
3. The ability to view email logs and statistics for all projects

#### Domain and DNS Management: 
1. The ability to create and manage domains for the projects
2. The ability to manage DNS records for the domains
3. The ability to view DNS statistics for the domains
4. I'm not a registrar so domains would need to be pointed from the registrar to the VPS. How hard is it to have custom nameservers? Maybe use Cloudflare for this?

## Performance, etc
1. Database: This doesn't need to be super powerful, just enough to handle let's say 1000 daily users spread across all projects (this is likely overkill)
2. Redis: This won't be used a ton since there won't be many users, it's just a development instance.
3. Object Storage: 250GB should be plenty for this spread across all projects. This will store email attachments as the biggest use
4. SMTP Relay: This will handle my personal/work email using the SMTP service built into the server as well as maybe 2500 emails per day (again, overkill)
5. Inboxes: (15-25 inboxes) I don't need a ton of storage for this and I don't expect to see a ton of traffic on this since it's my use.

