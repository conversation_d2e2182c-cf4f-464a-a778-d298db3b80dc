# Phase 1: Foundation (Weeks 1-4)

## Version Information

### Core Dependencies
- **Rust**: 1.70+ (MSRV - Minimum Supported Rust Version)
- **Node.js**: 20.x LTS
- **PostgreSQL**: 15+
- **Redis**: 7.0+
- **Docker**: 24.0+ & Docker Compose: 2.20+

### Observability Stack
- **Grafana**: 12.0.1 (Visualization & Dashboards)
- **Prometheus**: 2.53.4 (Metrics Collection)
- **Loki**: 3.4.4 (Log Aggregation)
- **J<PERSON>ger**: 2.7.0 (Distributed Tracing)
- **OpenTelemetry**: 1.36.0 (Instrumentation)

### Development Tools
- **Rust Toolchain**: stable
- **Node.js Package Manager**: pnpm 8.x
- **Container Runtime**: Docker with BuildKit enabled
- **CI/CD**: GitHub Actions

## 1. Project Setup & CI/CD

### 1.1 Repository Structure
```
stalwart/
├── .github/
│   ├── workflows/          # GitHub Actions workflows
│   │   ├── ci.yml         # Continuous Integration
│   │   ├── cd.yml         # Continuous Deployment
│   │   └── release.yml    # Release management
│   └── dependabot.yml     # Dependabot configuration
├── apps/
│   ├── api/               # Backend services (Rust/Actix)
│   │   ├── src/          # Source code
│   │   ├── tests/         # Unit and integration tests
│   │   └── Cargo.toml     # Cargo manifest
│   └── web/               # Next.js frontend
│       ├── pages/         # Next.js pages
│       ├── public/        # Static assets
│       ├── src/           # Source code
│       └── package.json   # NPM dependencies
├── crates/                # Core Rust crates
│   ├── common/            # Shared utilities
│   ├── models/            # Data models
│   └── storage/           # Database abstractions
├── docs/                  # Documentation
│   ├── api/               # API documentation
│   ├── architecture/      # Architecture decisions
│   └── setup/             # Setup guides
├── infrastructure/        # Infrastructure as Code
│   ├── k8s/               # Kubernetes manifests
│   └── terraform/         # Cloud provisioning
└── tests/                 # Test suites
    ├── integration/       # Integration tests
    └── e2e/               # End-to-end tests
```

### 1.2 Development Environment Setup

#### Prerequisites
1. Install Rust:
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   rustup toolchain install stable
   rustup default stable
   rustup component add rustfmt clippy
   ```

2. Install Node.js (using nvm recommended):
   ```bash
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
   nvm install --lts
   nvm use --lts
   ```

3. Install Docker and Docker Compose:
   - [Docker Desktop](https://www.docker.com/products/docker-desktop/) (macOS/Windows)
   - Or use your system package manager for Linux

4. Install pnpm (faster alternative to npm):
   ```bash
   npm install -g pnpm
   ```

#### Local Development
1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/stalwart.git
   cd stalwart
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

3. Start development services:
   ```bash
   # Start database and other dependencies
   docker-compose -f docker-compose.dev.yml up -d
   
   # Run database migrations
   cd apps/api
   sqlx migrate run
   
   # Start the backend
   cargo watch -x run
   
   # In a new terminal, start the frontend
   cd apps/web
   pnpm install
   pnpm dev
   ```

### 1.3 CI/CD Pipeline

#### 1.3.1 GitHub Actions Workflow

##### CI Workflow (`.github/workflows/ci.yml`)
```yaml
name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4
    
    # Set up Rust
    - name: Set up Rust
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: stable
        components: rustfmt, clippy
    
    # Cache cargo dependencies
    - name: Cache cargo
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    
    # Set up Node.js
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'pnpm'
    
    # Install pnpm
    - name: Install pnpm
      run: npm install -g pnpm
    
    # Install frontend dependencies
    - name: Install frontend deps
      working-directory: apps/web
      run: pnpm install --frozen-lockfile
    
    # Run linters
    - name: Run linters
      run: |
        cargo fmt --check
        cargo clippy -- -D warnings
        cd apps/web && pnpm lint
    
    # Run tests
    - name: Run tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/test
      run: |
        # Run backend tests
        cargo test --workspace --no-fail-fast
        
        # Run frontend tests
        cd apps/web
        pnpm test:ci
        
        # Run E2E tests
        pnpm test:e2e

  build:
    name: Build
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    # Set up Docker Buildx
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    # Login to GitHub Container Registry
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    # Build and push Docker images
    - name: Build and push
      uses: docker/build-push-action@v5
      with:
        context: .
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ghcr.io/${{ github.repository }}/api:latest
          ghcr.io/${{ github.repository }}/api:${{ github.sha }}
        file: ./apps/api/Dockerfile
        build-args: |
          BUILDKIT_INLINE_CACHE=1
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

##### CD Workflow (`.github/workflows/cd.yml`)
```yaml
name: CD

on:
  push:
    branches: [main]
    paths:
      - 'apps/**'
      - '.github/workflows/cd.yml'
      - 'infrastructure/**'

jobs:
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    # Install Terraform
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.5.0
    
    # Configure AWS credentials
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    
    # Deploy infrastructure
    - name: Terraform Init
      working-directory: ./infrastructure
      run: |
        terraform init -backend-config="bucket=stalwart-tfstate" \
                      -backend-config="key=staging/terraform.tfstate" \
                      -backend-config="region=us-west-2"
    
    - name: Terraform Plan
      working-directory: ./infrastructure
      run: terraform plan -var="environment=staging"
    
    - name: Terraform Apply
      working-directory: ./infrastructure
      run: terraform apply -auto-approve -var="environment=staging"
    
    # Deploy application
    - name: Deploy to Kubernetes
      run: |
        echo "Deploying application to staging..."
        # Add deployment steps here
        kubectl apply -f ./infrastructure/k8s/staging/

  production:
    name: Deploy to Production
    needs: deploy
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Deploy to Production
      run: |
        echo "Production deployment would happen here after approval"
        # Requires manual approval in GitHub Environments
```

#### 1.3.2 Deployment Strategy

##### Staging Environment
- **Trigger**: On push to `main` branch
- **Infrastructure**: Managed by Terraform
- **Kubernetes**: Namespace `stalwart-staging`
- **Monitoring**: Full observability stack enabled
- **Access**: Internal team access only

##### Production Environment
- **Trigger**: Manual approval after successful staging deployment
- **Infrastructure**: Identical to staging, separate AWS account
- **Kubernetes**: Namespace `stalwart-production`
- **Monitoring**: Full observability with additional alerting
- **Rollback**: Automated rollback on health check failures

#### 1.3.3 Monitoring and Alerts

##### Prometheus Alerts
```yaml
# monitoring/alerts/prometheus/rules.yml
groups:
- name: stalwart
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
    for: 10m
    labels:
      severity: critical
    annotations:
      summary: High error rate on {{ $labels.instance }}
      description: "Error rate is {{ $value }}%"

  - alert: ServiceDown
    expr: up == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: Service {{ $labels.job }} is down
      description: "Instance {{ $labels.instance }} has been down for more than 5 minutes"
```

##### Grafana Dashboards
1. **API Dashboard**
   - Request rates
   - Error rates
   - Latency percentiles
   - Database query performance

2. **Infrastructure Dashboard**
   - CPU/Memory usage
   - Disk I/O
   - Network traffic
   - Container metrics

3. **Business Metrics**
   - Active users
   - API usage by endpoint
   - Tenant resource usage
   - Billing metrics

### 1.4 Testing Strategy

#### 1.4.1 Test Types and Coverage Requirements

| Test Type | Coverage Target | Execution Frequency | Description |
|-----------|-----------------|---------------------|-------------|
| **Unit Tests** | 100% for core modules | On every commit | Test individual functions and methods in isolation |
| **Integration Tests** | 80%+ for service boundaries | On every PR | Test interactions between components |
| **E2E Tests** | All critical user journeys | On merge to main | Test complete user flows |
| **Performance Tests** | Key performance metrics | Weekly | Ensure system meets performance SLOs |

#### 1.4.2 Test Implementation

##### Unit Tests
```rust
// Example unit test for authentication service
#[cfg(test)]
mod auth_service_tests {
    use super::*;
    use test_case::test_case;
    use mockall::predicate::*;
    use uuid::Uuid;

    #[test]
    fn test_jwt_token_generation() {
        let secret = "test_secret".to_string();
        let auth_service = AuthService::new(secret);
        let user_id = Uuid::new_v4();
        let tenant_id = Uuid::new_v4();
        
        let token = auth_service.generate_token(user_id, tenant_id).unwrap();
        let claims = auth_service.validate_token(&token).unwrap();
        
        assert_eq!(claims.sub, user_id);
        assert_eq!(claims.tenant_id, tenant_id);
    }

    #[test_case("<EMAIL>", "Str0ngP@ss!", true; "valid credentials")]
    #[test_case("<EMAIL>", "wrong", false; "invalid credentials")]
    fn test_authentication(email: &str, password: &str, expected: bool) {
        let mut mock_repo = MockUserRepository::new();
        mock_repo
            .expect_find_by_email()
            .with(eq("<EMAIL>"))
            .returning(|_| Some(User {
                id: Uuid::new_v4(),
                email: "<EMAIL>".to_string(),
                password_hash: hash_password("Str0ngP@ss!").unwrap(),
                // ... other fields
            }));
            
        let auth_service = AuthService::new(mock_repo);
        let result = auth_service.authenticate(email, password).is_ok();
        assert_eq!(result, expected);
    }
}
```

##### Integration Tests
```rust
// tests/integration/auth_flow_test.rs
use testcontainers::{clients, images::postgres::Postgres};
use sqlx::postgres::PgPoolOptions;
use stalwart_api::{
    app::create_app,
    models::{NewUser, UserRole},
};
use axum::{
    body::Body,
    http::{self, Request, StatusCode},
};
use serde_json::json;
use tower::ServiceExt;

#[tokio::test]
async fn test_registration_flow() {
    // Setup test database
    let docker = clients::Cli::default();
    let pg = docker.run(Postgres::default());
    let db_url = format!(
        "postgres://postgres:postgres@localhost:{}/test",
        pg.get_host_port_ipv4(5432)
    );
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&db_url).await.unwrap();
    
    // Run migrations
    sqlx::migrate!("./migrations")
        .run(&pool)
        .await
        .unwrap();
    
    // Create test app
    let app = create_app(pool.clone());
    
    // Test registration
    let response = app
        .oneshot(
            Request::builder()
                .method(http::Method::POST)
                .uri("/api/v1/auth/register")
                .header(http::header::CONTENT_TYPE, mime::APPLICATION_JSON.as_ref())
                .body(Body::from(
                    serde_json::to_vec(&json!({
                        "email": "<EMAIL>",
                        "password": "Test@1234",
                        "tenant_id": "550e8400-e29b-41d4-a716-446655440000"
                    }))
                    .unwrap(),
                ))
                .unwrap(),
        )
        .await
        .unwrap();
    
    assert_eq!(response.status(), StatusCode::CREATED);
    
    // Verify user was created
    let user = sqlx::query_as!(
        NewUser,
        "SELECT * FROM users WHERE email = $1",
        "<EMAIL>"
    )
    .fetch_one(&pool)
    .await
    .unwrap();
    
    assert_eq!(user.email, "<EMAIL>");
}
```

##### E2E Tests with Playwright
```typescript
// tests/e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('user can register and login', async ({ page }) => {
    // Navigate to registration page
    await page.goto('/register');
    
    // Fill registration form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'Test@1234');
    await page.fill('input[name="confirmPassword"]', 'Test@1234');
    await page.click('button[type="submit"]');
    
    // Verify successful registration
    await expect(page).toHaveURL('/dashboard');
    
    // Logout
    await page.click('text=Logout');
    
    // Login with new credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'Test@1234');
    await page.click('button[type="submit"]');
    
    // Verify successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Welcome, <EMAIL>')).toBeVisible();
  });
});
```

#### 1.4.3 Performance Testing

##### K6 Load Test Example
```javascript
// tests/load/auth_flow.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

export const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '30s', target: 20 },  // Ramp up to 20 users
    { duration: '1m', target: 50 },   // Stay at 50 users
    { duration: '30s', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],  // 95% of requests under 500ms
    'errors': ['rate<0.1'],           // <10% errors
  },
};

export default function () {
  const url = 'https://api.stalwart.test/v1/auth/login';
  const payload = JSON.stringify({
    email: '<EMAIL>',
    password: 'Test@1234',
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const res = http.post(url, payload, params);
  
  // Check for successful response
  const success = check(res, {
    'status is 200': (r) => r.status === 200,
    'has token': (r) => r.json('token') !== undefined,
  });
  
  // Record errors
  if (!success) {
    errorRate.add(1);
  }

  sleep(1);
}
```

#### 1.4.4 Test Coverage Reporting

##### Rust Code Coverage
```yaml
# .github/workflows/coverage.yml
name: Code Coverage

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Install Rust toolchain
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: nightly
        components: llvm-tools-preview, rustfmt, clippy
    - name: Install cargo-llvm-cov
      uses: taiki-e/install-action@v2
      with:
        tool: cargo-llvm-cov
    - name: Generate coverage report
      uses: actions-rs/cargo@v1
      with:
        command: llvm-cov
        args: --all-features --workspace --lcov --output-path lcov.info
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./lcov.info
        fail_ci_if_error: true
```

#### 1.4.5 Test Data Management

##### Factory Pattern for Test Data
```rust
// tests/factories/user_factory.rs
use fake::{Fake, Faker};
use uuid::Uuid;
use crate::models::{NewUser, UserRole};

pub struct UserFactory;

impl UserFactory {
    pub fn new_user() -> NewUser {
        let email: String = fake::faker::internet::en::FreeEmail().fake();
        let password_hash = "$argon2id$v=19$m=19456,t=2,p=1$".to_string() + 
            &base64::encode(&[0u8; 16]) + "$" + 
            &base64::encode(&[0u8; 32]);
            
        NewUser {
            id: Uuid::new_v4(),
            email,
            password_hash,
            role: UserRole::User,
            tenant_id: Uuid::new_v4(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }
    
    pub fn admin() -> NewUser {
        let mut user = Self::new_user();
        user.role = UserRole::Admin;
        user
    }
}
```

##### Database Transaction Rollback
```rust
// tests/test_utils/mod.rs
use sqlx::{Pool, Postgres, Transaction};
use std::sync::Once;
use testcontainers::{clients, images::postgres::Postgres as PostgresImage};

static INIT: Once = Once::new();

pub struct TestDb {
    _pg_container: testcontainers::Container<'static, PostgresImage>,
    pub pool: Pool<Postgres>,
}

impl TestDb {
    pub async fn new() -> Self {
        INIT.call_once(|| {
            dotenv::dotenv().ok();
            env_logger::init();
        });

        let docker = clients::Cli::default();
        let pg = docker.run(PostgresImage::default());
        let port = pg.get_host_port_ipv4(5432);
        
        let database_url = format!(
            "postgres://postgres:postgres@localhost:{}/test",
            port
        );
        
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(5)
            .connect(&database_url)
            .await
            .expect("Failed to connect to test database");
            
        // Run migrations
        sqlx::migrate!("./migrations")
            .run(&pool)
            .await
            .expect("Failed to run migrations");
            
        TestDb {
            _pg_container: pg,
            pool,
        }
    }
    
    pub async fn begin_transaction(&self) -> Transaction<'_, Postgres> {
        self.pool.begin().await.unwrap()
    }
}
```

## 2. Authentication Requirements

This section outlines the authentication and authorization requirements that must be fulfilled by the external authentication service. The email server will integrate with this service to handle all authentication-related functionality.

### 2.1 Core Requirements

#### 2.1.1 Authentication Methods
- **JWT-based authentication** with short-lived access tokens and long-lived refresh tokens
- **Multi-factor authentication (MFA)** with TOTP support (e.g., Google Authenticator, Authy)
- **Session management** with configurable timeouts and concurrent session limits
- **Password-based authentication** with secure password policies
- **API key authentication** for service-to-service communication

#### 2.1.2 Security Requirements
- **Password Security**:
  - Minimum 12 characters
  - Require uppercase, lowercase, number, and special character
  - Argon2id hashing with appropriate work factors
  - Check against known data breaches

- **Token Security**:
  - Short-lived access tokens (15 minutes recommended)
  - Long-lived refresh tokens (7 days recommended)
  - Token rotation on use
  - Token binding to user agent and IP

- **Rate Limiting**:
  - Per-IP rate limiting for authentication endpoints
  - Account-level rate limiting
  - Configurable limits and lockout policies

### 2.2 Integration Interface

The external auth service must provide the following interfaces for the email server:

#### 2.2.1 Authentication Middleware
- Validate JWT tokens in API requests
- Extract and provide user/tenant context to the email server
- Handle token refresh flows

#### 2.2.2 User Management
- User registration and profile management
- Password reset flows
- MFA setup and management
- Session management (view/revoke active sessions)

#### 2.2.3 API Endpoints

```http
# User Authentication
POST /api/v1/auth/login
POST /api/v1/auth/refresh
POST /api/v1/auth/logout

# User Management
POST /api/v1/auth/register
POST /api/v1/auth/reset-password
POST /api/v1/auth/forgot-password

# MFA Management
POST /api/v1/auth/mfa/setup
POST /api/v1/auth/mfa/verify
POST /api/v1/auth/mfa/disable

# Session Management
GET    /api/v1/auth/sessions
DELETE /api/v1/auth/sessions/{session_id}
```

### 2.3 Security Headers

The auth service must include the following security headers in all responses:

```
Content-Security-Policy: default-src 'self'
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

### 2.4 Error Handling

Standard error response format:
```json
{
  "error": {
    "code": "error_code",
    "message": "Human-readable error message",
    "details": {}
  }
}
```

### 2.5 Monitoring and Logging

- Log all authentication attempts (success/failure)
- Log security-relevant events (password changes, MFA setup, etc.)
- Provide metrics for monitoring (login attempts, token issues, etc.)
- Support for audit logging of all administrative actions

### 2.6 Integration Testing Requirements

The external auth service should provide test utilities and mocks to facilitate integration testing. These should include:

1. **Test Users and Credentials**
   - Pre-configured test users with various permission levels
   - Test API keys with different scopes
   - Test MFA configurations

2. **Mock Endpoints**
   - Simulate successful/failed authentication
   - Test rate limiting behavior
   - Simulate various error conditions

3. **Test Fixtures**
   - Sample JWTs for testing
   - Test MFA codes
   - Test session tokens

### 2.7 Performance Requirements

The auth service should meet the following performance characteristics:

1. **Response Times**
   - Authentication: < 100ms p99
   - Token validation: < 10ms p99
   - User lookup: < 20ms p99

2. **Scalability**
   - Support 1000+ requests per second
   - Horizontal scaling capability
   - Caching of frequently accessed data

3. **Availability**
   - 99.9% uptime SLA
   - Graceful degradation under load
   - Automatic failover capabilities
```

## 3. Multi-tenancy Architecture

### 3.1 Tenant Identification

Tenants are identified using one of the following methods, in order of precedence:

1. **JWT Claim**: `tenant_id` claim in the authentication token
2. **HTTP Header**: `X-Tenant-ID` header
3. **Subdomain**: First part of the hostname (e.g., `tenant1.email.example.com`)
4. **URL Path**: First segment of the path (e.g., `example.com/tenant1/api/...`)

### 3.2 Data Model

#### 3.2.1 Core Entities

```mermaid
erDiagram
    TENANT ||--o{ USER : has
    TENANT ||--o{ DOMAIN : has
    TENANT ||--o{ MAILBOX : has
    TENANT ||--o{ QUOTA : has
    
    TENANT {
        uuid id
        string name
        string status
        timestamp created_at
        timestamp updated_at
        jsonb settings
    }
    
    USER {
        uuid id
        uuid tenant_id
        string email
        string password_hash
        string status
        jsonb metadata
        timestamp last_login_at
        timestamp created_at
        timestamp updated_at
    }
    
    DOMAIN {
        uuid id
        uuid tenant_id
        string domain_name
        boolean is_primary
        string verification_status
        timestamp created_at
        timestamp updated_at
    }
    
    MAILBOX {
        uuid id
        uuid tenant_id
        string name
        string path
        bigint storage_used
        timestamp created_at
        timestamp updated_at
    }
    
    QUOTA {
        uuid id
        uuid tenant_id
        string resource_type
        bigint limit
        string period
        bigstring usage
        timestamp reset_at
    }
```

### 3.3 Tenant Isolation

#### 3.3.1 Database Level

1. **PostgreSQL Row Level Security (RLS)**
   - All tables with tenant-specific data include `tenant_id`
   - RLS policies enforce tenant isolation
   - Example policy for the `emails` table:
   
   ```sql
   CREATE POLICY tenant_isolation_policy ON emails
       USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
   ```

2. **Database Connections**
   - Connection pool per tenant for strict isolation
   - Connection string includes tenant context
   - Connection limits per tenant to prevent resource exhaustion

#### 3.3.2 Application Level

1. **Tenant Context**
   - Thread-local storage for tenant context
   - Automatic tenant ID extraction from requests
   - Middleware to validate tenant access

2. **Caching**
   - Tenant-aware cache keys
   - Separate cache namespaces per tenant
   - Cache invalidation on tenant data changes

### 3.4 Resource Management

#### 3.4.1 Quotas and Limits

| Resource Type | Default Limit | Enforced At | Override |
|---------------|---------------|-------------|----------|
| Storage | 10 GB | Write | Per tenant |
| API Requests | 1000/min | API Gateway | Per API key |
| Email Sending | 1000/day | SMTP | Per user |
| Mailboxes | 10 | Create | Per tenant |
| Aliases | 100 | Create | Per domain |

#### 3.4.2 Rate Limiting

- **Per-tenant rate limits**
- **Per-user rate limits**
- **Per-IP rate limits**
- **Burst protection**

### 3.5 Cross-Tenant Operations

1. **Tenant Administration**
   - Super admin can access all tenants
   - Audit logs for all cross-tenant operations
   - Approval workflow for sensitive operations

2. **Data Export/Import**
   - Tenant data export in standard formats
   - Data import with validation
   - Background processing for large datasets

### 3.6 Testing Requirements

#### 3.6.1 Test Scenarios

1. **Isolation Tests**
   - Verify data cannot be accessed across tenants
   - Test RLS policies with different user roles
   - Verify cache isolation

2. **Performance Tests**
   - Measure performance with multiple active tenants
   - Test resource allocation under load
   - Verify quota enforcement

3. **Security Tests**
   - Attempt cross-tenant data access
   - Test tenant ID spoofing
   - Verify audit logging

#### 3.6.2 Test Data

- Create test tenants with various configurations
- Generate realistic tenant data volumes
- Include edge cases (e.g., tenants at quota limits)

## 4. Observability Stack

### 4.1 Architecture Overview

```mermaid
graph TD
    subgraph Email Server
        A[Application] -->|Metrics| B(Prometheus)
        A -->|Logs| C(Loki)
        A -->|Traces| D(Jaeger)
    end
    
    subgraph Monitoring
        B --> E[Grafana]
        C --> E
        D --> E
    end
    
    E --> F[Alerts]
    F --> G[Email/Slack/PagerDuty]
```

### 4.2 Components

#### 4.2.1 Metrics (Prometheus)

**Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'email-server'
    static_configs:
      - targets: ['email-server:9100']
    metrics_path: '/metrics'
    scheme: 'http'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

**Key Metrics to Monitor**

| Category | Metric | Description | Alert Threshold |
|----------|--------|-------------|-----------------|
| HTTP | http_requests_total | Total HTTP requests | N/A |
| HTTP | http_request_duration_seconds | Request duration | p99 > 1s |
| Database | db_queries_total | Total DB queries | N/A |
| Database | db_query_duration_seconds | Query duration | p99 > 500ms |
| System | process_cpu_seconds_total | CPU usage | > 80% for 5m |
| System | process_resident_memory_bytes | Memory usage | > 90% of limit |
| SMTP | smtp_sessions_total | Active SMTP sessions | N/A |
| SMTP | smtp_commands_total | SMTP commands processed | N/A |
| IMAP | imap_sessions_total | Active IMAP sessions | N/A |
| IMAP | imap_commands_total | IMAP commands processed | N/A |

#### 4.2.2 Logging (Loki)

**Configuration**
```yaml
# promtail-config.yaml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/**/*.log
```

**Log Structure**
```json
{
  "timestamp": "2025-03-20T14:30:45Z",
  "level": "info",
  "message": "Request processed",
  "request_id": "abc123",
  "tenant_id": "tenant-123",
  "user_id": "user-456",
  "method": "GET",
  "path": "/api/messages",
  "status": 200,
  "duration_ms": 45,
  "ip": "***********",
  "user_agent": "Mozilla/5.0..."
}
```

#### 4.2.3 Tracing (Jaeger)

**Configuration**
```yaml
# jaeger-config.yaml
sampler:
  type: const
  param: 1
  samplingServerURL: http://jaeger:5778/sampling

es:
  server-urls: http://elasticsearch:9200
  username: ""
  password: ""
  tags: {}
```

**Trace Context Propagation**
- W3C Trace Context
- B3 Propagation
- Jaeger Propagation

### 4.3 Dashboards (Grafana)

**Required Dashboards**

1. **Service Health**
   - Uptime
   - Error rates
   - Request rates
   - Latency percentiles

2. **Resource Usage**
   - CPU/Memory/Disk usage
   - Network I/O
   - File descriptors
   - Thread count

3. **Email Processing**
   - Emails processed/sec
   - Queue sizes
   - Processing time
   - Error rates

4. **Database**
   - Query performance
   - Connection pool
   - Cache hit ratio
   - Replication lag

5. **Tenant Metrics**
   - Active tenants
   - Resource usage by tenant
   - API usage by tenant
   - Quota utilization

### 4.4 Alerting

#### 4.4.1 Alert Rules

```yaml
groups:
- name: email-server.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
    for: 10m
    labels:
      severity: critical
    annotations:
      summary: "High error rate on {{ $labels.instance }}"
      description: "Error rate is {{ $value }}%"

  - alert: HighLatency
    expr: histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High latency on {{ $labels.instance }}"
      description: "99th percentile latency is {{ $value }}s"
```

#### 4.4.2 Notification Channels
- Email
- Slack
- PagerDuty
- Webhooks

### 4.5 Performance Considerations

1. **Cardinality Management**
   - Limit label cardinality
   - Use histograms for high-cardinality data
   - Drop unnecessary metrics

2. **Sampling**
   - Sample high-volume traces
   - Dynamic sampling based on error rates
   - Head-based sampling for consistency

3. **Retention**
   - Metrics: 30 days
   - Logs: 7 days
   - Traces: 3 days
   - Long-term storage for aggregates

### 4.6 Security

1. **Authentication**
   - OAuth2 for Grafana
   - mTLS between components
   - Role-based access control (RBAC)

2. **Data Protection**
   - Redact sensitive data in logs
   - Mask PII in traces
   - Encrypt data at rest and in transit

3. **Audit Logging**
   - Log all configuration changes
   - Track access to sensitive data
   - Retain audit logs for 1 year

## 5. Acceptance Criteria

### 5.1 Functional Requirements

#### 5.1.1 Authentication & Authorization
- [ ] Implement JWT-based authentication with access and refresh tokens
- [ ] Support multi-factor authentication (TOTP)
- [ ] Enforce role-based access control (RBAC)
- [ ] Provide API key management
- [ ] Support OAuth2/OIDC integration
- [ ] Implement secure password policies

#### 5.1.2 Multi-tenancy
- [ ] Isolate tenant data with RLS policies
- [ ] Support custom domains per tenant
- [ ] Enforce resource quotas
- [ ] Provide tenant management APIs
- [ ] Support tenant-specific configurations

#### 5.1.3 Email Processing
- [ ] Handle SMTP/IMAP protocols
- [ ] Process incoming/outgoing emails
- [ ] Support email filtering and rules
- [ ] Implement spam/virus scanning
- [ ] Provide email search functionality

### 5.2 Non-Functional Requirements

#### 5.2.1 Performance
| Metric | Target | Measurement |
|--------|--------|-------------|
| API Response Time | p99 < 500ms | Prometheus |
| Email Delivery | < 5s for 99% of emails | Application Logs |
| Authentication | < 200ms p99 | Jaeger Traces |
| Database Queries | < 100ms p99 | pg_stat_statements |
| Concurrent Users | 10,000+ | Load Testing |

#### 5.2.2 Reliability
- [ ] 99.9% uptime SLA
- [ ] Automated failover
- [ ] Data backup and recovery
- [ ] Graceful degradation
- [ ] Circuit breakers for external services

#### 5.2.3 Security
- [ ] Regular security audits
- [ ] Penetration testing
- [ ] SOC 2 Compliance
- [ ] Data encryption at rest and in transit
- [ ] Regular dependency updates

### 5.3 Quality Gates

#### 5.3.1 Testing
- [ ] 90%+ code coverage
- [ ] No critical/high severity bugs
- [ ] Performance test suite
- [ ] Security scan clean
- [ ] Documentation coverage

#### 5.3.2 Deployment
- [ ] Zero-downtime deployments
- [ ] Blue/green deployment support
- [ ] Automated rollback on failure
- [ ] Environment parity
- [ ] Infrastructure as Code

### 5.4 Documentation

#### 5.4.1 Technical Documentation
- [ ] API Reference (OpenAPI)
- [ ] Architecture Decision Records (ADRs)
- [ ] Deployment guides
- [ ] Monitoring and alerting guide
- [ ] Troubleshooting guide

#### 5.4.2 User Documentation
- [ ] Getting started guide
- [ ] Admin manual
- [ ] End-user guide
- [ ] FAQ and best practices
- [ ] Video tutorials

### 5.5 Success Metrics

| Metric | Target | Measurement Period |
|--------|--------|-------------------|
| Uptime | 99.9% | Monthly |
| Mean Time to Detect (MTTD) | < 5 minutes | Monthly |
| Mean Time to Resolve (MTTR) | < 30 minutes | Monthly |
| User Satisfaction | > 4.5/5 | Quarterly |
| Feature Adoption | > 70% | Quarterly |

### 5.6 Exit Criteria

1. **Code Complete**
   - All features implemented
   - All tests passing
   - Documentation updated
   - Performance benchmarks met

2. **QA Sign-off**
   - Test cases executed
   - Critical bugs resolved
   - Performance verified
   - Security review completed

3. **Stakeholder Approval**
   - Product owner sign-off
   - Security team approval
   - Operations team ready
   - Support team trained

## 6. API Reference

### 6.1 Base URL
All API endpoints are relative to the base URL:
```
https://api.email.example.com/v1
```

### 6.2 Authentication
All API requests must include an `Authorization` header with a valid JWT token:
```
Authorization: Bearer <jwt_token>
```

### 6.3 Common Headers
```http
Accept: application/json
Content-Type: application/json
X-Request-ID: <uuid>
```

### 6.4 Response Format
All successful responses follow this format:
```json
{
  "data": {},
  "meta": {
    "request_id": "<uuid>",
    "timestamp": "2025-03-20T12:00:00Z"
  }
}
```

Error responses:
```json
{
  "error": {
    "code": "invalid_request",
    "message": "Invalid request parameters",
    "details": {
      "field": "email",
      "issue": "invalid_format"
    },
    "request_id": "<uuid>"
  }
}
```

### 6.5 Authentication Endpoints

#### 6.5.1 Login
```http
POST /auth/login
```

**Request**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "mfa_code": "123456"
}
```

**Response**
```json
{
  "data": {
    "access_token": "<jwt_token>",
    "refresh_token": "<refresh_token>",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}
```

### 6.6 User Management

#### 6.6.1 Get Current User
```http
GET /users/me
```

**Response**
```json
{
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "admin",
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

### 6.7 Tenant Management

#### 6.7.1 List Tenants
```http
GET /tenants
```

**Query Parameters**
- `page` (number): Page number (default: 1)
- `per_page` (number): Items per page (default: 20, max: 100)

**Response**
```json
{
  "data": [
    {
      "id": "tenant_123",
      "name": "Acme Corp",
      "status": "active",
      "created_at": "2025-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}
```

### 6.8 Email Management

#### 6.8.1 Send Email
```http
POST /emails
```

**Request**
```json
{
  "to": ["<EMAIL>"],
  "cc": ["<EMAIL>"],
  "bcc": ["<EMAIL>"],
  "subject": "Hello, World!",
  "text": "This is a test email.",
  "html": "<p>This is a <strong>test</strong> email.</p>",
  "attachments": [
    {
      "filename": "document.pdf",
      "content_type": "application/pdf",
      "content": "<base64_encoded_content>"
    }
  ]
}
```

**Response**
```json
{
  "data": {
    "id": "msg_123",
    "status": "queued",
    "created_at": "2025-03-20T12:00:00Z"
  }
}
```

### 6.9 Webhooks

#### 6.9.1 Create Webhook
```http
POST /webhooks
```

**Request**
```json
{
  "url": "https://example.com/webhook",
  "events": ["email.delivered", "email.failed"],
  "secret": "webhook_secret_123"
}
```

**Response**
```json
{
  "data": {
    "id": "wh_123",
    "url": "https://example.com/webhook",
    "events": ["email.delivered", "email.failed"],
    "created_at": "2025-03-20T12:00:00Z"
  }
}
```

### 6.10 Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| /auth/* | 5 requests | 1 minute |
| /users/* | 100 requests | 1 minute |
| /tenants/* | 50 requests | 1 minute |
| /emails/* | 200 requests | 1 minute |
| /webhooks/* | 20 requests | 1 minute |

### 6.11 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Unprocessable Entity |
| 429 | Too Many Requests |
| 500 | Internal Server Error |
| 503 | Service Unavailable |

## 7. Test Plan

### 6.1 Unit Tests
- Core authentication logic
- Tenant isolation
- Data access layers

### 6.2 Integration Tests
- Service communication
- Database interactions
- API endpoints

### 6.3 E2E Tests
- User registration flow
- Multi-tenant isolation
- Authentication scenarios

## 7. Risk Assessment

| Risk | Impact | Mitigation |
|------|--------|------------|
| Authentication bypass | Critical | Regular security audits, penetration testing |
| Tenant data leakage | Critical | Row-level security, encryption |
| Performance degradation | High | Load testing, monitoring |

## 8. Dependencies
- Rust 1.70+
- PostgreSQL 15+
- Docker & Kubernetes
- Prometheus stack

## 9. Out of Scope
- Advanced user management
- Billing integration
- Advanced monitoring rules
