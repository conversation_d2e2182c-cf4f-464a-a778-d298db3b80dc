# Phase 2: Core Services (Weeks 5-10)

## 1. SMTP Relay Service

### 1.1 Features
- REST & gRPC APIs
- Rate limiting per application
- Webhook notifications
- Usage analytics

### 1.2 Test Strategy
```rust
#[cfg(test)]
mod smtp_tests {
    use super::*;
    use testcontainers::{clients, images::generic::GenericImage};
    use tokio::net::TcpStream;
    use tokio_test::assert_ok;

    #[tokio::test]
    async fn test_smtp_flow() {
        // Test complete SMTP transaction
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        // Verify rate limiting works
    }
}
```

## 2. ML Pipeline

### 2.1 Components
- Email classification
- Sentiment analysis
- Entity extraction
- Spam detection

### 2.2 Testing Approach
- Unit tests for individual models
- Integration tests with mock data
- Performance benchmarks
- Model accuracy validation

## 3. Next.js Admin Interface

### 3.1 Key Screens
- Dashboard
- User management
- System health
- Logs viewer

### 3.2 Test Coverage
```typescript
describe('Admin Dashboard', () => {
  beforeAll(async () => {
    await page.goto('/admin');
  });

  test('loads dashboard', async () => {
    await expect(page).toHaveTitle(/Dashboard/);
  });

  test('displays system metrics', async () => {
    const metrics = await page.$$eval('.metric', els => 
      els.map(e => e.textContent)
    );
    expect(metrics.length).toBeGreaterThan(0);
  });
});
```

## 4. Monitoring Dashboards

### 4.1 Key Metrics
- Request rates
- Error rates
- System resources
- Business metrics

### 4.2 Alert Rules
- Error budget burn rate
- P99 latency
- Resource saturation

## 5. Test Automation

### 5.1 Test Pyramid
```mermaid
pie
    title Test Distribution
    "Unit Tests" : 70
    "Integration Tests" : 20
    "E2E Tests" : 10
```

### 5.2 Coverage Requirements
- 100% critical paths
- 90%+ branch coverage
- 85%+ line coverage

## 6. Performance Testing

### 6.1 Scenarios
- Peak load simulation
- Long-running tests
- Failure mode analysis

### 6.2 Success Criteria
- 99.9% uptime
- <100ms p99 latency
- Graceful degradation

## 7. Security Testing

### 7.1 Scans
- SAST/DAST
- Dependency scanning
- Secrets detection

### 7.2 Penetration Tests
- OWASP Top 10
- Authentication bypass attempts
- Data leakage checks

## 8. Documentation

### 8.1 Developer Docs
- API references
- Architecture decisions
- Setup guides

### 8.2 Runbooks
- Incident response
- Performance tuning
- Scaling procedures

## 9. Exit Criteria

### 9.1 Must Have
- [ ] SMTP relay with 100% test coverage
- [ ] ML pipeline with validation
- [ ] Admin interface (core features)
- [ ] Monitoring & alerting

### 9.2 Nice to Have
- [ ] Advanced analytics
- [ ] Custom dashboard widgets
- [ ] Automated performance tests

## 10. Risk Mitigation

| Risk | Impact | Mitigation |
|------|--------|------------|
| SMTP relay abuse | High | Rate limiting, IP blocking |
| Model drift | Medium | Continuous retraining |
| UI inconsistencies | Low | Design system, visual regression |

## 11. Dependencies
- ML model training data
- Monitoring infrastructure
- Design system components

## 12. Out of Scope
- Advanced ML features
- Custom report generation
- Third-party integrations
