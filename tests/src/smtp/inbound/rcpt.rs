/*
 * SPDX-FileCopyrightText: 2020 Stalwart Labs LLC <<EMAIL>>
 *
 * SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-SEL
 */

use std::time::Duration;

use common::Core;

use smtp_proto::{RCPT_NOTIFY_DELAY, RCPT_NOTIFY_FAILURE, RCPT_NOTIFY_SUCCESS};
use store::Stores;
use utils::config::Config;

use smtp::core::{Session, State};

use crate::smtp::{
    TempDir, TestSMTP,
    session::{TestSession, VerifyResponse},
};

const CONFIG: &str = r#"
[storage]
data = "rocksdb"
lookup = "rocksdb"
blob = "rocksdb"
fts = "rocksdb"

[store."rocksdb"]
type = "rocksdb"
path = "{TMP}/queue.db"

[directory."local"]
type = "memory"

[[directory."local".principals]]
name = "john"
description = "<PERSON>"
secret = "secret"
email = "<EMAIL>"

[[directory."local".principals]]
name = "jane"
description = "<PERSON>"
secret = "p4ssw0rd"
email = "<EMAIL>"

[[directory."local".principals]]
name = "bill"
description = "Bill Foobar"
secret = "p4ssw0rd"
email = "<EMAIL>"

[[directory."local".principals]]
name = "mike"
description = "Mike Foobar"
secret = "p4ssw0rd"
email = "<EMAIL>"

[session.rcpt]
directory = "'local'"
max-recipients = [{if = "remote_ip = '********'", then = 3},
                {else = 5}]
relay = [{if = "remote_ip = '********'", then = false},
         {else = true}]

[session.rcpt.errors]
total = [{if = "remote_ip = '********'", then = 3},
         {else = 100}]
wait = [{if = "remote_ip = '********'", then = '5ms'},
        {else = '1s'}]

[session.extensions]
dsn = [{if = "remote_ip = '********'", then = false},
       {else = true}]

[[queue.limiter.inbound]]
match = "remote_ip = '********' && !is_empty(rcpt)"
key = 'sender'
rate = '2/1s'
enable = true

"#;

#[tokio::test]
async fn rcpt() {
    // Enable logging
    crate::enable_logging();

    let tmp_dir = TempDir::new("smtp_rcpt_test", true);
    let mut config = Config::new(tmp_dir.update_config(CONFIG)).unwrap();
    let stores = Stores::parse_all(&mut config, false).await;
    let core = Core::parse(&mut config, stores, Default::default()).await;

    // RCPT without MAIL FROM
    let mut session = Session::test(TestSMTP::from_core(core).server);
    session.data.remote_ip_str = "********".into();
    session.eval_session_params().await;
    session.ehlo("mx1.foobar.org").await;
    session.rcpt_to("<EMAIL>", "503 5.5.1").await;

    // Relaying is disabled for ********
    session.mail_from("<EMAIL>", "250").await;
    session.rcpt_to("<EMAIL>", "550 5.1.2").await;

    // DSN is disabled for ********
    session
        .ingest(b"RCPT TO:<<EMAIL>> NOTIFY=SUCCESS,FAILURE,DELAY\r\n")
        .await
        .unwrap();
    session.response().assert_code("501 5.5.4");

    // Send to non-existing user
    session.rcpt_to("<EMAIL>", "550 5.1.2").await;

    // Exceeding max number of errors
    session
        .ingest(b"RCPT TO:<<EMAIL>>\r\n")
        .await
        .unwrap_err();
    session.response().assert_code("451 4.3.0");

    // Rate limit
    session.data.rcpt_errors = 0;
    session.state = State::default();
    session.rcpt_to("<EMAIL>", "250").await;
    session.rcpt_to("<EMAIL>", "250").await;
    session.rcpt_to("<EMAIL>", "452 4.4.5").await;

    // Restore rate limit
    tokio::time::sleep(Duration::from_millis(1100)).await;
    session.rcpt_to("<EMAIL>", "250").await;
    session.rcpt_to("<EMAIL>", "455 4.5.3").await;

    // Check recipients
    assert_eq!(session.data.rcpt_to.len(), 3);
    for (rcpt, expected) in
        session
            .data
            .rcpt_to
            .iter()
            .zip(["<EMAIL>", "<EMAIL>", "<EMAIL>"])
    {
        assert_eq!(rcpt.address, expected);
        assert_eq!(rcpt.domain, "foobar.org");
        assert_eq!(rcpt.address_lcase, expected.to_lowercase());
    }

    // Relaying should be allowed for ********
    session.data.remote_ip_str = "********".into();
    session.eval_session_params().await;
    session.rset().await;
    session.mail_from("<EMAIL>", "250").await;
    session.rcpt_to("<EMAIL>", "250").await;

    // DSN is enabled for ********
    session
        .ingest(b"RCPT TO:<<EMAIL>> NOTIFY=SUCCESS,FAILURE,DELAY ORCPT=rfc822;<EMAIL>\r\n")
        .await
        .unwrap();
    session.response().assert_code("250");
    let rcpt = session.data.rcpt_to.last().unwrap();
    assert!((rcpt.flags & (RCPT_NOTIFY_DELAY | RCPT_NOTIFY_SUCCESS | RCPT_NOTIFY_FAILURE)) != 0);
    assert_eq!(rcpt.dsn_info.as_ref().unwrap(), "<EMAIL>");
}
