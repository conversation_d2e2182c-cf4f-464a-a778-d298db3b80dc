/*
 * SPDX-FileCopyrightText: 2020 Stalwart Labs LLC <<EMAIL>>
 *
 * SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-SEL
 */

use common::Core;

use smtp::core::Session;
use utils::config::Config;

use crate::smtp::{TestSMTP, session::TestSession};

const CONFIG: &str = r#"
[session.mail]
rewrite = [ { if = "ends_with(sender_domain, '.foobar.net') & matches('^([^.]+)@([^.]+)\.(.+)$', sender)", then = "$1 + '+' + $2 + '@' + $3"},
            { else = false } ]
script = [ { if = "sender_domain = 'foobar.org'", then = "'mail'" }, 
            { else = false } ]

[session.rcpt]
rewrite = [ { if = "rcpt_domain = 'foobar.net' & matches('^([^.]+)\\.([^.]+)@(.+)$', rcpt)", then = "$1 + '+' + $2 + '@' + $3"},
            { else = false } ]
script = [ { if = "rcpt_domain = 'foobar.org'", then = "'rcpt'" }, 
            { else = false } ]
relay = true

[sieve.trusted]
from-name = "Sieve Daemon"
from-addr = "<EMAIL>"
return-path = ""
hostname = "mx.foobar.org"

[sieve.trusted.limits]
redirects = 3
out-messages = 5
received-headers = 50
cpu = 10000
nested-includes = 5
duplicate-expiry = "7d"

[sieve.trusted.scripts."mail"]
contents = '''
require ["variables", "envelope"];

if allof( envelope :domain :is "from" "foobar.org", 
          envelope :localpart :contains "from" "admin" ) {
     set "envelope.from" "<EMAIL>";
}

'''

[sieve.trusted.scripts."rcpt"]
contents = '''
require ["variables", "envelope", "regex"];

if allof( envelope :localpart :contains "to" ".",
          envelope :regex "to" "(.+)@(.+)$") {
    set :replace "." "" "to" "${1}";
    set "envelope.to" "${to}@${2}";
}

'''

"#;

#[tokio::test]
async fn address_rewrite() {
    // Enable logging
    crate::enable_logging();

    // Prepare config
    let mut config = Config::new(CONFIG).unwrap();
    let core = Core::parse(&mut config, Default::default(), Default::default()).await;

    // Init session
    let mut session = Session::test(TestSMTP::from_core(core).server);
    session.data.remote_ip_str = "********".into();
    session.eval_session_params().await;
    session.ehlo("mx.doe.org").await;

    // Sender rewrite using regex
    session.mail_from("<EMAIL>", "250").await;
    assert_eq!(
        session.data.mail_from.as_ref().unwrap().address,
        "<EMAIL>"
    );
    session.reset();

    // Sender rewrite using sieve
    session.mail_from("<EMAIL>", "250").await;
    assert_eq!(
        session.data.mail_from.as_ref().unwrap().address_lcase,
        "<EMAIL>"
    );

    // Recipient rewrite using regex
    session.rcpt_to("<EMAIL>", "250").await;
    assert_eq!(
        session.data.rcpt_to.last().unwrap().address,
        "<EMAIL>"
    );

    // Remove duplicates
    session.rcpt_to("<EMAIL>", "250").await;
    assert_eq!(session.data.rcpt_to.len(), 1);

    // Recipient rewrite using sieve
    session.rcpt_to("<EMAIL>", "250").await;
    assert_eq!(
        session.data.rcpt_to.last().unwrap().address,
        "<EMAIL>"
    );
}
