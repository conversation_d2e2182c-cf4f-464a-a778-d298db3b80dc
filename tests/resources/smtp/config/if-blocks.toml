durations = [
    {if = "sender = 'jdoe'", then = "5d"},
    {if = "priority = -1 | starts_with(rcpt, 'jane')", then = "1h"},
    {else = false}
]

string-list = [
    {if = "sender = 'jdoe'", then = "['From', 'To', 'Date']"},
    {if = "priority = -1 | starts_with(rcpt, 'jane')", then = "'Other-ID'"},
    {else = "[]"}
]

string-list-bis = [
    {if = "sender = 'jdoe'", then = "['From', 'To', 'Date']"},
    {if = "priority = -1 | starts_with(rcpt, 'jane')", then = "[]"},
    {else = "['ID-Bis']"}
]

single-value = "'hello world'"

bad-if-without-then = [
    {if = "sender = 'jdoe'"},
    {else = 1}
]

bad-if-without-else = [
    {if = "sender = 'jdoe'", then = 1}
]

bad-multiple-else = [
    {if = "sender = 'jdoe'", then = 1},
    {else = 1},
    {else = 2}
]
