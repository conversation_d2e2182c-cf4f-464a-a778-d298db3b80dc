
[envelope]
rcpt-domain = "foo.example.org"
rcpt = "<EMAIL>"
sender-domain = "foo.net"
sender = "<EMAIL>"
local-ip = "***********"
remote-ip = "A:B:C::D:E"
mx = "mx.somedomain.com"
authenticated-as = "<EMAIL>"
priority = -4
listener = "smtp"
helo-domain = "hi-domain.net"

[eval."eq"]
test = [
    {if = "sender = '<EMAIL>'", then = "sender"},
    {else = false}
]
expect = "<EMAIL>"

[eval."starts-with"]
test = [
    {if = "starts_with(rcpt_domain, 'foo')", then = "'mx.' + rcpt_domain"},
    {else = false}
]
expect = "mx.foo.example.org"

[eval."regex"]
test = [
    {if = "matches('^([^.]+)@([^.]+)\.(.+)$', rcpt)", then = "$1 + '+' + $2 + '@' + $3"},
    {else = false}
]
expect = "<EMAIL>"

[eval."regex-full"]
test = [
    {if = "matches('^([^.]+)@([^.]+)\.(.+)$', rcpt)", then = "rcpt"},
    {else = false}
]
expect = "<EMAIL>"

[eval."envelope-match"]
test = [
    {if = "matches('^([^.]+)@(.+)$', authenticated_as)", then = "'rcpt ' + rcpt + ' listener ' + listener + ' ip ' + local_ip + ' priority ' + priority"},
    {else = false}
]
expect = "rcpt <EMAIL> listener smtp ip *********** priority -4"

[eval."static-match"]
test = [
    {if = "matches('^([^.]+)@(.+)$', authenticated_as)", then = "'hello world'"},
    {else = false}
]
expect = "hello world"

[eval."no-match"]
test = [
    {if = "matches('^([^.]+)@([^.]+)\.(.+)$org', authenticated_as)", then = "'test'"},
    {else = false}
]
expect = false

