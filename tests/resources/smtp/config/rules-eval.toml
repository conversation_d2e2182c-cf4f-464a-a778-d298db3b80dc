[envelope]
rcpt-domain = "example.org"
rcpt = "<EMAIL>"
sender-domain = "foo.net"
sender = "<EMAIL>"
local-ip = "***********"
remote-ip = "A:B:C::D:E"
mx = "mx.somedomain.com"
authenticated-as = "<EMAIL>"
priority = -4
listener = "smtp"
helo-domain = "hi-domain.net"

[rule]
"eq-true" = "rcpt_domain = 'example.org'"
"eq-false" = "rcpt_domain = 'example.com'"
"listener-eq-true" = "listener = 'smtp'"
"listener-eq-false" = "listener = 'smtps'"
"ip-eq-true" = "local_ip = '***********'"
"ip-eq-false" = "remote_ip = 'A:B:C::D:E'"
"ne-true" = "!is_empty(authenticated_as)"
"ne-false" = "authenticated_as != '<EMAIL>'"
"starts-with-true" = "starts_with(mx, 'mx.some')"
"starts-with-false" = "starts_with(mx, 'enchilada')"
"ends-with-true" = "ends_with(sender, '@foo.net')"
"ends-with-false" = "ends_with(sender, 'chimichanga')"
"regex-true" = "matches('^(.+)@(.+)$', sender)"
"regex-false" = "matches('/^\\S+@\\S+\\.\\S+$/', mx)"
"any-of-true" = "authenticated_as != '<EMAIL>' | rcpt_domain = 'example.org' | starts_with(mx, 'mx.some')"
"any-of-false" = "authenticated_as = 'something else' | rcpt_domain = 'something else' | starts_with(mx, 'something else')"
"all-of-true" =  "rcpt_domain = 'example.org' & listener = 'smtp' & starts_with(mx, 'mx.some')"
"all-of-false" = "rcpt_domain = 'example.org' & listener = 'smtp' & starts_with(mx, 'something else')"
"none-of-true" = "!(authenticated_as = 'something else' | rcpt_domain = 'something else' | starts_with(mx, 'something else'))"
"none-of-false" = "!(rcpt_domain = 'example.org' | listener = 'smtp' | starts_with(mx, 'mx.some'))"
