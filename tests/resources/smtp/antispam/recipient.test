expect MISSING_TO RCPT_COUNT_ZERO

X-To: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
expect RCPT_COUNT_ONE TO_DN_ALL

To: "Hello World" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
expect RCPT_COUNT_ONE TO_DN_NONE TAGGED_RCPT

To: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect TO_DN_RECIPIENTS RCPT_COUNT_TWO TO_DN_SOME

To: "recipients" <<EMAIL>>
Cc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
expect RCPT_IN_SUBJECT TO_DN_NONE RCPT_COUNT_ONE

To: <EMAIL>
Subject: Special <NAME_EMAIL>

Test
<!-- NEXT TEST -->
expect RCPT_LOCAL_IN_SUBJECT TO_DN_NONE RCPT_COUNT_ONE

To: <EMAIL>
Subject: Special offer for hello

Test
<!-- NEXT TEST -->
envelope_from 
envelope_to <EMAIL>
envelope_to <EMAIL>
expect RCPT_BOUNCEMOREONE TO_MATCH_ENVRCPT_ALL TO_DN_NONE RCPT_COUNT_TWO

To: <EMAIL>
Cc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect RCPT_BOUNCEMOREONE TO_MATCH_ENVRCPT_SOME TO_DN_NONE RCPT_COUNT_THREE

To: <EMAIL>, <EMAIL>
Cc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
expect RCPT_COUNT_ZERO UNDISC_RCPT

To: Undisclosed recipients:;
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
expect TO_DN_ALL RCPT_COUNT_ONE

List-Id: <list.domain.org>
To: "Mailing List" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
expect FORGED_RECIPIENTS TO_NEEDS_ENCODING TO_DN_ALL RCPT_COUNT_ONE

To: "Thé Spámmer" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect TO_DN_ALL TO_MATCH_ENVRCPT_ALL RCPT_COUNT_ONE

To: "Hello World" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect TO_EXCESS_QP TO_DN_ALL TO_MATCH_ENVRCPT_ALL RCPT_COUNT_ONE

To: "=?iso-8859-1?Q?Die_Hasen_und_die_Froesche?=" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect TO_EXCESS_BASE64 TO_DN_ALL TO_MATCH_ENVRCPT_ALL RCPT_COUNT_ONE

To: "=?iso-8859-1?B?RGllIEhhc2VuIHVuIGRpZSBGcm9lc2NoZQ==?=" <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FREEMAIL_TO DISPOSABLE_CC RCPT_COUNT_TWO TO_DN_NONE

To: <EMAIL>
Cc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FREEMAIL_CC DISPOSABLE_TO DISPOSABLE_BCC RCPT_COUNT_THREE TO_DN_NONE

To: <EMAIL>
Cc: <EMAIL>
Bcc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SORTED_RECIPS RCPT_COUNT_SEVEN TO_DN_NONE

To: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
Cc: <EMAIL>, <EMAIL>, <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect RCPT_COUNT_SEVEN TO_DN_NONE

To: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
Cc: <EMAIL>, <EMAIL>, <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SUSPICIOUS_RECIPS RCPT_COUNT_SEVEN TO_DN_NONE

To: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
Cc: <EMAIL>, <EMAIL>, <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
expect INFO_TO_INFO_LU RCPT_COUNT_ONE TO_MATCH_ENVRCPT_ALL TO_DN_NONE

From: <EMAIL>
To: <EMAIL>
List-Unsubscribe: <<EMAIL>>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
expect RCPT_COUNT_ONE TO_MATCH_ENVRCPT_ALL TO_DN_NONE

From: <EMAIL>
To: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect TO_WRAPPED_IN_SPACES RCPT_COUNT_TWO TO_MATCH_ENVRCPT_ALL TO_DN_NONE

From: <EMAIL>
To: < <EMAIL> >
Cc: <EMAIL>
Subject: Hi

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
envelope_to <EMAIL>
envelope_to <EMAIL>
expect TO_WRAPPED_IN_SPACES RCPT_COUNT_TWO TO_MATCH_ENVRCPT_ALL TO_DN_SOME

From: <EMAIL>
To: <EMAIL>
Cc: "Test" <<EMAIL> >
Subject: Hi

Test
<!-- NEXT TEST -->
expect RCPT_IN_BODY TO_DN_NONE RCPT_COUNT_ONE

To: <EMAIL>
Subject: Special offer 

An <NAME_EMAIL>
<!-- NEXT TEST -->
expect RCPT_DOMAIN_IN_MESSAGE RCPT_IN_BODY RCPT_DOMAIN_IN_SUBJECT RCPT_COUNT_ONE TO_DN_NONE

To: <EMAIL>
Subject: Message for world.com

An <NAME_EMAIL>

<!-- NEXT TEST -->
expect RCPT_DOMAIN_IN_MESSAGE RCPT_DOMAIN_IN_BODY RCPT_DOMAIN_IN_SUBJECT RCPT_COUNT_ONE TO_DN_NONE

To: <EMAIL>
Subject: Message for world.com

An offer for world.com

