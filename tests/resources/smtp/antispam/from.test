expect MISSING_FROM

X-From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect MULTIPLE_FROM FROM_EQ_ENV_FROM FROM_NO_DN

From: <EMAIL>
From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from test
expect FROM_INVALID ENV_FROM_INVALID

From: test

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_SERVICE_ACCT FROM_HAS_DN FROM_EQ_ENV_FROM

From: "WWW DATA" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_DN_EQ_ADDR FROM_EQ_ENV_FROM

From: "<EMAIL>" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SPOOF_DISPLAY_NAME FROM_EQ_ENV_FROM FROM_HAS_DN

From: "<EMAIL>" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_NEQ_DISPLAY_NAME FROM_EQ_ENV_FROM FROM_HAS_DN

From: "<EMAIL>" <<EMAIL>>

Test
<!-- NEXT TEST -->
helo_domain mx.domain.co.uk
expect FROMTLD_EQ_ENV_FROMTLD FROM_NEQ_DISPLAY_NAME FROM_HAS_DN FROM_BOUNCE

From: "<EMAIL>" <<EMAIL>>

Test
<!-- NEXT TEST -->
helo_domain mx.domain.co.uk
expect FROMTLD_EQ_ENV_FROMTLD FROM_HAS_DN FROM_BOUNCE

From: "Mailer Daemon" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_NAME_HAS_TITLE FROM_NAME_EXCESS_SPACE FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Mr. Money   Maker" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect TAGGED_FROM FROM_EQ_ENV_FROM FROM_NO_DN

From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect TO_EQ_FROM FROM_EQ_ENV_FROM FROM_NO_DN

From: <EMAIL>
To: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_EQ_ENV_FROM FROM_NO_DN

From: <EMAIL>
To: <EMAIL>, <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_NEEDS_ENCODING FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hélló" <<EMAIL>>

Test
<!-- NEXT TEST -->
param.smtputf8 1
envelope_from <EMAIL>
expect FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hélló" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_EXCESS_QP FROM_EQ_ENV_FROM FROM_HAS_DN

From: =?iso-8859-1?Q?Die_Hasen_und_die_Froesche?= <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_EXCESS_BASE64 FROM_EQ_ENV_FROM FROM_HAS_DN

From: "=?iso-8859-1?B?RGllIEhhc2VuIHVuIGRpZSBGcm9lc2NoZQ==?=" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_EQ_ENV_FROM FROM_HAS_DN

From: "=?iso-8859-1?Q?Die_Hasen_und_die_Fr=F6sche?=" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect NO_SPACE_IN_FROM FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hello"<<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect HEADER_RCONFIRM_MISMATCH FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hello" <<EMAIL>>
X-Confirm-Reading-To: <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect HEADER_FORGED_MDN FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hello" <<EMAIL>>
Disposition-Notification-To: <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROM_SERVICE_ACCT WWW_DOT_DOMAIN FROM_EQ_ENV_FROM FROM_HAS_DN

From: "Hello" <<EMAIL>>
Reply-to: <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FREEMAIL_FROM DISPOSABLE_ENV_FROM FROM_NEQ_ENV_FROM FROM_NO_DN FORGED_SENDER

From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect DISPOSABLE_FROM FREEMAIL_ENV_FROM FROM_NEQ_ENV_FROM FROM_NO_DN FORGED_SENDER

From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect FROMHOST_NORES_A_OR_MX FROM_EQ_ENV_FROM FROM_NO_DN

From: <EMAIL>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SPOOF_DISPLAY_NAME FROM_HAS_DN FROM_EQ_ENV_FROM

From: "Foo (<EMAIL>)" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SPOOF_DISPLAY_NAME FROM_HAS_DN FROM_EQ_ENV_FROM

From: Foo (<EMAIL>) <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SPOOF_DISPLAY_NAME FROM_HAS_DN FROM_EQ_ENV_FROM

From: "Foo <EMAIL>" <<EMAIL>>

Test
<!-- NEXT TEST -->
envelope_from <EMAIL>
expect SPOOF_DISPLAY_NAME FROM_HAS_DN FROM_EQ_ENV_FROM

From: "Foo '<EMAIL>'" <<EMAIL>>

Test
