expect MIME_HTML_ONLY HTML_SHORT_1

Message-Id: <<EMAIL>>
X-Sender: <EMAIL> (Unverified)
X-Mailer: QUALCOMM Windows Eudora Pro Version 4.2.0.58 
X-Priority: 2 (High)
Date: Fri, 19 May 2000 00:29:55 -0400
To: <PERSON> =?iso-8859-1?Q?M=FCller?= <<EMAIL>>
From: <PERSON> <<EMAIL>>
Subject: =?iso-8859-1?Q?Die_Hasen_und_die_Fr=F6sche?=
Mime-Version: 1.0
Content-Type: text/html; charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable

<html>
<font face=3D"Arial, Helvetica" size=3D5 color=3D"#0000FF"><b>Die Hasen und =
die
Fr=F6sche<br>
<br>
</font></b><font face=3D"Arial, Helvetica">Die Hasen klagten einst =FCber
ihre mi=DFliche Lage; &quot;wir leben&quot;, sprach ein Redner, &quot;in
steter Furcht vor Menschen und Tieren, eine Beute der Hunde, der Adler,
ja fast aller Raubtiere! Unsere stete Angst ist =E4rger als der Tod selbst.
Auf, la=DFt uns ein f=FCr allemal sterben.&quot; <br>
<br>
In einem nahen Teich wollten sie sich nun ers=E4ufen; sie eilten ihm zu;
allein das au=DFerordentliche Get=F6se und ihre wunderbare Gestalt
erschreckte eine Menge Fr=F6sche, die am Ufer sa=DFen, so sehr, da=DF sie au=
fs
schnellste untertauchten. <br>
<br>
&quot;Halt&quot;, rief nun eben dieser Sprecher, &quot;wir wollen das
Ers=E4ufen noch ein wenig aufschieben, denn auch uns f=FCrchten, wie ihr
seht, einige Tiere, welche also wohl noch ungl=FCcklicher sein m=FCssen als
wir.&quot; <br>
<br>
</font></html>
<!-- NEXT TEST -->
expect HTTP_TO_HTTPS HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
<a href="http://mydomain.com">https://mydomain.com</a>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTTP_TO_IP HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head>
<body>
<p>some text</p>
<a href="https://*******/phisherino.php">https://*******</a>
</body>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect EXT_CSS HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<link href="https://domain.com/external.css#test">
<p>some text</p>
<a href="https://mydomain.com">https://mydomain.com</a>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect EXT_CSS HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<link rel="stylesheet" href="https://domain.com/external">
<p>some text</p>
<a href="https://mydomain.com">https://mydomain.com</a>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTML_UNBALANCED_TAG HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<link rel="unknown" href="https://domain.com/external">
<head>hello
<a href="https://mydomain.com">https://mydomain.com</a>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTML_UNBALANCED_TAG HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

https://mydomain.com

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<link rel="unknown" href="https://domain.com/external">
<body>hello
<a href="https://mydomain.com">https://mydomain.com</a>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTML_SHORT_LINK_IMG_1 HTML_SHORT_1 HAS_LINK_TO_LARGE_IMG

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

Testing

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<html>
<head><title>Test</title></head>
<body>
<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam</p>
<a href="https://herrspammer.com"><img src="cid:spammimg" width="200" height="200"></a>
</body>
</html>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect BODY_URI_ONLY HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

Testing

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<html>
<head><title>Test</title></head>
<body>
<p>http://myurl.com</>
</body>
</html>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTML_TEXT_IMG_RATIO HTML_SHORT_1

Content-Type: multipart/alternative;
	boundary="=====================_714967308==_.ALT"

--=====================_714967308==_.ALT
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: quoted-printable

Testing

--=====================_714967308==_.ALT
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<html>
<head><title>Test</title></head>
<body>
<a<img src="cid:spammimg1" width="200" height="200">
<img src="cid:spammimg2" width="200" height="200">
<img src="cid:spammimg3" width="200" height="200">
</body>
</html>

--=====================_714967308==_.ALT--
<!-- NEXT TEST -->
expect HTML_META_REFRESH_URL MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head>
<meta http-equiv="refresh" content="5; url=https://example.com/">
</head>
<body>
<p>some text</p>
</body>
<!-- NEXT TEST -->
expect MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head>
<meta http-equiv="refresh" content="5">
</head>
<body>
<p>some text</p>
</body>
<!-- NEXT TEST -->
expect HAS_DATA_URI DATA_URI_OBFU MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head>
<meta http-equiv="refresh" content="5">
</head>
<body>
<p>some text</p>
<a href="data:text/plain;base64,SGVsbG8sIHdvcmxkIQ==">Click me for a hello message</a>
</body>
<!-- NEXT TEST -->
expect HAS_DATA_URI MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head>
<meta http-equiv="refresh" content="5">
</head>
<body>
<p>some text and a lovely explanation to avoid the text to image ratio tag</p>
<img src="data:image/png;base64,iVBORw0KGg...." alt="Red dot" />
<a href="data:other">Click me for a hello message</a>
</body>
<!-- NEXT TEST -->
expect PHISHING MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head><body><p>some text</p>
<a href="https://domain1.com/query">https://domain2.com/otherquery</a>
</body>
<!-- NEXT TEST -->
expect MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head><body><p>some text</p>
<a href="https://domain1.co.uk/query">https://subdomain.domain1.co.uk/otherquery</a>
</body>
<!-- NEXT TEST -->
expect PHISHING MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head><body><p>some text</p>
<a href="https://domain1.com/query">domain2.com/otherquery</a>
</body>
<!-- NEXT TEST -->
expect MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head><body><p>some text</p>
<a href="https://domain1.co.uk/query">subdomain.domain1.co.uk/otherquery</a>
</body>
<!-- NEXT TEST -->
expect MIME_HTML_ONLY HTML_SHORT_1

Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: 8bit

<head></head><body><p>some text</p>
<a href="https://domain1.co.uk/query">normal text</a>
</body>
