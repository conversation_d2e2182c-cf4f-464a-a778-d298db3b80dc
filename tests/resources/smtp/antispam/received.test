expect RCVD_COUNT_THREE RCVD_NO_TLS_LAST

Received: from BAY0-HMR08.bay0.hotmail.com (bay0-hmr08.bay0.hotmail.com [*************]) 
          by dogma.slashnull.org (8.11.6/8.11.6) 
          with ESMTP id h2DBpvs24047 for <<EMAIL>>; Thu, 13 Mar 2003 11:51:57 GMT
Received: from BAY0-HMR08.bay0.hotmail.com (bay0-hmr08.bay0.hotmail.com [*************]) 
          by dogma.slashnull.org (8.11.6/8.11.6) 
          with ESMTP id h2DBpvs24047 for <<EMAIL>>; Thu, 13 Mar 2003 11:51:57 GMT
Received: from BAY0-HMR08.bay0.hotmail.com (bay0-hmr08.bay0.hotmail.com [*************]) 
          by dogma.slashnull.org (8.11.6/8.11.6) 
          with ESMTP id h2DBpvs24047 for <<EMAIL>>; Thu, 13 Mar 2003 11:51:57 GMT

test
<!-- NEXT TEST -->
authenticated_as <EMAIL>
tls.version TLSv1.3
expect RCVD_VIA_SMTP_AUTH RCVD_COUNT_ONE RCVD_TLS_LAST

Received: from BAY0-HMR08.bay0.hotmail.com (bay0-hmr08.bay0.hotmail.com [*************]) 
          by dogma.slashnull.org (8.11.6/8.11.6) 
          with ESMTP id h2DBpvs24047 for <<EMAIL>>; Thu, 13 Mar 2003 11:51:57 GMT

test
<!-- NEXT TEST -->
expect RCVD_ILLEGAL_CHARS RCVD_COUNT_ONE RCVD_NO_TLS_LAST

Received: from BAY0-HMR08.bay0.hótmail.com (bay0-hmr08.bay0.hótmail.com [*************]) 
          by dogma.slashnull.org (8.11.6/8.11.6) 
          with ESMTP id h2DBpvs24047 for <<EMAIL>>; Thu, 13 Mar 2003 11:51:57 GMT

test

<!-- NEXT TEST -->
tls.version TLVv1.3
expect RCVD_TLS_ALL RCVD_HELO_USER RCVD_DOUBLE_IP_SPAM FORGED_RCVD_TRAIL PREVIOUSLY_DELIVERED RCVD_COUNT_FIVE

Received: from Agni (localhost [::ffff:127.0.0.1]) (TLS: TLSv1/SSLv3, 168bits,DES-CBC3-SHA) by agni.forevermore.net 
          with esmtp; Mon, 28 Oct 2002 14:48:52 -0800
Received: from [**************] (79.sub-174-252-72.myvzw.com [*************]) by mx.google.com 
          with ESMTPS id m16sm345129qck.28.2011.***********.02 (version=TLSv1/SSLv3 cipher=OTHER); Wed, 15 Jun 2011 07:42:08 -0700 (PDT)
Received: from other.myvzw.com (79.sub-174-252-72.myvzw.com [*************]) by mx.google.com 
          with ESMTPS id m16sm345129qck.28.2011.***********.02 (version=TLSv1/SSLv3 cipher=OTHER); Wed, 15 Jun 2011 07:42:08 -0700 (PDT)
Received: from user (*************) by DB6PR07MB3384.eurprd07.prod.outlook.com (*************) 
          with Microsoft SMTP Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.1143.11; Thu, 13 Sep 2018 14:47:44 +0000
Received: from [************] (using TLSv1.3 with cipher TLS_AES_256_GCM_SHA384 (256/256 bits) key-exchange ECDHE (P-256) server-signature RSA-PSS (2048 bits) server-digest SHA256) (No client certificate requested) 
          by ietfa.amsl.com (Postfix) with ESMTPS id 10B7AC151535 for <<EMAIL>>; Mon, 28 Aug 2023 02:21:23 -0700 (PDT)
To: <EMAIL>
Subject: test

test

<!-- NEXT TEST -->
expect DIRECT_TO_MX RCVD_COUNT_ZERO RCVD_NO_TLS_LAST

To: <EMAIL>
X-Mailer: MUA 1.2
Subject: test

test
<!-- NEXT TEST -->
expect RCVD_UNPARSABLE RCVD_NO_TLS_LAST RCVD_COUNT_ONE

To: <EMAIL>
Received: invalid

test
