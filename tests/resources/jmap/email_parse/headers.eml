From: <PERSON> <<EMAIL>> (Vandelay Industries)
To: "  <PERSON>" <<EMAIL>>, Friends:
      <EMAIL>, =?UTF-8?Q?John_Sm=C3=AEth?=
      <<EMAIL>>;
Cc: List 1: <EMAIL>, <EMAIL>; List 2: <EMAIL>, 
       <EMAIL>; <EMAIL>, <EMAIL>
Cc: =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: <EMAIL>, 
       <EMAIL>; =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: 
       <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>
Bcc: <PERSON> <<EMAIL>>, <PERSON>
        <<EMAIL>>, <PERSON> <<EMAIL>>
Bcc: <EMAIL>, <EMAIL>
Date: Tue, 1 Jul 2003 10:52:37 +0200
Resent-Date: Tue, 2 Jul 2005 11:52:37 +0200
Resent-Date: Tue, 3 Jul 2005 12:52:37 +0300
Resent-Date: Tue, 4 Jul 2005 13:52:37 +0400
Message-ID: <<EMAIL>>
References: <<EMAIL>>
            <<EMAIL>>
References: <<EMAIL>>
            <<EMAIL>>
Keywords: multipart, alternative, example
List-Post: <mailto:<EMAIL>> (Postings are Moderated)
List-Subscribe: (Use this command to join the list)
       <mailto:<EMAIL>?body=subscribe%20list>
List-Subscribe: <ftp://ftp.host.com/list.txt> (FTP),  
               <mailto:<EMAIL>?subject=subscribe>
List-Owner: <http://www.host.com/list.cgi?cmd=sub&lst=list>,
       <mailto:<EMAIL>?body=subscribe%20list>
List-Unsubscribe: (Use this command to get off the list)
         <mailto:<EMAIL>?body=unsubscribe%20list>
Subject: Why not both importing AND exporting? =?utf-8?b?4pi6?=
X-Address-Single: =?ISO-8859-1?Q?Andr=E9?= Pirard <<EMAIL>>
X-Address: Mary Smith <<EMAIL>>
X-Address: John Doe <<EMAIL>>
X-AddressList-Single: Mary Smith <<EMAIL>>, <EMAIL>, Who? <<EMAIL>>
X-AddressList: =?US-ASCII*EN?Q?Keith_Moore?= <<EMAIL>>, 
                John =?US-ASCII*EN?Q?Doe?= <<EMAIL>>
X-AddressList: =?ISO-8859-1?Q?Keld_J=F8rn_Simonsen?= <<EMAIL>>,
                =?ISO-8859-1?Q?Olle_J=E4rnefors?= <<EMAIL>>
X-AddressesGroup-Single: A Group(Some people)
            :Chris Jones <c@(Chris's host.)public.example>,
            <EMAIL>,     John <<EMAIL>> (my dear
            friend); (the end of the group)
X-AddressesGroup: A Group:Ed Jones <<EMAIL>>,<EMAIL>,John <<EMAIL>>;
X-AddressesGroup: "List 1": <EMAIL>, <EMAIL>; "List 2": 
            <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>
X-List-Single: <mailto:<EMAIL>> (X-Postings are Moderated)
X-List: <http://www.mylist.com/list>,
       <mailto:<EMAIL>>
X-List: <http://www.mylist2.com/list2>,
       <mailto:<EMAIL>>
X-Text-Single: =?ISO-8859-1?B?SWYgeW91IGNhbiByZWFkIHRoaXMgeW8=?=
              =?ISO-8859-2?B?dSB1bmRlcnN0YW5kIHRoZSBleGFtcGxlLg==?=
X-Text: =?iso-8859-1?q?this=20is=20some=20text?=
X-Text: =?ISO-8859-1?Q?a?= =?ISO-8859-2?Q?_b?=
X-Date-Single: Tue, 5 Jul 2006 13:52:37 -0500
X-Date: Sat, 20 Nov 2021 14:22:01 -0800
X-Date: Sun, 21 Nov 2021 15:23:02 -0900
X-Id-Single: <<EMAIL>> <<EMAIL>>
X-Id: <<EMAIL>>
X-Id: <<EMAIL>> <<EMAIL>>
Content-Type: multipart/mixed; boundary="festivus";

--festivus
Content-Type: text/html; charset="us-ascii"
Content-Transfer-Encoding: base64
X-Custom-Header: 123

PGh0bWw+PHA+SSB3YXMgdGhpbmtpbmcgYWJvdXQgcXVpdHRpbmcgdGhlICZsZHF1bztle
HBvcnRpbmcmcmRxdW87IHRvIGZvY3VzIGp1c3Qgb24gdGhlICZsZHF1bztpbXBvcnRpbm
cmcmRxdW87LDwvcD48cD5idXQgdGhlbiBJIHRob3VnaHQsIHdoeSBub3QgZG8gYm90aD8
gJiN4MjYzQTs8L3A+PC9odG1sPg==
--festivus
Content-Type: message/rfc822
X-Custom-Header-2: 345

From: "Cosmo Kramer" <<EMAIL>>
Subject: Exporting my book about coffee tables
Content-Type: multipart/mixed; boundary="giddyup";

--giddyup
Content-Type: text/plain; charset="utf-16"
Content-Transfer-Encoding: quoted-printable

=FF=FE=0C!5=D8"=DD5=D8)=DD5=D8-=DD =005=D8*=DD5=D8"=DD =005=D8"=
=DD5=D85=DD5=D8-=DD5=D8,=DD5=D8/=DD5=D81=DD =005=D8*=DD5=D86=DD =
=005=D8=1F=DD5=D8,=DD5=D8,=DD5=D8(=DD =005=D8-=DD5=D8)=DD5=D8"=
=DD5=D8=1E=DD5=D80=DD5=D8"=DD!=00
--giddyup
Content-Type: image/gif; name*1="about "; name*0="Book ";
              name*2*=utf-8''%e2%98%95 tables.gif
Content-Transfer-Encoding: Base64
Content-Disposition: attachment

R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7
--giddyup--
--festivus--
