{"mailboxIds": null, "messageId": ["<EMAIL>"], "references": ["<EMAIL>", "<EMAIL>"], "from": [{"name": "<PERSON> (Vandelay Industries)", "email": "<EMAIL>"}], "to": [{"name": "  <PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "cc": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], "bcc": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], "subject": "Why not both importing AND exporting? ☺", "sentAt": "2003-07-01T08:52:37Z", "textBody": [{"size": 175, "type": "text/html", "charset": "us-ascii"}], "htmlBody": [{"size": 175, "type": "text/html", "charset": "us-ascii"}], "attachments": [{"size": 723, "type": "message/rfc822"}], "preview": "I was thinking about quitting the “exporting” to focus just on the “importing”,\nbut then I thought, why not do both? ☺\n", "header:Bcc": " <EMAIL>, <EMAIL>", "header:Bcc:all": [" <PERSON> <<EMAIL>>, <PERSON>\n        <<EMAIL>>, <PERSON> <<EMAIL>>", " <EMAIL>, <EMAIL>"], "header:Bcc:asAddresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], "header:Bcc:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> Freed", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]], "header:Bcc:asGroupedAddresses": [{"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}], "header:Bcc:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> Freed", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}], [{"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}]], "header:Cc": " =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: <EMAIL>, \n       <EMAIL>; =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: \n       <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>", "header:Cc:all": [" List 1: <EMAIL>, <EMAIL>; List 2: <EMAIL>, \n       <EMAIL>; <EMAIL>, <EMAIL>", " =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: <EMAIL>, \n       <EMAIL>; =?utf-8?b?VGjDrXMgw61zIHbDoWzDrWQgw5pURjg=?=: \n       <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>"], "header:Cc:asAddresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], "header:Cc:asAddresses:all": [[{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]], "header:Cc:asGroupedAddresses": [{"name": "<PERSON><PERSON>ís í<PERSON> válíd ÚTF8", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": "<PERSON><PERSON>ís í<PERSON> válíd ÚTF8", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}], "header:Cc:asGroupedAddresses:all": [[{"name": "List 1", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": "List 2", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}], [{"name": "<PERSON><PERSON>ís í<PERSON> válíd ÚTF8", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": "<PERSON><PERSON>ís í<PERSON> válíd ÚTF8", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}]], "header:Date": " <PERSON><PERSON>, 1 Jul 2003 10:52:37 +0200", "header:Date:all": [" <PERSON><PERSON>, 1 Jul 2003 10:52:37 +0200"], "header:Date:asDate": "2003-07-01T08:52:37Z", "header:Date:asDate:all": ["2003-07-01T08:52:37Z"], "header:From": " <PERSON> <<EMAIL>> (Vandelay Industries)", "header:From:all": [" <PERSON> <<EMAIL>> (Vandelay Industries)"], "header:From:asAddresses": [{"name": "<PERSON> (Vandelay Industries)", "email": "<EMAIL>"}], "header:From:asAddresses:all": [[{"name": "<PERSON> (Vandelay Industries)", "email": "<EMAIL>"}]], "header:From:asGroupedAddresses": [{"name": null, "addresses": [{"name": "<PERSON> (Vandelay Industries)", "email": "<EMAIL>"}]}], "header:From:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON> (Vandelay Industries)", "email": "<EMAIL>"}]}]], "header:Keywords": " multipart, alternative, example", "header:Keywords:all": [" multipart, alternative, example"], "header:Keywords:asText": "multipart, alternative, example", "header:Keywords:asText:all": ["multipart, alternative, example"], "header:List-Owner": " <http://www.host.com/list.cgi?cmd=sub&lst=list>,\n       <mailto:<EMAIL>?body=subscribe%20list>", "header:List-Owner:all": [" <http://www.host.com/list.cgi?cmd=sub&lst=list>,\n       <mailto:<EMAIL>?body=subscribe%20list>"], "header:List-Owner:asURLs": ["http://www.host.com/list.cgi?cmd=sub&lst=list", "mailto:<EMAIL>?body=subscribe%20list"], "header:List-Owner:asURLs:all": [["http://www.host.com/list.cgi?cmd=sub&lst=list", "mailto:<EMAIL>?body=subscribe%20list"]], "header:List-Post": " <mailto:<EMAIL>> (Postings are Moderated)", "header:List-Post:all": [" <mailto:<EMAIL>> (Postings are Moderated)"], "header:List-Post:asURLs": ["mailto:<EMAIL>"], "header:List-Post:asURLs:all": [["mailto:<EMAIL>"]], "header:List-Subscribe": " <ftp://ftp.host.com/list.txt> (FTP),  \n               <mailto:<EMAIL>?subject=subscribe>", "header:List-Subscribe:all": [" (Use this command to join the list)\n       <mailto:<EMAIL>?body=subscribe%20list>", " <ftp://ftp.host.com/list.txt> (FTP),  \n               <mailto:<EMAIL>?subject=subscribe>"], "header:List-Subscribe:asURLs": ["ftp://ftp.host.com/list.txt", "mailto:<EMAIL>?subject=subscribe"], "header:List-Subscribe:asURLs:all": [["mailto:<EMAIL>?body=subscribe%20list"], ["ftp://ftp.host.com/list.txt", "mailto:<EMAIL>?subject=subscribe"]], "header:List-Unsubscribe": " (Use this command to get off the list)\n         <mailto:<EMAIL>?body=unsubscribe%20list>", "header:List-Unsubscribe:all": [" (Use this command to get off the list)\n         <mailto:<EMAIL>?body=unsubscribe%20list>"], "header:List-Unsubscribe:asURLs": ["mailto:<EMAIL>?body=unsubscribe%20list"], "header:List-Unsubscribe:asURLs:all": [["mailto:<EMAIL>?body=unsubscribe%20list"]], "header:Message-ID": " <<EMAIL>>", "header:Message-ID:all": [" <<EMAIL>>"], "header:Message-ID:asMessageIds": ["<EMAIL>"], "header:Message-ID:asMessageIds:all": [["<EMAIL>"]], "header:References": " <<EMAIL>>\n            <<EMAIL>>", "header:References:all": [" <<EMAIL>>\n            <<EMAIL>>", " <<EMAIL>>\n            <<EMAIL>>"], "header:References:asMessageIds": ["<EMAIL>", "<EMAIL>"], "header:References:asMessageIds:all": [["<EMAIL>", "<EMAIL>"], ["<EMAIL>", "<EMAIL>"]], "header:Resent-Date": " <PERSON><PERSON>, 4 Jul 2005 13:52:37 +0400", "header:Resent-Date:all": [" <PERSON><PERSON>, 2 Jul 2005 11:52:37 +0200", " <PERSON><PERSON>, 3 Jul 2005 12:52:37 +0300", " <PERSON><PERSON>, 4 Jul 2005 13:52:37 +0400"], "header:Resent-Date:asDate": "2005-07-04T09:52:37Z", "header:Resent-Date:asDate:all": ["2005-07-02T09:52:37Z", "2005-07-03T09:52:37Z", "2005-07-04T09:52:37Z"], "header:Subject": " Why not both importing AND exporting? =?utf-8?b?4pi6?=", "header:Subject:all": [" Why not both importing AND exporting? =?utf-8?b?4pi6?="], "header:Subject:asText": "Why not both importing AND exporting? ☺", "header:Subject:asText:all": ["Why not both importing AND exporting? ☺"], "header:To": " \"  <PERSON>\" <<EMAIL>>, Friends:\n      <EMAIL>, =?UTF-8?Q?John_Sm=C3=AEth?=\n      <<EMAIL>>;", "header:To:all": [" \"  <PERSON>\" <<EMAIL>>, Friends:\n      <EMAIL>, =?UTF-8?Q?John_Sm=C3=AEth?=\n      <<EMAIL>>;"], "header:To:asAddresses": [{"name": "  <PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "header:To:asAddresses:all": [[{"name": "  <PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]], "header:To:asGroupedAddresses": [{"name": null, "addresses": [{"name": "  <PERSON>", "email": "<EMAIL>"}]}, {"name": "Friends", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}], "header:To:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "  <PERSON>", "email": "<EMAIL>"}]}, {"name": "Friends", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}]], "header:X-Address": " <PERSON> <<EMAIL>>", "header:X-Address:all": [" <PERSON> <<EMAIL>>", " <PERSON> <<EMAIL>>"], "header:X-Address:asAddresses": [{"name": "<PERSON>", "email": "<EMAIL>"}], "header:X-Address:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}], [{"name": "<PERSON>", "email": "<EMAIL>"}]], "header:X-Address:asGroupedAddresses": [{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}]}], "header:X-Address:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}]}], [{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}]}]], "header:X-Address-Single": " =?ISO-8859-1?Q?Andr=E9?= Pirard <<EMAIL>>", "header:X-Address-Single:all": [" =?ISO-8859-1?Q?Andr=E9?= Pirard <<EMAIL>>"], "header:X-Address-Single:asAddresses": [{"name": "<PERSON>", "email": "<EMAIL>"}], "header:X-Address-Single:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}]], "header:X-Address-Single:asGroupedAddresses": [{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}]}], "header:X-Address-Single:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}]}]], "header:X-AddressList": " =?ISO-8859-1?Q?Keld_J=F8rn_<PERSON><PERSON>?= <<EMAIL>>,\n                =?ISO-8859-1?Q?Olle_J=E4rnefors?= <<EMAIL>>", "header:X-AddressList:all": [" =?US-ASCII*EN?Q?<PERSON>?= <<EMAIL>>, \n                <PERSON> =?US-ASCII*EN?Q?Doe?= <<EMAIL>>", " =?ISO-8859-1?Q?Keld_J=F8rn_<PERSON><PERSON>?= <<EMAIL>>,\n                =?ISO-8859-1?Q?Olle_J=E4rnefors?= <<EMAIL>>"], "header:X-AddressList:asAddresses": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "header:X-AddressList:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]], "header:X-AddressList:asGroupedAddresses": [{"name": null, "addresses": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}], "header:X-AddressList:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}], [{"name": null, "addresses": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}]], "header:X-AddressList-Single": " <PERSON> <<EMAIL>>, <EMAIL>, Who? <<EMAIL>>", "header:X-AddressList-Single:all": [" <PERSON> <<EMAIL>>, <EMAIL>, Who? <<EMAIL>>"], "header:X-AddressList-Single:asAddresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "Who?", "email": "<EMAIL>"}], "header:X-AddressList-Single:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "Who?", "email": "<EMAIL>"}]], "header:X-AddressList-Single:asGroupedAddresses": [{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "Who?", "email": "<EMAIL>"}]}], "header:X-AddressList-Single:asGroupedAddresses:all": [[{"name": null, "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "Who?", "email": "<EMAIL>"}]}]], "header:X-AddressesGroup": " \"List 1\": <EMAIL>, <EMAIL>; \"List 2\": \n            <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>", "header:X-AddressesGroup:all": [" A Group:<PERSON> <<EMAIL>>,<EMAIL>,<PERSON> <<EMAIL>>;", " \"List 1\": <EMAIL>, <EMAIL>; \"List 2\": \n            <EMAIL>, <EMAIL>; <EMAIL>, <EMAIL>"], "header:X-AddressesGroup:asAddresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}], "header:X-AddressesGroup:asAddresses:all": [[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]], "header:X-AddressesGroup:asGroupedAddresses": [{"name": "List 1", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": "List 2", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}], "header:X-AddressesGroup:asGroupedAddresses:all": [[{"name": "A Group", "addresses": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}], [{"name": "List 1", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": "List 2", "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": null, "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}]}]], "header:X-AddressesGroup-Single": " A Group(Some people)\n            :<PERSON> <c@(<PERSON>'s host.)public.example>,\n            <EMAIL>,     <PERSON> <<EMAIL>> (my dear\n            friend); (the end of the group)", "header:X-AddressesGroup-Single:all": [" A Group(Some people)\n            :<PERSON> <c@(<PERSON>'s host.)public.example>,\n            <EMAIL>,     <PERSON> <<EMAIL>> (my dear\n            friend); (the end of the group)"], "header:X-AddressesGroup-Single:asAddresses": [{"name": "<PERSON> (<PERSON>'s host.)", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON> (my dear friend)", "email": "<EMAIL>"}, {"name": "the end of the group", "email": ""}], "header:X-AddressesGroup-Single:asAddresses:all": [[{"name": "<PERSON> (<PERSON>'s host.)", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON> (my dear friend)", "email": "<EMAIL>"}, {"name": "the end of the group", "email": ""}]], "header:X-AddressesGroup-Single:asGroupedAddresses": [{"name": "A Group (Some people)", "addresses": [{"name": "<PERSON> (<PERSON>'s host.)", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON> (my dear friend)", "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": "the end of the group", "email": ""}]}], "header:X-AddressesGroup-Single:asGroupedAddresses:all": [[{"name": "A Group (Some people)", "addresses": [{"name": "<PERSON> (<PERSON>'s host.)", "email": "<EMAIL>"}, {"name": null, "email": "<EMAIL>"}, {"name": "<PERSON> (my dear friend)", "email": "<EMAIL>"}]}, {"name": null, "addresses": [{"name": "the end of the group", "email": ""}]}]], "header:X-Date": " Sun, 21 Nov 2021 15:23:02 -0900", "header:X-Date:all": [" Sat, 20 Nov 2021 14:22:01 -0800", " Sun, 21 Nov 2021 15:23:02 -0900"], "header:X-Date:asDate": "2021-11-22T00:23:02Z", "header:X-Date:asDate:all": ["2021-11-20T22:22:01Z", "2021-11-22T00:23:02Z"], "header:X-Date-Single": " <PERSON><PERSON>, 5 Jul 2006 13:52:37 -0500", "header:X-Date-Single:all": [" <PERSON><PERSON>, 5 Jul 2006 13:52:37 -0500"], "header:X-Date-Single:asDate": "2006-07-05T18:52:37Z", "header:X-Date-Single:asDate:all": ["2006-07-05T18:52:37Z"], "header:X-Id": " <<EMAIL>> <<EMAIL>>", "header:X-Id:all": [" <<EMAIL>>", " <<EMAIL>> <<EMAIL>>"], "header:X-Id:asMessageIds": ["<EMAIL>", "<EMAIL>"], "header:X-Id:asMessageIds:all": [["<EMAIL>"], ["<EMAIL>", "<EMAIL>"]], "header:X-Id-Single": " <<EMAIL>> <<EMAIL>>", "header:X-Id-Single:all": [" <<EMAIL>> <<EMAIL>>"], "header:X-Id-Single:asMessageIds": ["<EMAIL>", "<EMAIL>"], "header:X-Id-Single:asMessageIds:all": [["<EMAIL>", "<EMAIL>"]], "header:X-List": " <http://www.mylist2.com/list2>,\n       <mailto:<EMAIL>>", "header:X-List:all": [" <http://www.mylist.com/list>,\n       <mailto:<EMAIL>>", " <http://www.mylist2.com/list2>,\n       <mailto:<EMAIL>>"], "header:X-List:asURLs": ["http://www.mylist2.com/list2", "mailto:<EMAIL>"], "header:X-List:asURLs:all": [["http://www.mylist.com/list", "mailto:<EMAIL>"], ["http://www.mylist2.com/list2", "mailto:<EMAIL>"]], "header:X-List-Single": " <mailto:<EMAIL>> (X-Postings are Moderated)", "header:X-List-Single:all": [" <mailto:<EMAIL>> (X-Postings are Moderated)"], "header:X-List-Single:asURLs": ["mailto:<EMAIL>"], "header:X-List-Single:asURLs:all": [["mailto:<EMAIL>"]], "header:X-Text": " =?ISO-8859-1?Q?a?= =?ISO-8859-2?Q?_b?=", "header:X-Text:all": [" =?iso-8859-1?q?this=20is=20some=20text?=", " =?ISO-8859-1?Q?a?= =?ISO-8859-2?Q?_b?="], "header:X-Text:asText": "a b", "header:X-Text:asText:all": ["this is some text", "a b"], "header:X-Text-Single": " =?ISO-8859-1?B?SWYgeW91IGNhbiByZWFkIHRoaXMgeW8=?=\n              =?ISO-8859-2?B?dSB1bmRlcnN0YW5kIHRoZSBleGFtcGxlLg==?=", "header:X-Text-Single:all": [" =?ISO-8859-1?B?SWYgeW91IGNhbiByZWFkIHRoaXMgeW8=?=\n              =?ISO-8859-2?B?dSB1bmRlcnN0YW5kIHRoZSBleGFtcGxlLg==?="], "header:X-Text-Single:asText": "If you can read this you understand the example.", "header:X-Text-Single:asText:all": ["If you can read this you understand the example."]}