{"mailboxIds": {"a": true}, "keywords": {"tag": true}, "size": 870, "receivedAt": "1997-07-27T10:40:00Z", "subject": "RFC 8621 Section 4.1.4 test", "bodyStructure": {"headers": [{"name": "Subject", "value": " RFC 8621 Section 4.1.4 test"}, {"name": "Content-Type", "value": " multipart/mixed; boundary=\"1\""}], "type": "multipart/mixed", "subParts": [{"partId": "1", "blobId": "blob_0", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"headers": [{"name": "Content-Type", "value": " multipart/mixed; boundary=\"2\""}], "type": "multipart/mixed", "subParts": [{"headers": [{"name": "Content-Type", "value": " multipart/alternative; boundary=\"3\""}], "type": "multipart/alternative", "subParts": [{"headers": [{"name": "Content-Type", "value": " multipart/mixed; boundary=\"4\""}], "type": "multipart/mixed", "subParts": [{"partId": "5", "blobId": "blob_1", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"partId": "6", "blobId": "blob_2", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}, {"name": "Content-Disposition", "value": " inline"}], "type": "image/jpeg", "disposition": "inline"}, {"partId": "7", "blobId": "blob_3", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}]}, {"headers": [{"name": "Content-Type", "value": " multipart/related; boundary=\"5\""}], "type": "multipart/related", "subParts": [{"partId": "9", "blobId": "blob_4", "size": 14, "headers": [{"name": "Content-Type", "value": " text/html"}], "type": "text/html", "charset": "us-ascii"}, {"partId": "10", "blobId": "blob_5", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}], "type": "image/jpeg"}]}]}, {"partId": "11", "blobId": "blob_6", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}, {"name": "Content-Disposition", "value": " attachment"}], "type": "image/jpeg", "disposition": "attachment"}, {"partId": "12", "blobId": "blob_7", "size": 1, "headers": [{"name": "Content-Type", "value": " application/x-excel"}], "type": "application/x-excel"}, {"partId": "13", "blobId": "blob_8", "size": 13, "headers": [{"name": "Content-Type", "value": " message/rfc822"}], "type": "message/rfc822"}]}, {"partId": "14", "blobId": "blob_9", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}]}, "bodyValues": {"1": {"value": "A", "isEncodingProblem": false, "isTruncated": false}, "14": {"value": "K", "isEncodingProblem": false, "isTruncated": false}, "5": {"value": "B", "isEncodingProblem": false, "isTruncated": false}, "7": {"value": "D", "isEncodingProblem": false, "isTruncated": false}, "9": {"value": "<html>E</html>", "isEncodingProblem": false, "isTruncated": false}}, "textBody": [{"partId": "1", "blobId": "blob_0", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"partId": "5", "blobId": "blob_1", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"partId": "6", "blobId": "blob_2", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}, {"name": "Content-Disposition", "value": " inline"}], "type": "image/jpeg", "disposition": "inline"}, {"partId": "7", "blobId": "blob_3", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"partId": "14", "blobId": "blob_9", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}], "htmlBody": [{"partId": "1", "blobId": "blob_0", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}, {"partId": "9", "blobId": "blob_4", "size": 14, "headers": [{"name": "Content-Type", "value": " text/html"}], "type": "text/html", "charset": "us-ascii"}, {"partId": "14", "blobId": "blob_9", "size": 1, "headers": [{"name": "Content-Type", "value": " text/plain"}, {"name": "Content-Disposition", "value": " inline"}], "type": "text/plain", "charset": "us-ascii", "disposition": "inline"}], "attachments": [{"partId": "6", "blobId": "blob_2", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}, {"name": "Content-Disposition", "value": " inline"}], "type": "image/jpeg", "disposition": "inline"}, {"partId": "10", "blobId": "blob_5", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}], "type": "image/jpeg"}, {"partId": "11", "blobId": "blob_6", "size": 1, "headers": [{"name": "Content-Type", "value": " image/jpeg"}, {"name": "Content-Disposition", "value": " attachment"}], "type": "image/jpeg", "disposition": "attachment"}, {"partId": "12", "blobId": "blob_7", "size": 1, "headers": [{"name": "Content-Type", "value": " application/x-excel"}], "type": "application/x-excel"}, {"partId": "13", "blobId": "blob_8", "size": 13, "headers": [{"name": "Content-Type", "value": " message/rfc822"}], "type": "message/rfc822"}], "hasAttachment": true, "preview": "A"}