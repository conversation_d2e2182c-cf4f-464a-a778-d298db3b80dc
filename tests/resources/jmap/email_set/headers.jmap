{"mailboxIds": {"a": true}, "keywords": {"$draft": true, "$seen": true}, "receivedAt": "2018-07-10T01:03:11Z", "messageId": ["my-message-id"], "inReplyTo": ["other-message-id", "yet-another-message-id"], "references": ["first-message-id", "second-message-id"], "sender": [{"name": "ハロー・ワールド", "email": "<EMAIL>"}], "from": [{"name": "<PERSON>", "email": "<EMAIL>"}], "to": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> Freed", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "cc": [{"name": "Привет, мир", "email": "<EMAIL>"}], "bcc": [{"name": "¡El ñandú comió ñoquis!", "email": "<EMAIL>"}], "replyTo": [{"name": "안녕하세요 세계", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "subject": "Headers test", "sentAt": "2018-07-10T01:03:11Z", "bodyStructure": {"headers": [{"name": "Bcc", "value": " \"=?utf-8?B?wqFFbCDDsWFuZMO6IGNvbWnDsyDDsW9xdWlzIQ==?=\"\r\n\t<<EMAIL>>"}, {"name": "Cc", "value": " \"=?utf-8?B?0J/RgNC40LLQtdGCLCDQvNC40YA=?=\" <<EMAIL>>"}, {"name": "Date", "value": " <PERSON><PERSON>, 10 Jul 2018 01:03:11 +0000"}, {"name": "From", "value": " \"<PERSON> Bloggs\" <<EMAIL>>"}, {"name": "In-Reply-To", "value": " <other-message-id> <yet-another-message-id>"}, {"name": "List-Owner", "value": " <http://www.host.com/list.cgi?cmd=sub&lst=list>,\r\n\t<mailto:<EMAIL>?body=subscribe%20list>"}, {"name": "List-Subscribe", "value": " <ftp://ftp.host.com/list.txt>,\r\n\t<mailto:<EMAIL>?subject=subscribe>"}, {"name": "List-Subscribe", "value": " <mailto:<EMAIL>?body=subscribe%20list>"}, {"name": "Message-ID", "value": " <my-message-id>"}, {"name": "References", "value": " <first-message-id> <second-message-id>"}, {"name": "Reply-To", "value": " \"=?utf-8?B?7JWI64WV7ZWY7IS47JqUIOyEuOqzhA==?=\" <<EMAIL>>, \r\n\t\"=?utf-8?Q?<PERSON>_<PERSON>_Saint-Exup=C3=A9ry?=\" <<EMAIL>>"}, {"name": "Resent-Date", "value": " Sat, 2 Jul 2005 09:52:37 +0000"}, {"name": "Resent-Date", "value": " Sun, 3 Jul 2005 09:52:37 +0000"}, {"name": "Resent-Date", "value": " Mon, 4 Jul 2005 09:52:37 +0000"}, {"name": "Sender", "value": " \"=?utf-8?B?44OP44Ot44O844O744Ov44O844Or44OJ?=\" <<EMAIL>>"}, {"name": "Subject", "value": " Headers test"}, {"name": "To", "value": " \"<PERSON>\" <<EMAIL>>, \r\n\t\"<PERSON>d\" <<EMAIL>>, \"<PERSON>\" <<EMAIL>>"}, {"name": "X-AddressesGroup", "value": " \"A Group\": \"<PERSON>\" <<EMAIL>>, \r\n\t<<EMAIL>>, \"<PERSON>\" <<EMAIL>>;"}, {"name": "X-AddressesGroup", "value": " \"List 1\": <<EMAIL>>, \r\n\t<<EMAIL>>;\"List 2\": <<EMAIL>>, \r\n\t<<EMAIL>>;<<EMAIL>>, \r\n\t<<EMAIL>>;"}, {"name": "X-References", "value": " <<EMAIL>> <<EMAIL>>"}, {"name": "X-References", "value": " <<EMAIL>> <<EMAIL>>"}, {"name": "X-Text", "value": " a b"}, {"name": "X-Text", "value": " this is some text"}, {"name": "MIME-Version", "value": " 1.0"}, {"name": "Content-Type", "value": " multipart/alternative; \r\n\tboundary=\"boundary_0\""}], "type": "multipart/alternative", "subParts": [{"partId": "1", "blobId": "blob_0", "size": 81, "headers": [{"name": "Content-Language", "value": " en"}, {"name": "Content-Type", "value": " text/plain; charset=\"us-ascii\""}, {"name": "<PERSON>-<PERSON><PERSON>", "value": " just a value"}, {"name": "X-Text", "value": " more text"}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "type": "text/plain", "charset": "us-ascii", "language": ["en"]}, {"partId": "2", "blobId": "blob_1", "size": 218, "headers": [{"name": "Content-Location", "value": " https://example.com/html-body.html"}, {"name": "Content-Type", "value": " text/html; charset=\"utf-8\"; name=\"html-body.html\""}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "name": "html-body.html", "type": "text/html", "charset": "utf-8", "location": "https://example.com/html-body.html"}]}, "bodyValues": {"1": {"value": "I have the most brilliant plan.  Let me tell you all about it.  What we do is, we", "isEncodingProblem": false, "isTruncated": false}, "2": {"value": "<!DOCTYPE html><html><head><title></title><style type=\"text/css\">div{font-size:16px}</style>...", "isEncodingProblem": false, "isTruncated": true}}, "textBody": [{"partId": "1", "blobId": "blob_0", "size": 81, "headers": [{"name": "Content-Language", "value": " en"}, {"name": "Content-Type", "value": " text/plain; charset=\"us-ascii\""}, {"name": "<PERSON>-<PERSON><PERSON>", "value": " just a value"}, {"name": "X-Text", "value": " more text"}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "type": "text/plain", "charset": "us-ascii", "language": ["en"]}], "htmlBody": [{"partId": "2", "blobId": "blob_1", "size": 218, "headers": [{"name": "Content-Location", "value": " https://example.com/html-body.html"}, {"name": "Content-Type", "value": " text/html; charset=\"utf-8\"; name=\"html-body.html\""}, {"name": "Content-Transfer-Encoding", "value": " 7bit"}], "name": "html-body.html", "type": "text/html", "charset": "utf-8", "location": "https://example.com/html-body.html"}], "attachments": [], "hasAttachment": false, "preview": "I have the most brilliant plan.  Let me tell you all about it.  What we do is, we"}