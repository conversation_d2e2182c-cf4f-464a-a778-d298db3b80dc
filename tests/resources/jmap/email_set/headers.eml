Bcc: "=?utf-8?B?wqFFbCDDsWFuZMO6IGNvbWnDsyDDsW9xdWlzIQ==?="
	<<EMAIL>>
Cc: "=?utf-8?B?0J/RgNC40LLQtdGCLCDQvNC40YA=?=" <<EMAIL>>
Date: Tue, 10 Jul 2018 01:03:11 +0000
From: "<PERSON> Bloggs" <<EMAIL>>
In-Reply-To: <other-message-id> <yet-another-message-id>
List-Owner: <http://www.host.com/list.cgi?cmd=sub&lst=list>,
	<mailto:<EMAIL>?body=subscribe%20list>
List-Subscribe: <ftp://ftp.host.com/list.txt>,
	<mailto:<EMAIL>?subject=subscribe>
List-Subscribe: <mailto:<EMAIL>?body=subscribe%20list>
Message-ID: <my-message-id>
References: <first-message-id> <second-message-id>
Reply-To: "=?utf-8?B?7JWI64WV7ZWY7IS47JqUIOyEuOqzhA==?=" <<EMAIL>>, 
	"=?utf-8?Q?Antoine_de_Saint-Exup=C3=A9ry?=" <<EMAIL>>
Resent-Date: Sat, 2 Jul 2005 09:52:37 +0000
Resent-Date: Sun, 3 Jul 2005 09:52:37 +0000
Resent-Date: Mon, 4 Jul 2005 09:52:37 +0000
Sender: "=?utf-8?B?44OP44Ot44O844O744Ov44O844Or44OJ?=" <<EMAIL>>
Subject: Headers test
To: "Greg Vaudreuil" <<EMAIL>>, 
	"Ned Freed" <<EMAIL>>, "Keith Moore" <<EMAIL>>
X-AddressesGroup: "A Group": "Ed Jones" <<EMAIL>>, 
	<<EMAIL>>, "John" <<EMAIL>>;
X-AddressesGroup: "List 1": <<EMAIL>>, 
	<<EMAIL>>;"List 2": <<EMAIL>>, 
	<<EMAIL>>;<<EMAIL>>, 
	<<EMAIL>>;
X-References: <<EMAIL>> <<EMAIL>>
X-References: <<EMAIL>> <<EMAIL>>
X-Text: a b
X-Text: this is some text
MIME-Version: 1.0
Content-Type: multipart/alternative; 
	boundary="boundary_0"


--boundary_0
Content-Language: en
Content-Type: text/plain; charset="us-ascii"
X-Header: just a value
X-Text: more text
Content-Transfer-Encoding: 7bit

I have the most brilliant plan.  Let me tell you all about it.  What we do is, we
--boundary_0
Content-Location: https://example.com/html-body.html
Content-Type: text/html; charset="utf-8"; name="html-body.html"
Content-Transfer-Encoding: 7bit

<!DOCTYPE html><html><head><title></title><style type="text/css">div{font-size:16px}</style></head><body><div>I have the most <b>brilliant</b> plan.  Let me tell you all about it.  What we do is, we</div></body></html>
--boundary_0--
