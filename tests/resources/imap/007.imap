BODY (
   (
      "text" "plain" (
         "charset" "us-ascii"
      ) NIL NIL "7bit" 1 0
   )(
      (
         (
            (
               "text" "plain" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 1 0
            )(
               "image" "jpeg" NIL NIL NIL NIL 1
            )(
               "text" "plain" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 1 0
            ) "mixed"
         )(
            (
               "text" "html" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 14 0
            )(
               "image" "jpeg" NIL NIL NIL NIL 1
            ) "related"
         ) "alternative"
      )(
         "image" "jpeg" NIL NIL NIL NIL 1
      )(
         "application" "x-excel" NIL NIL NIL NIL 1
      )(
         "message" "rfc822" NIL NIL NIL NIL 13 (
            NIL "J" (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 1 0
         ) 0
      ) "mixed"
   )(
      "text" "plain" (
         "charset" "us-ascii"
      ) NIL NIL "7bit" 1 0
   ) "mixed"
)

BODYSTRUCTURE (
   (
      "text" "plain" (
         "charset" "us-ascii"
      ) NIL NIL "7bit" 1 0 "7fc56270e7a70fa81a5935b72eacbe29" (
         "inline" NIL
      ) NIL NIL
   )(
      (
         (
            (
               "text" "plain" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 1 0 "9d5ed678fe57bcca610140957afab571" (
                  "inline" NIL
               ) NIL NIL
            )(
               "image" "jpeg" NIL NIL NIL NIL 1 "0d61f8370cad1d412f80b84d143e1257" (
                  "inline" NIL
               ) NIL NIL
            )(
               "text" "plain" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 1 0 "f623e75af30e62bbd73d6df5b50bb7b5" (
                  "inline" NIL
               ) NIL NIL
            ) "mixed" (
               "boundary" "4"
            ) NIL NIL NIL
         )(
            (
               "text" "html" (
                  "charset" "us-ascii"
               ) NIL NIL "7bit" 14 0 "ece7293a99c7e12f8d044b683e5c2f33" NIL NIL NIL
            )(
               "image" "jpeg" NIL NIL NIL NIL 1 "800618943025315f869e4e1f09471012" NIL NIL NIL
            ) "related" (
               "boundary" "5"
            ) NIL NIL NIL
         ) "alternative" (
            "boundary" "3"
         ) NIL NIL NIL
      )(
         "image" "jpeg" NIL NIL NIL NIL 1 "dfcf28d0734569a6a693bc8194de62bf" (
            "attachment" NIL
         ) NIL NIL
      )(
         "application" "x-excel" NIL NIL NIL NIL 1 "c1d9f50f86825a1a2302ec2449c17196" NIL NIL NIL
      )(
         "message" "rfc822" NIL NIL NIL NIL 13 (
            NIL "J" (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) (
               (
                  NIL NIL "unknown" "localhost"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 1 0 "ff44570aca8241914870afbc310cdb85" NIL NIL NIL
         ) 0 "9c9888d1b2c167dd33f7542df5a65aa7" NIL NIL NIL
      ) "mixed" (
         "boundary" "2"
      ) NIL NIL NIL
   )(
      "text" "plain" (
         "charset" "us-ascii"
      ) NIL NIL "7bit" 1 0 "a5f3c6a11b03839d46af9fb43c97c188" (
         "inline" NIL
      ) NIL NIL
   ) "mixed" (
      "boundary" "1"
   ) NIL NIL NIL
)

BODY[] {871}
Subject: RFC 8621 Section 4.1.4 test
Content-Type: multipart/mixed; boundary="1"

--1
Content-Type: text/plain
Content-Disposition: inline

A
--1  
Content-Type: multipart/mixed; boundary="2"

--2
Content-Type: multipart/alternative; boundary="3"

--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

--2   
Content-Type: image/jpeg
Content-Disposition: attachment

G
--2  
Content-Type: application/x-excel

H
--2  
Content-Type: message/rfc822

Subject: J

J
--2--

--1  
Content-Type: text/plain
Content-Disposition: inline

K
--1--


BINARY[] {16}
[binary content]
BINARY.SIZE[] 871
----------------------------------
BODY[HEADER] {82}
Subject: RFC 8621 Section 4.1.4 test
Content-Type: multipart/mixed; boundary="1"


----------------------------------
BODY[TEXT] {789}
--1
Content-Type: text/plain
Content-Disposition: inline

A
--1  
Content-Type: multipart/mixed; boundary="2"

--2
Content-Type: multipart/alternative; boundary="3"

--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

--2   
Content-Type: image/jpeg
Content-Disposition: attachment

G
--2  
Content-Type: application/x-excel

H
--2  
Content-Type: message/rfc822

Subject: J

J
--2--

--1  
Content-Type: text/plain
Content-Disposition: inline

K
--1--


----------------------------------
BODY[MIME] {46}
Content-Type: multipart/mixed; boundary="1"


----------------------------------
BODY[1] {1}
A
BINARY[1] {1}
A
BINARY.SIZE[1] 1
----------------------------------
BODY[1.HEADER] {54}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[1.TEXT] {1}
A
----------------------------------
BODY[1.MIME] {55}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[1.1] {1}
A
BINARY[1.1] {1}
A
BINARY.SIZE[1.1] 1
----------------------------------
BODY[2] {608}
--2
Content-Type: multipart/alternative; boundary="3"

--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

--2   
Content-Type: image/jpeg
Content-Disposition: attachment

G
--2  
Content-Type: application/x-excel

H
--2  
Content-Type: message/rfc822

Subject: J

J
--2--

BINARY[2] {16}
[binary content]
BINARY.SIZE[2] 653
----------------------------------
BODY[2.HEADER] {45}
Content-Type: multipart/mixed; boundary="2"


----------------------------------
BODY[2.TEXT] {608}
--2
Content-Type: multipart/alternative; boundary="3"

--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

--2   
Content-Type: image/jpeg
Content-Disposition: attachment

G
--2  
Content-Type: application/x-excel

H
--2  
Content-Type: message/rfc822

Subject: J

J
--2--

----------------------------------
BODY[2.MIME] {46}
Content-Type: multipart/mixed; boundary="2"


----------------------------------
BODY[2.1] {386}
--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

BINARY[2.1] {16}
[binary content]
BINARY.SIZE[2.1] 437
----------------------------------
BODY[2.1.HEADER] {51}
Content-Type: multipart/alternative; boundary="3"


----------------------------------
BODY[2.1.TEXT] {386}
--3
Content-Type: multipart/mixed; boundary="4"

--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

--3 
Content-Type: multipart/related; boundary="5"

--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

--3-- 

----------------------------------
BODY[2.1.MIME] {52}
Content-Type: multipart/alternative; boundary="3"


----------------------------------
BODY[2.1.1] {190}
--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

BINARY[2.1.1] {16}
[binary content]
BINARY.SIZE[2.1.1] 235
----------------------------------
BODY[2.1.1.HEADER] {45}
Content-Type: multipart/mixed; boundary="4"


----------------------------------
BODY[2.1.1.TEXT] {190}
--4
Content-Type: text/plain
Content-Disposition: inline

B
--4  
Content-Type: image/jpeg
Content-Disposition: inline

C
--4  
Content-Type: text/plain
Content-Disposition: inline

D
--4--

----------------------------------
BODY[2.1.1.MIME] {46}
Content-Type: multipart/mixed; boundary="4"


----------------------------------
BODY[*******] {1}
B
BINARY[*******] {1}
B
BINARY.SIZE[*******] 1
----------------------------------
BODY[*******.HEADER] {54}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[*******.TEXT] {1}
B
----------------------------------
BODY[*******.MIME] {55}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[*******.1] {1}
B
BINARY[*******.1] {1}
B
BINARY.SIZE[*******.1] 1
----------------------------------
BODY[*******] {1}
C
BINARY[*******] {16}
[binary content]
BINARY.SIZE[*******] 1
----------------------------------
BODY[*******.HEADER] {54}
Content-Type: image/jpeg
Content-Disposition: inline


----------------------------------
BODY[*******.TEXT] {1}
C
----------------------------------
BODY[*******.MIME] {55}
Content-Type: image/jpeg
Content-Disposition: inline


----------------------------------
BODY[*******.1] {1}
C
BINARY[*******.1] {16}
[binary content]
BINARY.SIZE[*******.1] 1
----------------------------------
BODY[*******] {1}
D
BINARY[*******] {1}
D
BINARY.SIZE[*******] 1
----------------------------------
BODY[*******.HEADER] {54}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[*******.TEXT] {1}
D
----------------------------------
BODY[*******.MIME] {55}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[*******.1] {1}
D
BINARY[*******.1] {1}
D
BINARY.SIZE[*******.1] 1
----------------------------------
BODY[2.1.2] {86}
--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

BINARY[2.1.2] {16}
[binary content]
BINARY.SIZE[2.1.2] 133
----------------------------------
BODY[2.1.2.HEADER] {47}
Content-Type: multipart/related; boundary="5"


----------------------------------
BODY[2.1.2.TEXT] {86}
--5
Content-Type: text/html

<html>E</html>
--5  
Content-Type: image/jpeg

F
--5--  

----------------------------------
BODY[2.1.2.MIME] {48}
Content-Type: multipart/related; boundary="5"


----------------------------------
BODY[*******] {14}
<html>E</html>
BINARY[*******] {14}
<html>E</html>
BINARY.SIZE[*******] 14
----------------------------------
BODY[*******.HEADER] {25}
Content-Type: text/html


----------------------------------
BODY[*******.TEXT] {14}
<html>E</html>
----------------------------------
BODY[*******.MIME] {26}
Content-Type: text/html


----------------------------------
BODY[*******.1] {14}
<html>E</html>
BINARY[*******.1] {14}
<html>E</html>
BINARY.SIZE[*******.1] 14
----------------------------------
BODY[*******] {1}
F
BINARY[*******] {16}
[binary content]
BINARY.SIZE[*******] 1
----------------------------------
BODY[*******.HEADER] {26}
Content-Type: image/jpeg


----------------------------------
BODY[*******.TEXT] {1}
F
----------------------------------
BODY[*******.MIME] {27}
Content-Type: image/jpeg


----------------------------------
BODY[*******.1] {1}
F
BINARY[*******.1] {16}
[binary content]
BINARY.SIZE[*******.1] 1
----------------------------------
BODY[2.2] {1}
G
BINARY[2.2] {16}
[binary content]
BINARY.SIZE[2.2] 1
----------------------------------
BODY[2.2.HEADER] {58}
Content-Type: image/jpeg
Content-Disposition: attachment


----------------------------------
BODY[2.2.TEXT] {1}
G
----------------------------------
BODY[2.2.MIME] {59}
Content-Type: image/jpeg
Content-Disposition: attachment


----------------------------------
BODY[2.2.1] {1}
G
BINARY[2.2.1] {16}
[binary content]
BINARY.SIZE[2.2.1] 1
----------------------------------
BODY[2.3] {1}
H
BINARY[2.3] {16}
[binary content]
BINARY.SIZE[2.3] 1
----------------------------------
BODY[2.3.HEADER] {35}
Content-Type: application/x-excel


----------------------------------
BODY[2.3.TEXT] {1}
H
----------------------------------
BODY[2.3.MIME] {36}
Content-Type: application/x-excel


----------------------------------
BODY[2.3.1] {1}
H
BINARY[2.3.1] {16}
[binary content]
BINARY.SIZE[2.3.1] 1
----------------------------------
BODY[2.4] {13}
Subject: J

J
BINARY[2.4] {16}
[binary content]
BINARY.SIZE[2.4] 13
----------------------------------
BODY[2.4.HEADER] {12}
Subject: J


----------------------------------
BODY[2.4.TEXT] {1}
J
----------------------------------
BODY[2.4.MIME] {31}
Content-Type: message/rfc822


----------------------------------
BODY[2.4.1] {1}
J
BINARY[2.4.1] {1}
J
BINARY.SIZE[2.4.1] 1
----------------------------------
BODY[3] {1}
K
BINARY[3] {1}
K
BINARY.SIZE[3] 1
----------------------------------
BODY[3.HEADER] {54}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[3.TEXT] {1}
K
----------------------------------
BODY[3.MIME] {55}
Content-Type: text/plain
Content-Disposition: inline


----------------------------------
BODY[3.1] {1}
K
BINARY[3.1] {1}
K
BINARY.SIZE[3.1] 1
----------------------------------
BODY[HEADER.FIELDS (FROM TO)] {2}


----------------------------------
BODY[HEADER.FIELDS (FROM TO)]<10> {0}

----------------------------------
BODY[HEADER.FIELDS.NOT (SUBJECT CC)] {46}
Content-Type: multipart/mixed; boundary="1"


----------------------------------
BODY[HEADER.FIELDS.NOT (SUBJECT CC)]<10> {25}
pe: multipart/mixed; boun
----------------------------------
