BODY (
   "message" "rfc822" NIL NIL NIL NIL 271 (
      "Sun, 12 Aug 2012 12:34:56 +0300" "submsg" (
         (
            NIL NIL "sub" "domain.org"
         )
      ) (
         (
            NIL NIL "sub" "domain.org"
         )
      ) (
         (
            NIL NIL "sub" "domain.org"
         )
      ) NIL NIL NIL NIL NIL
   ) (
      (
         "message" NIL NIL NIL NIL NIL 42 (
            NIL "m1" (
               (
                  NIL NIL "m1" "example.com"
               )
            ) (
               (
                  NIL NIL "m1" "example.com"
               )
            ) (
               (
                  NIL NIL "m1" "example.com"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 8 1
         ) 0
      )(
         "message" NIL NIL NIL NIL NIL 42 (
            NIL "m2" (
               (
                  NIL NIL "m2" "example.com"
               )
            ) (
               (
                  NIL NIL "m2" "example.com"
               )
            ) (
               (
                  NIL NIL "m2" "example.com"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 8 1
         ) 0
      ) "digest"
   ) 0
)

BODYSTRUCTURE (
   "message" "rfc822" NIL NIL NIL NIL 271 (
      "Sun, 12 Aug 2012 12:34:56 +0300" "submsg" (
         (
            NIL NIL "sub" "domain.org"
         )
      ) (
         (
            NIL NIL "sub" "domain.org"
         )
      ) (
         (
            NIL NIL "sub" "domain.org"
         )
      ) NIL NIL NIL NIL NIL
   ) (
      (
         "message" NIL NIL NIL NIL NIL 42 (
            NIL "m1" (
               (
                  NIL NIL "m1" "example.com"
               )
            ) (
               (
                  NIL NIL "m1" "example.com"
               )
            ) (
               (
                  NIL NIL "m1" "example.com"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 8 1 "8dc313ad8cf1d82dbe8d46f5f0d3d79c" NIL NIL NIL
         ) 0 "702907ad1c165219425153a8f0a5f578" NIL NIL NIL
      )(
         "message" NIL NIL NIL NIL NIL 42 (
            NIL "m2" (
               (
                  NIL NIL "m2" "example.com"
               )
            ) (
               (
                  NIL NIL "m2" "example.com"
               )
            ) (
               (
                  NIL NIL "m2" "example.com"
               )
            ) NIL NIL NIL NIL NIL
         ) (
            "text" "plain" (
               "charset" "us-ascii"
            ) NIL NIL "7bit" 8 1 "f344a10ee7adfdcfc29650b6e31601d8" NIL NIL NIL
         ) 0 "0c79449f982ccecbc258d902cd989f69" NIL NIL NIL
      ) "digest" (
         "boundary" "foo"
      ) NIL NIL NIL
   ) 0 "4935800d6cfad87d931093820097206a" NIL NIL NIL
)

BODY[] {379}
From: <EMAIL>
Date: Sat, 24 Mar 2007 23:00:00 +0200
Mime-Version: 1.0
Content-Type: message/rfc822

From: <EMAIL>
Date: Sun, 12 Aug 2012 12:34:56 +0300
Subject: submsg
Content-Type: multipart/digest; boundary="foo"

prologue

--foo 

From: <EMAIL>
Subject: m1

m1 body

--foo 
X-Mime: m2 header

From: <EMAIL>
Subject: m2

m2 body

--foo--

epilogue

BINARY[] {16}
[binary content]
BINARY.SIZE[] 260
----------------------------------
BODY[HEADER] {108}
From: <EMAIL>
Date: Sat, 24 Mar 2007 23:00:00 +0200
Mime-Version: 1.0
Content-Type: message/rfc822


----------------------------------
BODY[TEXT] {271}
From: <EMAIL>
Date: Sun, 12 Aug 2012 12:34:56 +0300
Subject: submsg
Content-Type: multipart/digest; boundary="foo"

prologue

--foo 

From: <EMAIL>
Subject: m1

m1 body

--foo 
X-Mime: m2 header

From: <EMAIL>
Subject: m2

m2 body

--foo--

epilogue

----------------------------------
BODY[MIME] {31}
Content-Type: message/rfc822


----------------------------------
BODY[1] {271}
From: <EMAIL>
Date: Sun, 12 Aug 2012 12:34:56 +0300
Subject: submsg
Content-Type: multipart/digest; boundary="foo"

prologue

--foo 

From: <EMAIL>
Subject: m1

m1 body

--foo 
X-Mime: m2 header

From: <EMAIL>
Subject: m2

m2 body

--foo--

epilogue

BINARY[1] {16}
[binary content]
BINARY.SIZE[1] 260
----------------------------------
BODY[1.HEADER] {123}
From: <EMAIL>
Date: Sun, 12 Aug 2012 12:34:56 +0300
Subject: submsg
Content-Type: multipart/digest; boundary="foo"


----------------------------------
BODY[1.TEXT] {137}
prologue

--foo 

From: <EMAIL>
Subject: m1

m1 body

--foo 
X-Mime: m2 header

From: <EMAIL>
Subject: m2

m2 body

--foo--
----------------------------------
BODY[1.MIME] {31}
Content-Type: message/rfc822


----------------------------------
BODY[1.1] {42}
From: <EMAIL>
Subject: m1

m1 body

BINARY[1.1] {16}
[binary content]
BINARY.SIZE[1.1] 42
----------------------------------
BODY[1.1.HEADER] {34}
From: <EMAIL>
Subject: m1


----------------------------------
BODY[1.1.TEXT] {8}
m1 body

----------------------------------
BODY[1.1.MIME] {2}


----------------------------------
BODY[1.1.1] {8}
m1 body

BINARY[1.1.1] {8}
m1 body

BINARY.SIZE[1.1.1] 8
----------------------------------
BODY[1.2] {42}
From: <EMAIL>
Subject: m2

m2 body

BINARY[1.2] {16}
[binary content]
BINARY.SIZE[1.2] 42
----------------------------------
BODY[1.2.HEADER] {34}
From: <EMAIL>
Subject: m2


----------------------------------
BODY[1.2.TEXT] {8}
m2 body

----------------------------------
BODY[1.2.MIME] {2}


----------------------------------
BODY[1.2.1] {8}
m2 body

BINARY[1.2.1] {8}
m2 body

BINARY.SIZE[1.2.1] 8
----------------------------------
BODY[HEADER.FIELDS (FROM TO)] {24}
From: <EMAIL>


----------------------------------
BODY[HEADER.FIELDS (FROM TO)]<10> {14}
@domain.org


----------------------------------
BODY[HEADER.FIELDS.NOT (SUBJECT CC)] {109}
From: <EMAIL>
Date: Sat, 24 Mar 2007 23:00:00 +0200
MIME-Version: 1.0
Content-Type: message/rfc822


----------------------------------
BODY[HEADER.FIELDS.NOT (SUBJECT CC)]<10> {25}
@domain.org
Date: Sat, 24
----------------------------------
