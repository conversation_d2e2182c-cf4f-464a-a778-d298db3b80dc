#################
# LDAP test config

#################
# General configuration.
debug = true
watchconfig = true

#################
# Server configuration.
[ldap]
  enabled = true
  # run on a non privileged port
  listen = "0.0.0.0:3893"

[ldaps]
# to enable ldaps genrerate a certificate, eg. with:
# openssl req -x509 -newkey rsa:4096 -keyout example.key -out example.crt -days 365 -nodes -subj '/CN=`hostname`'
  enabled = false
  listen = "0.0.0.0:3894"
  cert = "example.crt"
  key = "example.key"

#################
# The backend section controls the data store.
[backend]
  datastore = "config"
  baseDN = "dc=example,dc=org"
  nameformat = "cn"
  groupformat = "ou"
  
[behaviors]
  # Ignore all capabilities restrictions, for instance allowing every user to perform a search
  IgnoreCapabilities = false
  # Enable a "fail2ban" type backoff mechanism temporarily banning repeated failed login attempts
  LimitFailedBinds = true
  # How many failed login attempts are allowed before a ban is imposed
  NumberOfFailedBinds = 3
  # How long (in seconds) is the window for failed login attempts
  PeriodOfFailedBinds = 10
  # How long (in seconds) is the ban duration
  BlockFailedBindsFor = 60
  # Clean learnt IP addresses every N seconds
  PruneSourceTableEvery = 600
  # Clean learnt IP addresses not seen in N seconds
  PruneSourcesOlderThan = 600

#################
# The users section contains a hardcoded list of valid users.
[[users]]
  name = "john"
  givenname = "<EMAIL>"
  sn = "<EMAIL>"
  uidnumber = 2
  primarygroup = 5
  mail = "<EMAIL>"
  [[users.customattributes]]
    principalName = ["John Doe"]
    userPassword = ["12345"]

[[users]]
  name = "jane"
  sn = "<EMAIL>"
  mail = "<EMAIL>"
  uidnumber = 3
  primarygroup = 5
  [[users.customattributes]]
    otherGroups = ["support"]
    principalName = ["Jane Doe"]
    userPassword = ["abcde"]

[[users]]
  name = "bill"
  sn = "<EMAIL>"
  mail = "<EMAIL>"
  uidnumber = 4
  passsha256 = "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"
  [[users.customattributes]]
    principalName = ["Bill Foobar"]
    diskQuota = [500000]
    userPassword = ["$2y$05$bvIG6Nmid91Mu9RcmmWZfO5HJIMCT8riNW0hEp8f6/FuA2/mHZFpe"]

[[users]]
  name = "robert"
  sn = "@catchall.org"
  mail = "<EMAIL>"
  uidnumber = 7
  [[users.customattributes]]
    principalName = ["Robect Foobar"]
    userPassword = ["nopass"]

[[users]]
  name = "serviceuser"
  mail = "<EMAIL>"
  uidnumber = 5003
  primarygroup = 5502
  passsha256 = "652c7dc687d98c9889304ed2e408c74b611e86a40caa51c4b43f1dd5913c5cd0" # mysecret
    [[users.capabilities]]
    action = "search"
    object = "*"


#################
# The groups section contains a hardcoded list of valid users.
[[groups]]
  name = "sales"
  gidnumber = 5

[[groups]]
  name = "support"
  gidnumber = 6

[[groups]]
  name = "svcaccts"
  gidnumber = 5502


#################
# Enable and configure the optional REST API here.
[api]
  enabled = false
  internals = true # debug application performance
  tls = false # enable TLS for production!!
  listen = "0.0.0.0:5555"
  cert = "cert.pem"
  key = "key.pem"
