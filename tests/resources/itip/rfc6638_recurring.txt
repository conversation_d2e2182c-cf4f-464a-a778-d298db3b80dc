# RFC6638 - Recurring Event Scheduling

# Organizer invites participant to a recurring event
> put <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYMONTH=11;BYDAY=1SU
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=2SU
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:0
DTSTAMP:20090602T185254Z
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;INTERVAL=1;COUNT=5
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
METHOD:REQUEST
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;COUNT=5;INTERVAL=1
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=
 TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR

# Send iTIP message to participant
> send

# Make sure the participant receives the event
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;COUNT=5;INTERVAL=1
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=
 TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR

# Participant declines an instance of the recurring event
> put <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYMONTH=11;BYDAY=1SU
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=2SU
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:20090602T185254Z
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;INTERVAL=1;COUNT=5
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:20090603T183823Z
RECURRENCE-ID;TZID=America/Montreal:20090602T150000
DTSTART;TZID=America/Montreal:20090602T150000
DTEND;TZID=America/Montreal:20090602T160000
TRANSP:TRANSPARENT
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=DECLINED;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
METHOD:REPLY
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
REQUEST-STATUS:2.0;Success
END:VEVENT
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
RECURRENCE-ID;TZID=America/Montreal:20090602T150000
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=DECLINED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
REQUEST-STATUS:2.0;Success
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR

# Send iTIP message to organizer
> send

# Organizer receives the response
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:1
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;COUNT=5;INTERVAL=1
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=
 TRUE;PARTSTAT=ACCEPTED;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
END:VEVENT
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
RECURRENCE-ID;TZID=America/Montreal:20090602T150000
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=DECLINED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR

# Participant removes an instance of the recurring event
> put <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYMONTH=11;BYDAY=1SU
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=2SU
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:20090602T185254Z
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;INTERVAL=1;COUNT=5
EXDATE;TZID=America/Montreal:20090603T150000
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:20090603T183823Z
RECURRENCE-ID;TZID=America/Montreal:20090602T150000
DTSTART;TZID=America/Montreal:20090602T150000
DTEND;TZID=America/Montreal:20090602T160000
TRANSP:TRANSPARENT
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=DECLINED;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
METHOD:REPLY
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;PARTSTAT=DECLINED:mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:0
SEQUENCE:1
RECURRENCE-ID;TZID=America/Montreal:20090603T150000
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR

# Send iTIP message to organizer
> send

# Organizer receives the response
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;PARTSTAT=DECLINED:mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:0
SEQUENCE:1
RECURRENCE-ID;TZID=America/Montreal:20090603T150000
END:VEVENT
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:1
DTSTAMP:1
DTSTART;TZID=America/Montreal:20090601T150000
DTEND;TZID=America/Montreal:20090601T160000
RRULE:FREQ=DAILY;COUNT=5;INTERVAL=1
TRANSP:OPAQUE
SUMMARY:Review Internet-Draft
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=
 TRUE;PARTSTAT=ACCEPTED;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
END:VEVENT
BEGIN:VEVENT
DTSTAMP:0
SEQUENCE:1
UID:9263504FD3AD
RECURRENCE-ID;TZID=America/Montreal:20090602T150000
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=DECLINED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
BEGIN:VTIMEZONE
TZID:America/Montreal
BEGIN:STANDARD
DTSTART:20071104T020000
RRULE:FREQ=YEARLY;BYDAY=1SU;BYMONTH=11
TZNAME:EST
TZOFFSETFROM:-0400
TZOFFSETTO:-0500
END:STANDARD
BEGIN:DAYLIGHT
DTSTART:20070311T020000
RRULE:FREQ=YEARLY;BYDAY=2SU;BYMONTH=3
TZNAME:EDT
TZOFFSETFROM:-0500
TZOFFSETTO:-0400
END:DAYLIGHT
END:VTIMEZONE
END:VCALENDAR




