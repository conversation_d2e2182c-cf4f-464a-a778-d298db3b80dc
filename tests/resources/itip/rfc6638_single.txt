# RFC6638 - Simple Event Scheduling

# Organizer Inviting Multiple Attendees
> put <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:0
DTSTAMP:20090602T185254Z
DTSTART:20090602T160000Z
DTEND:20090602T170000Z
TRANSP:OPAQUE
SUMMARY:Lunch
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Wilfredo <PERSON>";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="<PERSON>";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="<PERSON>s";CUTYPE=INDIVIDUAL;RSVP=TRUE:mailto:<EMAIL>
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
SUMMARY:Lunch
DTEND:20090602T170000Z
DTSTART:20090602T160000Z
TRANSP:OPAQUE
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;RSVP=TRUE;PARTSTAT=NEEDS-ACTI
 ON:mailto:<EMAIL>
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Make sure the sequence number is updated
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
PRODID:-//Example Corp.//CalDAV Client//EN
VERSION:2.0
BEGIN:VEVENT
SUMMARY:Lunch
DTEND:20090602T170000Z
DTSTART:20090602T160000Z
TRANSP:OPAQUE
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;RSVP=TRUE:mailto:mike@example.
 org
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:1
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Send iTIP message to attendees
> send

# Make sure the message was received by the attendees
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
SUMMARY:Lunch
DTEND:20090602T170000Z
DTSTART:20090602T160000Z
TRANSP:OPAQUE
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;RSVP=TRUE;PARTSTAT=NEEDS-ACTI
 ON:mailto:<EMAIL>
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Wilfredo accepts the invitation
> put <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Example Corp.//CalDAV Client//EN
BEGIN:VEVENT
UID:9263504FD3AD
SEQUENCE:0
DTSTAMP:20090602T185254Z
DTSTART:20090602T160000Z
DTEND:20090602T170000Z
TRANSP:OPAQUE
SUMMARY:Lunch
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:mailto:<EMAIL>
BEGIN:VALARM
TRIGGER:-PT15M
ACTION:DISPLAY
DESCRIPTION:Reminder
END:VALARM
END:VEVENT
END:VCALENDAR

# Make sure the sequence number is not updated
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
PRODID:-//Example Corp.//CalDAV Client//EN
VERSION:2.0
BEGIN:VEVENT
SUMMARY:Lunch
DTEND:20090602T170000Z
DTSTART:20090602T160000Z
TRANSP:OPAQUE
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;RSVP=TR
 UE:mailto:<EMAIL>
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:1
SEQUENCE:0
BEGIN:VALARM
DESCRIPTION:Reminder
ACTION:DISPLAY
TRIGGER:-PT15M
END:VALARM
END:VEVENT
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED;ROLE=
 REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:0
SEQUENCE:0
REQUEST-STATUS:2.0;Success
END:VEVENT
END:VCALENDAR

# Send ITIP message to organizer
> send

# Make sure the message was received by the organizer
> get <EMAIL> 9263504FD3AD
BEGIN:VCALENDAR
PRODID:-//Example Corp.//CalDAV Client//EN
VERSION:2.0
BEGIN:VEVENT
SUMMARY:Lunch
DTEND:20090602T170000Z
DTSTART:20090602T160000Z
TRANSP:OPAQUE
ATTENDEE;CN="Bernard Desruisseaux";CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION;
 ROLE=REQ-PARTICIPANT;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;CN="Cyrus Daboo";CUTYPE=INDIVIDUAL;PARTSTAT=ACCEPTED:mailto:cyrus@e
 xample.com
ATTENDEE;CN="Mike Douglass";CUTYPE=INDIVIDUAL;RSVP=TRUE:mailto:mike@example.
 org
ATTENDEE;CN="Wilfredo Sanchez Vega";CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;
 RSVP=TRUE;PARTSTAT=ACCEPTED;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
ORGANIZER;CN="Cyrus Daboo":mailto:<EMAIL>
UID:9263504FD3AD
DTSTAMP:1
SEQUENCE:1
END:VEVENT
END:VCALENDAR


