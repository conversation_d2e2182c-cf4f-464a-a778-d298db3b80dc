# RFC5546 - Todo Request

# A sample todo is sent from "A" to "B", "C", and "D".
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
METHOD:REQUEST
VERSION:2.0
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
DTSTART:19970701T170000Z
DUE:19970722T170000Z
PRIORITY:1
SUMMARY:Create the requirements document
UID:<EMAIL>
SEQUENCE:0
DTSTAMP:19970717T200000Z
STATUS:NEEDS-ACTION
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>, <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
END:VTODO
END:VCALENDAR

# Send iTIP request to the attendees
> send

# Make sure B received the todo
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
END:VTODO
END:VCALENDAR

# "B" accepts the to-do.
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;PARTSTAT=ACCEPTED:mailto:<EMAIL>
UID:<EMAIL>
COMMENT:I'll send you my input by email
SEQUENCE:1
PRIORITY:1
STATUS:IN-PROCESS
DTSTART:19970701T170000Z
DUE:19970722T170000Z
DTSTAMP:19970717T203000Z
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
STATUS:IN-PROCESS
ATTENDEE;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
REQUEST-STATUS:2.0;Success
END:VTODO
END:VCALENDAR

# Send iTIP reply to the organizer
> send

# Make sure "A" received the reply
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:IN-PROCESS
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE;PARTSTAT=ACCEPTED;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:1
SEQUENCE:1
END:VTODO
END:VCALENDAR

# "B" updates percent completion of the to-do.
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
PERCENT-COMPLETE:75
ATTENDEE;PARTSTAT=IN-PROCESS:mailto:<EMAIL>
UID:<EMAIL>
COMMENT:I'll send you my input by email
SEQUENCE:1
PRIORITY:1
STATUS:IN-PROCESS
DTSTART:19970701T170000Z
DUE:19970722T170000Z
DTSTAMP:19970717T203000Z
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PERCENT-COMPLETE:75
STATUS:IN-PROCESS
ATTENDEE;PARTSTAT=IN-PROCESS:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
REQUEST-STATUS:2.0;Success
END:VTODO
END:VCALENDAR

# Send iTIP reply to the organizer
> send

# Make sure "A" received the reply
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
PERCENT-COMPLETE:75
PRIORITY:1
STATUS:IN-PROCESS
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE;PARTSTAT=IN-PROCESS;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:1
SEQUENCE:1
END:VTODO
END:VCALENDAR

# "D" completed the to-do.
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;PARTSTAT=COMPLETED:mailto:<EMAIL>
UID:<EMAIL>
COMMENT:I'll send you my input by email
SEQUENCE:1
PRIORITY:1
DTSTART:19970701T170000Z
DUE:19970722T170000Z
DTSTAMP:19970717T203000Z
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
ATTENDEE;PARTSTAT=COMPLETED:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
REQUEST-STATUS:2.0;Success
END:VTODO
END:VCALENDAR

# Send iTIP reply to the organizer
> send

# Make sure "A" received the reply
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
PERCENT-COMPLETE:75
PRIORITY:1
STATUS:IN-PROCESS
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE;PARTSTAT=COMPLETED;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
ATTENDEE;PARTSTAT=IN-PROCESS;SCHEDULE-STATUS=2.0:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:1
SEQUENCE:1
END:VTODO
END:VCALENDAR

# Recurring to-do request
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL:mailto:<EMAIL>
RRULE:FREQ=MONTHLY;COUNT=10;BYDAY=1FR
DTSTART:19980101T100000Z
DUE:19980103T100000Z
SUMMARY:Send Status Reports to Area Managers
UID:<EMAIL>
SEQUENCE:0
DTSTAMP:19970717T200000Z
STATUS:NEEDS-ACTION
PRIORITY:1
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>, <EMAIL>
changes: ATTENDEE, DTSTART, DUE, PERCENT-COMPLETE, RRULE, STATUS, SUMMARY
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Send Status Reports to Area Managers
DUE:19980103T100000Z
DTSTART:19980101T100000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:b@example.
 com
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:d@example.
 com
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;COUNT=10;BYDAY=1FR
DTSTAMP:0
SEQUENCE:1
END:VTODO
END:VCALENDAR
================================
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:CANCEL
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
STATUS:CANCELLED
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:2
END:VTODO
END:VCALENDAR

> send

# Make sure "C" received the cancel request
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:CANCELLED
SUMMARY:Create the requirements document
DUE:19970722T170000Z
DTSTART:19970701T170000Z
ATTENDEE:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
DTSTAMP:0
END:VTODO
END:VCALENDAR

# Make sure "B" received the updated to-do
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
COMMENT:I'll send you my input by email
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Send Status Reports to Area Managers
DUE:19980103T100000Z
DTSTART:19980101T100000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:b@example.
 com
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:d@example.
 com
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;COUNT=10;BYDAY=1FR
DTSTAMP:0
SEQUENCE:1
END:VTODO
END:VCALENDAR

# Reply to an instance of a recurring to-do
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
COMMENT:I'll send you my input by email
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Send Status Reports to Area Managers
DUE:19980103T100000Z
DTSTART:19980101T100000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:b@example.
 com
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;PARTSTAT=NEEDS-ACTION:mailto:d@example.
 com
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;COUNT=10;BYDAY=1FR
DTSTAMP:19970717T233000Z
SEQUENCE:1
END:VTODO
BEGIN:VTODO
ORGANIZER:mailto:<EMAIL>
ATTENDEE;PARTSTAT=IN-PROCESS:mailto:<EMAIL>
PERCENT-COMPLETE:75
UID:<EMAIL>
DTSTAMP:19970717T233000Z
RECURRENCE-ID:19980101T170000Z
SEQUENCE:1
END:VTODO
END:VCALENDAR

> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REPLY
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VTODO
PERCENT-COMPLETE:75
ATTENDEE;PARTSTAT=IN-PROCESS:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19980101T170000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
REQUEST-STATUS:2.0;Success
END:VTODO
END:VCALENDAR

> send

# Make sure "A" received the reply
> get <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VTODO
PRIORITY:1
STATUS:NEEDS-ACTION
SUMMARY:Send Status Reports to Area Managers
DUE:19980103T100000Z
DTSTART:19980101T100000Z
ATTENDEE;ROLE=CHAIR:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RRULE:FREQ=MONTHLY;COUNT=10;BYDAY=1FR
DTSTAMP:1
SEQUENCE:1
END:VTODO
BEGIN:VTODO
PERCENT-COMPLETE:75
ATTENDEE;PARTSTAT=IN-PROCESS:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
RECURRENCE-ID:19980101T170000Z
UID:<EMAIL>
DTSTAMP:0
SEQUENCE:1
END:VTODO
END:VCALENDAR


