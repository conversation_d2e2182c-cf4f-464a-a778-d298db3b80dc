# iTIP tests

# Recipient is not organizer or attendee
> itip <EMAIL> <EMAIL>
BEGIN:VCALENDAR
PRODID:-//Example/ExampleCalendarClient//EN
METHOD:REQUEST
VERSION:2.0
BEGIN:VEVENT
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED;CN=A:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=B:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=C:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;CUTYPE=INDIVIDUAL;CN=Hal:mailto:<EMAIL>
ATTENDEE;RSVP=FALSE;CUTYPE=ROOM:<EMAIL>
ATTENDEE;ROLE=NON-PARTICIPANT;RSVP=FALSE:mailto:<EMAIL>
DTSTAMP:19970611T190000Z
DTSTART:19970701T200000Z
DTEND:19970701T2100000Z
SUMMARY:Conference
UID:<EMAIL>
SEQUENCE:0
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

> send
NotOrganizerNorAttendee

> reset

# Refreshing an event (1)
> put <EMAIL> <EMAIL>
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:0
RDATE:19980304T180000Z
RDATE:19980311T180000Z
RDATE:19980318T180000Z
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
SUMMARY:Review Accounts
DTSTART:19980304T180000Z
DTEND:19980304T200000Z
DTSTAMP:19980303T193000Z
LOCATION:Conference Room A
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR

# Refreshing an event (2)
> expect
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:Conference Room A
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980304T200000Z
DTSTART:19980304T180000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RDATE:19980304T180000Z
RDATE:19980311T180000Z
RDATE:19980318T180000Z
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR

# Refreshing an event (3)
> send

# Refreshing an event (4)
> itip <EMAIL> <EMAIL>
BEGIN:VCALENDAR
METHOD:REFRESH
PRODID:-//Example/ExampleCalendarClient//EN
VERSION:2.0
BEGIN:VEVENT
UID:<EMAIL>
SEQUENCE:0
ORGANIZER:mailto:<EMAIL>
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE:mailto:<EMAIL>
DTSTART:19980304T180000Z
END:VEVENT
END:VCALENDAR

# Refreshing an event (5)
> send
from: <EMAIL>
to: <EMAIL>
changes: 
BEGIN:VCALENDAR
METHOD:REQUEST
PRODID:-//Stalwart Labs LLC//Stalwart Server//EN
VERSION:2.0
BEGIN:VEVENT
LOCATION:Conference Room A
STATUS:CONFIRMED
SUMMARY:Review Accounts
DTEND:19980304T200000Z
DTSTART:19980304T180000Z
ATTENDEE;ROLE=CHAIR;PARTSTAT=ACCEPTED:mailto:<EMAIL>
ATTENDEE;RSVP=TRUE;PARTSTAT=NEEDS-ACTION:mailto:<EMAIL>
ORGANIZER:mailto:<EMAIL>
UID:<EMAIL>
RDATE:19980304T180000Z
RDATE:19980311T180000Z
RDATE:19980318T180000Z
DTSTAMP:0
SEQUENCE:1
END:VEVENT
END:VCALENDAR



