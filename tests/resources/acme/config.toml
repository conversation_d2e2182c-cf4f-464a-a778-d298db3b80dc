acme.pebble.contact = "<EMAIL>"
acme.pebble.directory = "https://localhost:14000/dir"
#acme.pebble.domains = "mail.example.org"
acme.pebble.renew-before = "30d"

acme.pebble.challenge = "tls-alpn-01"
#acme.pebble.challenge = "http-01"
#acme.pebble.challenge = "dns-01"

acme.pebble.domains = "*.example.org"
acme.pebble.provider = "cloudflare"
acme.pebble.secret = "<KEY>"

authentication.fallback-admin.secret = "secret"
authentication.fallback-admin.user = "admin"
config.local-keys.0 = "*"
directory.internal.store = "rocksdb"
directory.internal.type = "internal"
lookup.default.hostname = "mail.example.org"
lookup.default.domain = "example.org"
oauth.key = "0Wn7rO4UdmBoE8mp3cDcD9Qlpz3na74z7fGRoSuq8fVsGPelLl3KrHomBN8h2biA"
queue.quota.size.enable = true
queue.quota.size.messages = 100000
queue.quota.size.size = 10737418240
report.analysis.addresses = "postmaster@*"
server.http.permissive-cors = true
server.listener.http.bind = "[::]:5002"
server.listener.http.protocol = "http"
server.listener.https.bind = "[::]:5001"
server.listener.https.protocol = "http"
server.listener.https.tls.implicit = true
server.listener.imap.bind = "[::]:143"
server.listener.imap.protocol = "imap"
server.listener.imaptls.bind = "[::]:993"
server.listener.imaptls.protocol = "imap"
server.listener.imaptls.tls.implicit = true
server.listener.sieve.bind = "[::]:4190"
server.listener.sieve.protocol = "managesieve"
server.listener.smtp.bind = "[::]:25"
server.listener.smtp.protocol = "smtp"
server.listener.submission.bind = "[::]:587"
server.listener.submission.protocol = "smtp"
server.listener.submissions.bind = "[::]:465"
server.listener.submissions.protocol = "smtp"
server.listener.submissions.tls.implicit = true
storage.blob = "rocksdb"
storage.data = "rocksdb"
storage.directory = "internal"
storage.fts = "rocksdb"
storage.lookup = "rocksdb"
store.rocksdb.compression = "lz4"
store.rocksdb.path = "/tmp/stalwart-temp-data"
store.rocksdb.type = "rocksdb"
tracer.stdout.ansi = true
tracer.stdout.enable = true
tracer.stdout.level = "trace"
tracer.stdout.type = "stdout"
version.spam-filter = 1.0
