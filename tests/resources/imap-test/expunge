connections: 2
messages: 8

# get UIDs
1 ok fetch 1:4 uid
* 1 fetch (uid $uid1)
* 2 fetch (uid $uid2)
* 3 fetch (uid $uid3)
* 4 fetch (uid $uid4)

# 1) test that expunges work ok and session 2 fetch sees 1's flag changes.
1 ok store 1,3 flags \deleted
* 1 fetch (flags (\deleted))
* 3 fetch (flags (\deleted))
1 ok store 2,4 flags \seen
* 2 fetch (flags (\seen))
* 4 fetch (flags (\seen))
1 ok expunge
* $1 expunge
* $3 expunge

2 ok fetch 2,4 (uid)
* 2 fetch (uid $uid2)
* 4 fetch (uid $uid4)
2 ok fetch 2,4 (uid)
* 2 fetch (uid $uid2)
* 4 fetch (uid $uid4)
2 ok check
* $1 expunge
* $3 expunge
2 ok fetch 1:2 (uid flags)
* 1 fetch (uid $uid2 flags (\seen))
* 2 fetch (uid $uid4 flags (\seen))

# 2) test that session 2 can update flags while some messages are expunged
1 ok store 2 +flags \deleted
1 ok expunge

2 ok store 1 flags \answered
* 1 fetch (flags (\answered))
1 ok check
* 1 fetch (flags (\answered))
2 ok noop
* 2 expunge

# 3) check notices flag changes correctly with expunges
1 ok store 1,3 +flags \deleted
1 ok store 2,4 flags \flagged
1 ok expunge
2 ok check
* $1 expunge
* $2 fetch (flags (\flagged))
* $3 expunge
* $4 fetch (flags (\flagged))

# 4) expunging while message is already expunged
1 ok store 1 +flags \deleted
2 ok store 1 +flags \deleted
1 ok expunge
* 1 expunge
2 ok expunge
* 1 expunge
