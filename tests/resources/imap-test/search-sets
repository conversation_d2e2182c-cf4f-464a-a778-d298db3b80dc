messages: 6

ok uid search all
* search $uid1 $uid2 $uid3 $uid4 $uid5 $uid6
ok status $mailbox (uidnext)
* status $mailbox (uidnext $uidnext)

# break seq=uid mapping
ok store 2 +flags \deleted
ok expunge
* 2 expunge

ok search all
* search 1 2 3 4 5
ok uid search all
* search $uid1 $uid3 $uid4 $uid5 $uid6

ok search 1:3,5
* search 1 2 3 5
ok search 4:2
* search 2 3 4
ok search uid $uid1:$uid3,$uid5
* search 1 2 4
ok search uid $uid4:$uid2
* search 2 3

ok search 1:3 not uid $uid3
* search 1 3
ok search not 2,4
* search 1 3 5
ok search or 1 uid $uid3
* search 1 2

ok search *
* search 5
ok search uid *
* search 5
ok search uid $uidnext:*
* search 5
ok search *:3
* search 3 4 5
ok search 1,4,*
* search 1 4 5

# These are in a bit of a grey area. Most servers allow them, but it's not
# explicitly defined in the RFC that they're legal:
#ok search 6:*
#* search 5
#ok search *:6
#* search 5

ok search (3) uid $uid4
* search 3
ok search (uid $uid4) 3
* search 3
ok search 3 (uid $uid4)
* search 3

ok uid search uid 1:4294967295
* search $uid1 $uid3 $uid4 $uid5 $uid6
ok uid search uid $uidnext:4294967295
* search 
