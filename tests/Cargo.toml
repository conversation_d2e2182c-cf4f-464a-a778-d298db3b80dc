[package]
name = "tests"
version = "0.12.4"
edition = "2024"
resolver = "2"

[features]
default = ["sqlite", "postgres", "mysql", "rocks", "elastic", "s3", "redis", "nats", "azure", "foundationdb"]
#default = ["sqlite", "postgres", "mysql", "rocks", "s3", "redis"]
#default = ["rocks", "redis", "s3"]
sqlite = ["store/sqlite"]
foundationdb = ["store/foundation", "common/foundation"]
postgres = ["store/postgres"]
mysql = ["store/mysql"]
rocks = ["store/rocks"]
elastic = ["store/elastic"]
s3 = ["store/s3"]
redis = ["store/redis"]
nats = ["store/nats"]
azure = ["store/azure"]

[dev-dependencies]
store = { path = "../crates/store", features = ["test_mode", "enterprise"] }
nlp = { path = "../crates/nlp" }
directory = { path = "../crates/directory", features = ["test_mode", "enterprise"] }
jmap = { path = "../crates/jmap", features = ["test_mode", "enterprise"] }
jmap_proto = { path = "../crates/jmap-proto" }
imap = { path = "../crates/imap", features = ["test_mode"] }
imap_proto = { path = "../crates/imap-proto" }
dav = { path = "../crates/dav", features = ["test_mode"] }
dav-proto = { path = "../crates/dav-proto", features = ["test_mode"] }
calcard = { version = "0.1.2", features = ["rkyv"] }
groupware = { path = "../crates/groupware", features = ["test_mode"] }
http = { path = "../crates/http", features = ["test_mode", "enterprise"] }
http_proto = { path = "../crates/http-proto" }
services = { path = "../crates/services", features = ["test_mode", "enterprise"] }
pop3 = { path = "../crates/pop3", features = ["test_mode"] }
smtp = { path = "../crates/smtp", features = ["test_mode", "enterprise"] }
common = { path = "../crates/common", features = ["test_mode", "enterprise"] }
email = { path = "../crates/email", features = ["test_mode", "enterprise"] }
spam-filter = { path = "../crates/spam-filter", features = ["test_mode", "enterprise"] }
migration = { path = "../crates/migration", features = ["test_mode", "enterprise"] }
trc = { path = "../crates/trc" }
managesieve = { path = "../crates/managesieve", features = ["test_mode", "enterprise"] }
smtp-proto = { version = "0.1" }
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
mail-auth = { version = "0.7.1", features = ["test"] }
sieve-rs = { version = "0.7", features = ["rkyv"] } 
utils = { path = "../crates/utils", features = ["test_mode"] }
jmap-client = { version = "0.3", features = ["websockets", "debug", "async"] } 
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] } 
tokio = { version = "1.45", features = ["full"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
rustls-pemfile = "2.0"
rustls-pki-types = { version = "1" }
csv = "1.1"
rayon = { version = "1.5.1" }
flate2 = { version = "1.0.17", features = ["zlib"], default-features = false }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "multipart", "http2"]}
bytes = "1.4.0"
futures = "0.3"
ece = "2.2"
hyper = { version = "1.0.1", features = ["server", "http1", "http2"] }
hyper-util = { version = "0.1.1", features = ["tokio"] }
http-body-util = "0.1.0"
base64 = "0.22"
ahash = { version = "0.8" }
serial_test = "3.0.0"
num_cpus = "1.15.0"
async-trait = "0.1.68"
chrono = "0.4"
ring = { version = "0.17" }
biscuit = "0.7.0"
form_urlencoded = "1.1.0"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"
quick-xml = "0.37.2"


[target.'cfg(not(target_env = "msvc"))'.dependencies]
jemallocator = "0.5.0"
