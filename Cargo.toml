[package]
name = "crabshield"
version = "0.0.1"
edition = "2024"
description = "Crab Shield Security Suite"
authors = ["DanTom"]

[[bin]]
name = "crabshield"
path = "src/main.rs"

[lib]
name = "crabshield"
path = "src/lib.rs"

[dependencies]
# Workspace dependencies
actix-web = "4.11"
actix-cors = "0.7"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "2.0.12"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-actix-web = "0.7"
dotenvy = "0.15"
lettre = { version = "0.11.17", default-features = false, features = ["tokio1", "smtp-transport", "builder"] }


# Configuration
config = "0.15.11"

# Auth-specific dependencies
jsonwebtoken = "9.0"
argon2 = "0.5"
validator = { version = "0.20.0", features = ["derive"] }
redis = { version = "0.32.0", features = ["tokio-comp"] }
reqwest = { version = "0.12", features = ["json"] }
actix-web-httpauth = "0.8"
totp-rs = { version = "5.7.0", features = ["qr"] }
qrcode = { version = "0.14.1", features = ["image"] }
base32 = "0.5.1"
base64 = "0.22.1"
rand = { version = "0.9.1", features = ["std"] }
regex = "1.11.1"
futures-util = "0.3.30"

# OAuth dependencies
oauth2 = "5.0.0"
url = "2.4"

# Email dependencies
resend-rs = "0.15.0"

# Template engine for emails
handlebars = "6.3.2"

# Cryptographic dependencies
sha2 = "0.10"

[dev-dependencies]
actix-rt = "2.9"
actix-test = "0.1"
mockall = "0.13.1"
tokio-test = "0.4"

[lints.clippy]
# Pedantic lints for high code quality
pedantic = "warn"
nursery = "warn"
cargo = "warn"

# Specific lints to allow/deny
unwrap_used = "deny"
expect_used = "warn"
panic = "deny"
todo = "warn"
unimplemented = "deny"
