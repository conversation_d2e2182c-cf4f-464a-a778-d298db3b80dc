[workspace]
members = [
    "crates/crab-shield",
    "crates/crab-dispatch",
    "crates/crab-stash",
    "crates/crab-hub",
    "crates/common",
]

[workspace.dependencies]
actix-web = "4.11.0"
sqlx = "0.8.6"
redis = "0.32.1"
reqwest = "0.12.20"
serde = "1.0.219"
tokio = "1.45.1"
jsonwebtoken = "9.3.1"
uuid = "1.17.0"
chrono = "0.4.41"
log = "0.4.27"
config = "0.15.11"

[profile.release]
opt-level = 3
lto = true