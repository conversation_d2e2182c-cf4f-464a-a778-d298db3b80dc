use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    
    // Role hierarchy and system flags
    pub parent_role_id: Option<Uuid>,
    pub is_system_role: bool,
    pub is_default_role: bool,
    
    // Role configuration (duration in seconds)
    pub max_session_duration_seconds: Option<i32>,
    pub require_mfa: bool,
    
    // Audit fields
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Permission {
    pub id: Uuid,
    pub name: String,
    pub resource: String,
    pub action: String,
    pub description: Option<String>,
    
    // Permission categorization
    pub category: String,
    pub scope: String, // GLOBAL, TENANT, RESOURCE
    
    // System flags
    pub is_system_permission: bool,
    
    // Audit fields
    pub created_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct RolePermission {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    
    // Grant details
    pub granted_at: DateTime<Utc>,
    pub granted_by: Option<Uuid>,
    
    // Conditional permissions
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserRole {
    pub user_id: Uuid,
    pub role_id: Uuid,
    
    // Assignment details
    pub assigned_by: Option<Uuid>,
    pub assigned_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    
    // Status
    pub is_active: bool,
    pub revoked_at: Option<DateTime<Utc>>,
    pub revoked_by: Option<Uuid>,
    pub revoked_reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserPermission {
    pub user_id: Uuid,
    pub permission_id: Uuid,
    
    // Grant type
    pub grant_type: String, // ALLOW, DENY
    
    // Assignment details
    pub assigned_by: Option<Uuid>,
    pub assigned_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    
    // Conditions and context
    pub conditions: Option<serde_json::Value>,
    pub reason: Option<String>,
    
    // Status
    pub is_active: bool,
    pub revoked_at: Option<DateTime<Utc>>,
}

// Request/Response DTOs

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateRoleRequest {
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub parent_role_id: Option<Uuid>,
    pub require_mfa: Option<bool>,
    pub max_session_duration_hours: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRoleRequest {
    pub display_name: Option<String>,
    pub description: Option<String>,
    pub parent_role_id: Option<Uuid>,
    pub require_mfa: Option<bool>,
    pub max_session_duration_hours: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleResponse {
    pub id: Uuid,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub parent_role_id: Option<Uuid>,
    pub parent_role_name: Option<String>,
    pub is_system_role: bool,
    pub is_default_role: bool,
    pub require_mfa: bool,
    pub max_session_duration_hours: Option<i32>,
    pub permissions_count: i32,
    pub users_count: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePermissionRequest {
    pub name: String,
    pub resource: String,
    pub action: String,
    pub description: Option<String>,
    pub category: String,
    pub scope: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionResponse {
    pub id: Uuid,
    pub name: String,
    pub resource: String,
    pub action: String,
    pub description: Option<String>,
    pub category: String,
    pub scope: String,
    pub is_system_permission: bool,
    pub created_at: DateTime<Utc>,
}

impl From<Permission> for PermissionResponse {
    fn from(permission: Permission) -> Self {
        Self {
            id: permission.id,
            name: permission.name,
            resource: permission.resource,
            action: permission.action,
            description: permission.description,
            category: permission.category,
            scope: permission.scope,
            is_system_permission: permission.is_system_permission,
            created_at: permission.created_at,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssignRoleRequest {
    pub user_id: Uuid,
    pub role_id: Uuid,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RevokeRoleRequest {
    pub user_id: Uuid,
    pub role_id: Uuid,
    pub reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssignPermissionRequest {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRoleResponse {
    pub role_id: Uuid,
    pub role_name: String,
    pub role_display_name: String,
    pub assigned_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub is_active: bool,
    pub assigned_by: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPermissionsResponse {
    pub user_id: Uuid,
    pub roles: Vec<UserRoleResponse>,
    pub direct_permissions: Vec<PermissionResponse>,
    pub effective_permissions: Vec<PermissionResponse>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckPermissionRequest {
    pub user_id: Uuid,
    pub permission: String,
    pub resource_id: Option<String>,
    pub context: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckPermissionResponse {
    pub allowed: bool,
    pub reason: String,
    pub matched_permissions: Vec<String>,
    pub matched_roles: Vec<String>,
}
