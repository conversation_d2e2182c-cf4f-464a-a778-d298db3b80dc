use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;


#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MultiAppConfig {
    pub service: ServiceConfig,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub default_tenant: String,
    pub clients: HashMap<String, ClientConfig>,
    pub oauth: OAuthConfig,
    pub email: EmailConfig,
    pub sms: Option<SmsConfig>,
    pub security: SecurityConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ServiceConfig {
    pub mode: ServiceMode,
    pub host: String,
    pub port: u16,
    pub environment: String,
    pub log_level: String,
    pub cors_origins: Vec<String>,
    pub api_prefix: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum ServiceMode {
    #[serde(rename = "standalone")]
    Standalone,
    #[serde(rename = "integrated")]
    Integrated,
}

#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
    pub connection_timeout: Duration,
    pub command_timeout: Duration,
    pub retry_attempts: u32,
    pub retry_delay: Duration,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ClientConfig {
    pub client_id: String,
    pub client_secret: String,
    pub client_name: String,
    pub client_type: ClientType,
    pub tenant_id: String,
    pub allowed_origins: Vec<String>,
    pub redirect_uris: Vec<String>,
    pub oauth_providers: Vec<String>,
    pub features: ClientFeatures,
    pub security: ClientSecurity,
    pub rate_limits: RateLimits,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum ClientType {
    #[serde(rename = "web")]
    Web,
    #[serde(rename = "mobile")]
    Mobile,
    #[serde(rename = "service")]
    Service,
    #[serde(rename = "api")]
    Api,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ClientFeatures {
    pub oauth_enabled: bool,
    pub mfa_required: bool,
    pub sms_totp_enabled: bool,
    pub email_verification_required: bool,
    pub password_reset_enabled: bool,
    pub session_management_enabled: bool,
    pub rbac_enabled: bool,
    pub audit_logging_enabled: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ClientSecurity {
    pub require_https: bool,
    pub session_timeout: Duration,
    pub refresh_token_timeout: Duration,
    pub max_concurrent_sessions: u32,
    pub password_policy: PasswordPolicy,
    pub lockout_policy: LockoutPolicy,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PasswordPolicy {
    pub min_length: u32,
    pub require_uppercase: bool,
    pub require_lowercase: bool,
    pub require_numbers: bool,
    pub require_special_chars: bool,
    pub prevent_reuse_count: u32,
    pub max_age_days: Option<u32>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LockoutPolicy {
    pub max_attempts: u32,
    pub lockout_duration: Duration,
    pub progressive_lockout: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RateLimits {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub login_attempts_per_minute: u32,
    pub password_reset_per_hour: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OAuthConfig {
    pub enabled: bool,
    pub base_url: String,
    pub providers: HashMap<String, OAuthProviderConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OAuthProviderConfig {
    pub client_id: String,
    pub client_secret: String,
    pub enabled: bool,
    pub scopes: Vec<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct EmailConfig {
    pub provider: EmailProvider,
    pub from_address: String,
    pub from_name: String,
    pub reply_to: String,
    pub resend: Option<ResendConfig>,
    pub smtp: Option<SmtpConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum EmailProvider {
    #[serde(rename = "resend")]
    Resend,
    #[serde(rename = "smtp")]
    Smtp,
    #[serde(rename = "internal")]
    Internal,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ResendConfig {
    pub api_key: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SmtpConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub use_tls: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SmsConfig {
    pub provider: SmsProvider,
    pub twilio: Option<TwilioConfig>,
    pub aws_sns: Option<AwsSnsConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum SmsProvider {
    #[serde(rename = "twilio")]
    Twilio,
    #[serde(rename = "aws_sns")]
    AwsSns,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TwilioConfig {
    pub account_sid: String,
    pub auth_token: String,
    pub from_number: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AwsSnsConfig {
    pub access_key_id: String,
    pub secret_access_key: String,
    pub region: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityConfig {
    pub jwt_secret: String,
    pub jwt_expiry: Duration,
    pub refresh_token_expiry: Duration,
    pub encryption_key: String,
    pub cors_max_age: Duration,
    pub rate_limiting_enabled: bool,
    pub audit_logging_enabled: bool,
}

impl Default for ClientFeatures {
    fn default() -> Self {
        Self {
            oauth_enabled: true,
            mfa_required: false,
            sms_totp_enabled: true,
            email_verification_required: true,
            password_reset_enabled: true,
            session_management_enabled: true,
            rbac_enabled: true,
            audit_logging_enabled: true,
        }
    }
}

impl Default for PasswordPolicy {
    fn default() -> Self {
        Self {
            min_length: 8,
            require_uppercase: true,
            require_lowercase: true,
            require_numbers: true,
            require_special_chars: true,
            prevent_reuse_count: 5,
            max_age_days: Some(90),
        }
    }
}

impl Default for LockoutPolicy {
    fn default() -> Self {
        Self {
            max_attempts: 5,
            lockout_duration: Duration::from_secs(900), // 15 minutes
            progressive_lockout: true,
        }
    }
}

impl Default for RateLimits {
    fn default() -> Self {
        Self {
            requests_per_minute: 1000,
            requests_per_hour: 10000,
            login_attempts_per_minute: 10,
            password_reset_per_hour: 5,
        }
    }
}

impl MultiAppConfig {
    pub fn get_client(&self, client_id: &str) -> Option<&ClientConfig> {
        self.clients.get(client_id)
    }

    pub fn get_default_client(&self) -> Option<&ClientConfig> {
        self.clients.values().next()
    }

    pub fn is_standalone_mode(&self) -> bool {
        matches!(self.service.mode, ServiceMode::Standalone)
    }

    pub fn validate(&self) -> Result<(), String> {
        if self.clients.is_empty() {
            return Err("At least one client must be configured".to_string());
        }

        for (client_id, client) in &self.clients {
            if client.client_id != *client_id {
                return Err(format!("Client ID mismatch for {}", client_id));
            }

            if client.client_secret.is_empty() {
                return Err(format!("Client secret is required for {}", client_id));
            }

            if client.allowed_origins.is_empty() {
                return Err(format!("At least one allowed origin is required for {}", client_id));
            }
        }

        Ok(())
    }
}
