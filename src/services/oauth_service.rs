use std::collections::HashMap;
use std::sync::Arc;
use sqlx::PgPool;
use chrono::{Utc, Duration};
use tracing::{info, warn, error, debug};
use uuid::Uuid;

use crate::models::oauth::*;
use crate::config::{OAuthConfig};
use crate::services::AuthService;

pub struct OAuthService {
    pool: PgPool,
    config: OAuthConfig,
    providers: HashMap<String, OAuthProviderType>,
    auth_service: Arc<AuthService>,
}

impl OAuthService {
    pub fn new(pool: PgPool, config: OAuthConfig, auth_service: Arc<AuthService>) -> Self {
        let mut providers: HashMap<String, OAuthProviderType> = HashMap::new();

        // Initialize OAuth providers
        if !config.google.client_id.is_empty() {
            providers.insert("google".to_string(), OAuthProviderType::Google);
        }

        if !config.github.client_id.is_empty() {
            providers.insert("github".to_string(), OAuthProviderType::GitHub);
        }

        if !config.microsoft.client_id.is_empty() {
            providers.insert("microsoft".to_string(), OAuthProviderType::Microsoft);
        }

        Self {
            pool,
            config,
            providers,
            auth_service,
        }
    }

    /// Initiate OAuth flow
    pub async fn initiate_oauth_flow(
        &self,
        request: OAuthInitiateRequest,
    ) -> Result<OAuthInitiateResponse, OAuthError> {
        info!("Initiating OAuth flow for provider: {}", request.provider);

        // Validate provider
        let provider_type = self.providers.get(&request.provider)
            .ok_or_else(|| OAuthError::InvalidProvider(request.provider.clone()))?;

        // Generate state and code verifier for PKCE
        let state = generate_state();
        let code_verifier = generate_code_verifier();

        // Store state in database
        let redirect_uri = request.redirect_uri
            .unwrap_or_else(|| format!("{}/auth/oauth/{}/callback", self.config.base_url, request.provider));

        self.store_oauth_state(&state, Some(&code_verifier), &redirect_uri, &request.provider).await?;

        // Generate authorization URL
        let authorization_url = self.get_authorization_url(provider_type, &state, Some(&code_verifier))?;

        Ok(OAuthInitiateResponse {
            success: true,
            authorization_url,
            state,
            code_verifier: Some(code_verifier),
        })
    }

    /// Handle OAuth callback
    pub async fn handle_oauth_callback(
        &self,
        request: OAuthCallbackRequest,
    ) -> Result<OAuthCallbackResponse, OAuthError> {
        info!("Handling OAuth callback for provider: {}", request.provider);

        // Validate provider
        let provider_type = self.providers.get(&request.provider)
            .ok_or_else(|| OAuthError::InvalidProvider(request.provider.clone()))?;

        // Validate state
        let oauth_state = self.get_oauth_state(&request.state).await?;
        if oauth_state.provider != request.provider {
            return Err(OAuthError::InvalidState);
        }

        // Exchange code for tokens
        let token_response = self.exchange_code_for_tokens(provider_type, &request.code, oauth_state.code_verifier.as_deref()).await?;

        // Get user info from provider
        let user_info = self.get_user_info_from_provider(provider_type, &token_response.access_token).await?;

        // Check if OAuth account already exists
        if let Ok(oauth_account) = self.get_oauth_account_by_provider(&request.provider, &user_info.id).await {
            // Existing OAuth account - update tokens and return user
            self.update_oauth_account_tokens(
                oauth_account.id,
                &token_response.access_token,
                token_response.refresh_token.as_deref(),
                token_response.expires_in,
            ).await?;

            // Clean up state
            self.delete_oauth_state(&request.state).await?;

            return Ok(OAuthCallbackResponse {
                success: true,
                user_id: Some(oauth_account.user_id),
                access_token: Some(token_response.access_token),
                refresh_token: token_response.refresh_token,
                is_new_user: false,
                message: "Successfully authenticated with existing account".to_string(),
            });
        }

        // Check if user exists by email
        let existing_user = if let Some(email) = &user_info.email {
            self.auth_service.get_user_by_email(email).await.unwrap_or_else(|_| None)
        } else {
            None
        };

        let (user_id, is_new_user) = if let Some(user) = existing_user {
            // Link OAuth account to existing user
            (user.id, false)
        } else {
            // Create new user
            let email = user_info.email.clone().ok_or_else(|| OAuthError::ProviderError("Email not provided by OAuth provider".to_string()))?;
            let username = format!("{}_{}", request.provider, &user_info.id);

            let create_user_request = crate::models::CreateUserRequest {
                username,
                email: email.clone(),
                password: generate_random_password(), // Generate random password for OAuth users
                first_name: user_info.name.clone(),
                last_name: None, // OAuth providers typically don't separate first/last names
            };

            let user = self.auth_service.create_user(create_user_request).await
                .map_err(|e| OAuthError::ProviderError(format!("Failed to create user: {}", e)))?;

            (user.id, true)
        };

        // Create OAuth account
        let oauth_account = self.create_oauth_account(
            user_id,
            &request.provider,
            &user_info.id,
            user_info.email.as_deref(),
            &token_response.access_token,
            token_response.refresh_token.as_deref(),
            token_response.expires_in,
        ).await?;

        // Clean up state
        self.delete_oauth_state(&request.state).await?;

        Ok(OAuthCallbackResponse {
            success: true,
            user_id: Some(user_id),
            access_token: Some(token_response.access_token),
            refresh_token: token_response.refresh_token,
            is_new_user,
            message: if is_new_user {
                "Successfully created new account and authenticated".to_string()
            } else {
                "Successfully linked OAuth account and authenticated".to_string()
            },
        })
    }

    /// Link OAuth account to existing user
    pub async fn link_oauth_account(
        &self,
        request: OAuthLinkRequest,
    ) -> Result<OAuthLinkResponse, OAuthError> {
        info!("Linking OAuth account for provider: {} to user: {}", request.provider, request.user_id);

        // Validate provider
        let provider_type = self.providers.get(&request.provider)
            .ok_or_else(|| OAuthError::InvalidProvider(request.provider.clone()))?;

        // Validate state
        let oauth_state = self.get_oauth_state(&request.state).await?;
        if oauth_state.provider != request.provider {
            return Err(OAuthError::InvalidState);
        }

        // Exchange code for tokens
        let token_response = self.exchange_code_for_tokens(provider_type, &request.code, oauth_state.code_verifier.as_deref()).await?;

        // Get user info from provider
        let user_info = self.get_user_info_from_provider(provider_type, &token_response.access_token).await?;

        // Check if OAuth account already exists
        if self.get_oauth_account_by_provider(&request.provider, &user_info.id).await.is_ok() {
            return Err(OAuthError::AccountAlreadyLinked);
        }

        // Create OAuth account
        let oauth_account = self.create_oauth_account(
            request.user_id,
            &request.provider,
            &user_info.id,
            user_info.email.as_deref(),
            &token_response.access_token,
            token_response.refresh_token.as_deref(),
            token_response.expires_in,
        ).await?;

        // Clean up state
        self.delete_oauth_state(&request.state).await?;

        Ok(OAuthLinkResponse {
            success: true,
            message: "OAuth account successfully linked".to_string(),
            oauth_account_id: Some(oauth_account.id),
        })
    }

    /// Unlink OAuth account
    pub async fn unlink_oauth_account(
        &self,
        request: OAuthUnlinkRequest,
    ) -> Result<OAuthUnlinkResponse, OAuthError> {
        info!("Unlinking OAuth account for provider: {} from user: {}", request.provider, request.user_id);

        // Find and delete OAuth account
        let result = sqlx::query(
            "DELETE FROM oauth_accounts WHERE user_id = $1 AND provider = $2"
        )
        .bind(request.user_id)
        .bind(&request.provider)
        .execute(&self.pool)
        .await?;

        if result.rows_affected() == 0 {
            return Err(OAuthError::AccountNotFound);
        }

        Ok(OAuthUnlinkResponse {
            success: true,
            message: "OAuth account successfully unlinked".to_string(),
        })
    }

    /// Get available OAuth providers
    pub async fn get_oauth_providers(&self) -> Result<OAuthProvidersResponse, OAuthError> {
        let providers = self.providers.iter().map(|(name, _provider_type)| {
            OAuthProviderInfo {
                name: name.clone(),
                display_name: match name.as_str() {
                    "google" => "Google".to_string(),
                    "github" => "GitHub".to_string(),
                    "microsoft" => "Microsoft".to_string(),
                    "apple" => "Apple".to_string(),
                    _ => name.clone(),
                },
                enabled: true,
                authorization_url: format!("{}/auth/oauth/{}", self.config.base_url, name),
            }
        }).collect();

        Ok(OAuthProvidersResponse {
            success: true,
            providers,
        })
    }

    // Private helper methods
    async fn store_oauth_state(
        &self,
        state: &str,
        code_verifier: Option<&str>,
        redirect_uri: &str,
        provider: &str,
    ) -> Result<(), OAuthError> {
        let expires_at = Utc::now() + Duration::minutes(10); // 10-minute expiration

        sqlx::query(
            "INSERT INTO oauth_states (state, code_verifier, redirect_uri, provider, expires_at) VALUES ($1, $2, $3, $4, $5)"
        )
        .bind(state)
        .bind(code_verifier)
        .bind(redirect_uri)
        .bind(provider)
        .bind(expires_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn get_oauth_state(&self, state: &str) -> Result<OAuthState, OAuthError> {
        let row = sqlx::query_as::<_, OAuthState>(
            "SELECT id, state, code_verifier, redirect_uri, provider, expires_at, created_at FROM oauth_states WHERE state = $1 AND expires_at > NOW()"
        )
        .bind(state)
        .fetch_one(&self.pool)
        .await
        .map_err(|_| OAuthError::InvalidState)?;

        Ok(row)
    }

    async fn delete_oauth_state(&self, state: &str) -> Result<(), OAuthError> {
        sqlx::query("DELETE FROM oauth_states WHERE state = $1")
            .bind(state)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    async fn get_oauth_account_by_provider(
        &self,
        provider: &str,
        provider_user_id: &str,
    ) -> Result<OAuthAccount, OAuthError> {
        let row = sqlx::query_as::<_, OAuthAccount>(
            "SELECT id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at FROM oauth_accounts WHERE provider = $1 AND provider_user_id = $2"
        )
        .bind(provider)
        .bind(provider_user_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(row)
    }

    async fn create_oauth_account(
        &self,
        user_id: Uuid,
        provider: &str,
        provider_user_id: &str,
        email: Option<&str>,
        access_token: &str,
        refresh_token: Option<&str>,
        expires_in: Option<i64>,
    ) -> Result<OAuthAccount, OAuthError> {
        let expires_at = expires_in.map(|exp| Utc::now() + Duration::seconds(exp));

        let row = sqlx::query_as::<_, OAuthAccount>(
            r#"
            INSERT INTO oauth_accounts (user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at
            "#
        )
        .bind(user_id)
        .bind(provider)
        .bind(provider_user_id)
        .bind(email)
        .bind(access_token)
        .bind(refresh_token)
        .bind(expires_at)
        .fetch_one(&self.pool)
        .await?;

        Ok(row)
    }

    async fn update_oauth_account_tokens(
        &self,
        oauth_account_id: Uuid,
        access_token: &str,
        refresh_token: Option<&str>,
        expires_in: Option<i64>,
    ) -> Result<(), OAuthError> {
        let expires_at = expires_in.map(|exp| Utc::now() + Duration::seconds(exp));

        sqlx::query(
            "UPDATE oauth_accounts SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = NOW() WHERE id = $4"
        )
        .bind(access_token)
        .bind(refresh_token)
        .bind(expires_at)
        .bind(oauth_account_id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // Helper methods for OAuth provider operations
    fn get_authorization_url(
        &self,
        provider_type: &OAuthProviderType,
        state: &str,
        code_verifier: Option<&str>,
    ) -> Result<String, OAuthError> {
        match provider_type {
            OAuthProviderType::Google => self.get_google_auth_url(state, code_verifier),
            OAuthProviderType::GitHub => self.get_github_auth_url(state, code_verifier),
            OAuthProviderType::Microsoft => self.get_microsoft_auth_url(state, code_verifier),
            OAuthProviderType::Apple => Err(OAuthError::ProviderError("Apple OAuth not yet implemented".to_string())),
        }
    }

    async fn exchange_code_for_tokens(
        &self,
        provider_type: &OAuthProviderType,
        code: &str,
        code_verifier: Option<&str>,
    ) -> Result<OAuthTokenResponse, OAuthError> {
        match provider_type {
            OAuthProviderType::Google => self.exchange_google_code(code, code_verifier).await,
            OAuthProviderType::GitHub => self.exchange_github_code(code, code_verifier).await,
            OAuthProviderType::Microsoft => self.exchange_microsoft_code(code, code_verifier).await,
            OAuthProviderType::Apple => Err(OAuthError::ProviderError("Apple OAuth not yet implemented".to_string())),
        }
    }

    async fn get_user_info_from_provider(
        &self,
        provider_type: &OAuthProviderType,
        access_token: &str,
    ) -> Result<OAuthUserInfo, OAuthError> {
        match provider_type {
            OAuthProviderType::Google => self.get_google_user_info(access_token).await,
            OAuthProviderType::GitHub => self.get_github_user_info(access_token).await,
            OAuthProviderType::Microsoft => self.get_microsoft_user_info(access_token).await,
            OAuthProviderType::Apple => Err(OAuthError::ProviderError("Apple OAuth not yet implemented".to_string())),
        }
    }
}

// Helper function to generate random password for OAuth users
fn generate_random_password() -> String {
    use base64::Engine;
    let mut bytes = [0u8; 32];
    rand::Rng::fill(&mut rand::rng(), &mut bytes);
    base64::engine::general_purpose::STANDARD.encode(bytes)
}

// OAuth Provider Implementations
use oauth2::{
    AuthUrl, ClientId, ClientSecret, CsrfToken, PkceCodeChallenge, PkceCodeVerifier,
     Scope, TokenUrl, AuthorizationCode, TokenResponse,
};
use oauth2::basic::BasicClient;
use oauth2::reqwest;

impl OAuthService {
    // Google OAuth implementation
    fn get_google_auth_url(&self, state: &str, code_verifier: Option<&str>) -> Result<String, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.google.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.google.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string())?)
            .set_token_uri(TokenUrl::new("https://www.googleapis.com/oauth2/v4/token".to_string())?);

        let mut auth_request = client
            .authorize_url(|| CsrfToken::new(state.to_string()))
            .add_scope(Scope::new("openid".to_string()))
            .add_scope(Scope::new("email".to_string()))
            .add_scope(Scope::new("profile".to_string()));

        if let Some(verifier) = code_verifier {
            let pkce_verifier = PkceCodeVerifier::new(verifier.to_string());
            let pkce_challenge = PkceCodeChallenge::from_code_verifier_sha256(&pkce_verifier);
            auth_request = auth_request.set_pkce_challenge(pkce_challenge);
        }

        let (auth_url, _) = auth_request.url();
        Ok(auth_url.to_string())
    }

    async fn exchange_google_code(&self, code: &str, code_verifier: Option<&str>) -> Result<OAuthTokenResponse, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.google.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.google.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string())?)
            .set_token_uri(TokenUrl::new("https://www.googleapis.com/oauth2/v4/token".to_string())?);

        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));

        if let Some(verifier) = code_verifier {
            token_request = token_request.set_pkce_verifier(PkceCodeVerifier::new(verifier.to_string()));
        }

        let http_client = reqwest::Client::new();
        let token_response = token_request
            .request_async(&http_client)
            .await
            .map_err(|e| OAuthError::TokenExchangeFailed(e.to_string()))?;

        Ok(OAuthTokenResponse {
            access_token: token_response.access_token().secret().clone(),
            refresh_token: token_response.refresh_token().map(|t| t.secret().clone()),
            expires_in: token_response.expires_in().map(|d| d.as_secs() as i64),
            token_type: "Bearer".to_string(),
            scope: token_response.scopes().map(|scopes| {
                scopes.iter().map(|s| s.as_str()).collect::<Vec<_>>().join(" ")
            }),
        })
    }

    async fn get_google_user_info(&self, access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        let client = reqwest::Client::new();
        let response = client
            .get("https://www.googleapis.com/oauth2/v2/userinfo")
            .bearer_auth(access_token)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(OAuthError::UserInfoFailed(format!("HTTP {}", response.status())));
        }

        let user_data: serde_json::Value = response.json().await?;

        Ok(OAuthUserInfo {
            id: user_data["id"].as_str().unwrap_or_default().to_string(),
            email: user_data["email"].as_str().map(|s| s.to_string()),
            name: user_data["name"].as_str().map(|s| s.to_string()),
            picture: user_data["picture"].as_str().map(|s| s.to_string()),
            verified_email: user_data["verified_email"].as_bool(),
        })
    }

    // GitHub OAuth implementation
    fn get_github_auth_url(&self, state: &str, _code_verifier: Option<&str>) -> Result<String, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.github.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.github.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://github.com/login/oauth/authorize".to_string())?)
            .set_token_uri(TokenUrl::new("https://github.com/login/oauth/access_token".to_string())?);

        let (auth_url, _) = client
            .authorize_url(|| CsrfToken::new(state.to_string()))
            .add_scope(Scope::new("user:email".to_string()))
            .url();

        Ok(auth_url.to_string())
    }

    async fn exchange_github_code(&self, code: &str, _code_verifier: Option<&str>) -> Result<OAuthTokenResponse, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.github.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.github.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://github.com/login/oauth/authorize".to_string())?)
            .set_token_uri(TokenUrl::new("https://github.com/login/oauth/access_token".to_string())?);

        let http_client = reqwest::Client::new();
        let token_response = client
            .exchange_code(AuthorizationCode::new(code.to_string()))
            .request_async(&http_client)
            .await
            .map_err(|e| OAuthError::TokenExchangeFailed(e.to_string()))?;

        Ok(OAuthTokenResponse {
            access_token: token_response.access_token().secret().clone(),
            refresh_token: token_response.refresh_token().map(|t| t.secret().clone()),
            expires_in: token_response.expires_in().map(|d| d.as_secs() as i64),
            token_type: "Bearer".to_string(),
            scope: token_response.scopes().map(|scopes| {
                scopes.iter().map(|s| s.as_str()).collect::<Vec<_>>().join(" ")
            }),
        })
    }

    async fn get_github_user_info(&self, access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        let client = reqwest::Client::new();

        // Get user info
        let user_response = client
            .get("https://api.github.com/user")
            .bearer_auth(access_token)
            .header("User-Agent", "CrabShield-Auth-Service")
            .send()
            .await?;

        if !user_response.status().is_success() {
            return Err(OAuthError::UserInfoFailed(format!("HTTP {}", user_response.status())));
        }

        let user_data: serde_json::Value = user_response.json().await?;

        // Get user emails
        let emails_response = client
            .get("https://api.github.com/user/emails")
            .bearer_auth(access_token)
            .header("User-Agent", "CrabShield-Auth-Service")
            .send()
            .await?;

        let primary_email = if emails_response.status().is_success() {
            let emails: Vec<serde_json::Value> = emails_response.json().await?;
            emails.iter()
                .find(|email| email["primary"].as_bool().unwrap_or(false))
                .and_then(|email| email["email"].as_str())
                .map(|s| s.to_string())
        } else {
            None
        };

        Ok(OAuthUserInfo {
            id: user_data["id"].as_u64().unwrap_or_default().to_string(),
            email: primary_email,
            name: user_data["name"].as_str().map(|s| s.to_string()),
            picture: user_data["avatar_url"].as_str().map(|s| s.to_string()),
            verified_email: Some(true), // GitHub emails are considered verified
        })
    }

    // Microsoft OAuth implementation
    fn get_microsoft_auth_url(&self, state: &str, code_verifier: Option<&str>) -> Result<String, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.microsoft.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.microsoft.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://login.microsoftonline.com/common/oauth2/v2.0/authorize".to_string())?)
            .set_token_uri(TokenUrl::new("https://login.microsoftonline.com/common/oauth2/v2.0/token".to_string())?);

        let mut auth_request = client
            .authorize_url(|| CsrfToken::new(state.to_string()))
            .add_scope(Scope::new("openid".to_string()))
            .add_scope(Scope::new("email".to_string()))
            .add_scope(Scope::new("profile".to_string()));

        if let Some(verifier) = code_verifier {
            let pkce_verifier = PkceCodeVerifier::new(verifier.to_string());
            let pkce_challenge = PkceCodeChallenge::from_code_verifier_sha256(&pkce_verifier);
            auth_request = auth_request.set_pkce_challenge(pkce_challenge);
        }

        let (auth_url, _) = auth_request.url();
        Ok(auth_url.to_string())
    }

    async fn exchange_microsoft_code(&self, code: &str, code_verifier: Option<&str>) -> Result<OAuthTokenResponse, OAuthError> {
        let client = BasicClient::new(ClientId::new(self.config.microsoft.client_id.clone()))
            .set_client_secret(ClientSecret::new(self.config.microsoft.client_secret.clone()))
            .set_auth_uri(AuthUrl::new("https://login.microsoftonline.com/common/oauth2/v2.0/authorize".to_string())?)
            .set_token_uri(TokenUrl::new("https://login.microsoftonline.com/common/oauth2/v2.0/token".to_string())?);

        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));

        if let Some(verifier) = code_verifier {
            token_request = token_request.set_pkce_verifier(PkceCodeVerifier::new(verifier.to_string()));
        }

        let http_client = reqwest::Client::new();
        let token_response = token_request
            .request_async(&http_client)
            .await
            .map_err(|e| OAuthError::TokenExchangeFailed(e.to_string()))?;

        Ok(OAuthTokenResponse {
            access_token: token_response.access_token().secret().clone(),
            refresh_token: token_response.refresh_token().map(|t| t.secret().clone()),
            expires_in: token_response.expires_in().map(|d| d.as_secs() as i64),
            token_type: "Bearer".to_string(),
            scope: token_response.scopes().map(|scopes| {
                scopes.iter().map(|s| s.as_str()).collect::<Vec<_>>().join(" ")
            }),
        })
    }

    async fn get_microsoft_user_info(&self, access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        let client = reqwest::Client::new();
        let response = client
            .get("https://graph.microsoft.com/v1.0/me")
            .bearer_auth(access_token)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(OAuthError::UserInfoFailed(format!("HTTP {}", response.status())));
        }

        let user_data: serde_json::Value = response.json().await?;

        Ok(OAuthUserInfo {
            id: user_data["id"].as_str().unwrap_or_default().to_string(),
            email: user_data["mail"].as_str()
                .or_else(|| user_data["userPrincipalName"].as_str())
                .map(|s| s.to_string()),
            name: user_data["displayName"].as_str().map(|s| s.to_string()),
            picture: None, // Microsoft Graph requires additional API call for photo
            verified_email: Some(true), // Microsoft emails are considered verified
        })
    }
}
