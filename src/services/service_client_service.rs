use sqlx::PgPool;
use uuid::Uuid;
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier};
use argon2::password_hash::{rand_core::OsRng, SaltString};
use crate::models::{
    ServiceClient, ServiceClientResponse, ServiceClientWithSecret,
    CreateServiceClientRequest, UpdateServiceClientRequest,
    ClientListRequest, ClientListResponse, ClientContext, ClientFeatures,
    ClientSecuritySettings, RateLimits
};
use crate::utils::errors::AppError;
use crate::utils::crypto::generate_secure_token;
use tracing::{info, warn, error, instrument};

pub struct ServiceClientService {
    db_pool: PgPool,
}

impl ServiceClientService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    #[instrument(skip(self, request))]
    pub async fn create_client(
        &self,
        request: CreateServiceClientRequest,
        created_by: Option<Uuid>,
    ) -> Result<ServiceClientWithSecret, AppError> {
        // Validate tenant exists
        let tenant_exists = sqlx::query_scalar::<_, bool>(
            "SELECT EXISTS(SELECT 1 FROM tenants WHERE id = $1 AND is_active = true)"
        )
        .bind(request.tenant_id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        if !tenant_exists {
            return Err(AppError::BadRequest("Invalid tenant_id".to_string()));
        }

        // Generate client_id and client_secret
        let client_id = format!("{}_{}", 
            request.client_type.to_string().to_lowercase(),
            generate_secure_token(16)
        );
        let client_secret = generate_secure_token(32);

        // Hash the client secret
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let client_secret_hash = argon2
            .hash_password(client_secret.as_bytes(), &salt)
            .map_err(|e| AppError::InternalServerError(format!("Failed to hash client secret: {}", e)))?
            .to_string();

        let features = request.features.unwrap_or_default();
        let security_settings = request.security_settings.unwrap_or_default();
        let rate_limits = request.rate_limits.unwrap_or_default();

        let features_json = serde_json::to_value(&features)
            .map_err(|e| AppError::InternalServerError(format!("Failed to serialize features: {}", e)))?;
        let security_json = serde_json::to_value(&security_settings)
            .map_err(|e| AppError::InternalServerError(format!("Failed to serialize security settings: {}", e)))?;

        let client = sqlx::query_as::<_, ServiceClient>(
            r#"
            INSERT INTO service_clients (
                client_id, client_secret_hash, client_name, client_type, tenant_id,
                allowed_origins, redirect_uris, oauth_providers, scopes,
                features, security_settings, is_development,
                rate_limit_requests_per_minute, rate_limit_requests_per_hour,
                created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            RETURNING id, client_id, client_secret_hash, client_name, client_type,
                     tenant_id, allowed_origins, redirect_uris, oauth_providers, scopes,
                     features, security_settings, is_active, is_development,
                     rate_limit_requests_per_minute, rate_limit_requests_per_hour,
                     created_at, updated_at, created_by, updated_by, last_used_at
            "#,
        )
        .bind(&client_id)
        .bind(&client_secret_hash)
        .bind(&request.client_name)
        .bind(request.client_type.to_string())
        .bind(request.tenant_id)
        .bind(&request.allowed_origins)
        .bind(&request.redirect_uris)
        .bind(&request.oauth_providers)
        .bind(request.scopes.unwrap_or_default())
        .bind(&features_json)
        .bind(&security_json)
        .bind(request.is_development.unwrap_or(false))
        .bind(rate_limits.requests_per_minute as i32)
        .bind(rate_limits.requests_per_hour as i32)
        .bind(created_by)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        info!(
            client_id = %client.client_id,
            client_name = %client.client_name,
            tenant_id = %client.tenant_id,
            "Service client created successfully"
        );

        Ok(ServiceClientWithSecret {
            client: ServiceClientResponse::from(client),
            client_secret,
        })
    }

    #[instrument(skip(self))]
    pub async fn get_client(&self, client_id: &str) -> Result<ServiceClientResponse, AppError> {
        let client = sqlx::query_as::<_, ServiceClient>(
            r#"
            SELECT sc.id, sc.client_id, sc.client_secret_hash, sc.client_name, sc.client_type,
                   sc.tenant_id, sc.allowed_origins, sc.redirect_uris, sc.oauth_providers, sc.scopes,
                   sc.features, sc.security_settings, sc.is_active, sc.is_development,
                   sc.rate_limit_requests_per_minute, sc.rate_limit_requests_per_hour,
                   sc.created_at, sc.updated_at, sc.created_by, sc.updated_by, sc.last_used_at
            FROM service_clients sc
            WHERE sc.client_id = $1
            "#,
        )
        .bind(client_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match client {
            Some(client) => {
                let mut response = ServiceClientResponse::from(client);
                
                // Get tenant name
                if let Ok(tenant_name) = sqlx::query_scalar::<_, String>(
                    "SELECT name FROM tenants WHERE id = $1"
                )
                .bind(response.tenant_id)
                .fetch_optional(&self.db_pool)
                .await
                {
                    response.tenant_name = tenant_name;
                }

                Ok(response)
            }
            None => Err(AppError::NotFound("Service client not found".to_string())),
        }
    }

    #[instrument(skip(self))]
    pub async fn get_client_by_id(&self, id: Uuid) -> Result<ServiceClientResponse, AppError> {
        let client = sqlx::query_as::<_, ServiceClient>(
            r#"
            SELECT id, client_id, client_secret_hash, client_name, client_type,
                   tenant_id, allowed_origins, redirect_uris, oauth_providers, scopes,
                   features, security_settings, is_active, is_development,
                   rate_limit_requests_per_minute, rate_limit_requests_per_hour,
                   created_at, updated_at, created_by, updated_by, last_used_at
            FROM service_clients
            WHERE id = $1
            "#,
        )
        .bind(id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match client {
            Some(client) => Ok(ServiceClientResponse::from(client)),
            None => Err(AppError::NotFound("Service client not found".to_string())),
        }
    }

    #[instrument(skip(self, request))]
    pub async fn update_client(
        &self,
        client_id: &str,
        request: UpdateServiceClientRequest,
        updated_by: Option<Uuid>,
    ) -> Result<ServiceClientResponse, AppError> {
        // Check if client exists
        let existing_client = self.get_client(client_id).await?;

        let features_json = request.features
            .map(|f| serde_json::to_value(&f).unwrap())
            .unwrap_or_else(|| serde_json::to_value(&existing_client.features).unwrap());

        let security_json = request.security_settings
            .map(|s| serde_json::to_value(&s).unwrap())
            .unwrap_or_else(|| serde_json::to_value(&existing_client.security_settings).unwrap());

        let rate_limits = request.rate_limits.unwrap_or(existing_client.rate_limits);

        let client = sqlx::query_as::<_, ServiceClient>(
            r#"
            UPDATE service_clients SET
                client_name = COALESCE($2, client_name),
                allowed_origins = COALESCE($3, allowed_origins),
                redirect_uris = COALESCE($4, redirect_uris),
                oauth_providers = COALESCE($5, oauth_providers),
                scopes = COALESCE($6, scopes),
                features = COALESCE($7, features),
                security_settings = COALESCE($8, security_settings),
                is_active = COALESCE($9, is_active),
                is_development = COALESCE($10, is_development),
                rate_limit_requests_per_minute = $11,
                rate_limit_requests_per_hour = $12,
                updated_at = NOW(),
                updated_by = $13
            WHERE client_id = $1
            RETURNING id, client_id, client_secret_hash, client_name, client_type,
                     tenant_id, allowed_origins, redirect_uris, oauth_providers, scopes,
                     features, security_settings, is_active, is_development,
                     rate_limit_requests_per_minute, rate_limit_requests_per_hour,
                     created_at, updated_at, created_by, updated_by, last_used_at
            "#,
        )
        .bind(client_id)
        .bind(request.client_name)
        .bind(request.allowed_origins)
        .bind(request.redirect_uris)
        .bind(request.oauth_providers)
        .bind(request.scopes)
        .bind(&features_json)
        .bind(&security_json)
        .bind(request.is_active)
        .bind(request.is_development)
        .bind(rate_limits.requests_per_minute as i32)
        .bind(rate_limits.requests_per_hour as i32)
        .bind(updated_by)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        info!(
            client_id = %client.client_id,
            client_name = %client.client_name,
            "Service client updated successfully"
        );

        Ok(ServiceClientResponse::from(client))
    }

    #[instrument(skip(self))]
    pub async fn delete_client(&self, client_id: &str) -> Result<(), AppError> {
        let rows_affected = sqlx::query("DELETE FROM service_clients WHERE client_id = $1")
            .bind(client_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?
            .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::NotFound("Service client not found".to_string()));
        }

        info!(client_id = %client_id, "Service client deleted successfully");
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_clients(
        &self,
        request: ClientListRequest,
    ) -> Result<ClientListResponse, AppError> {
        let page = request.page.unwrap_or(1);
        let limit = request.limit.unwrap_or(20).min(100); // Max 100 per page
        let offset = (page - 1) * limit;

        let mut query = String::from(
            r#"
            SELECT sc.id, sc.client_id, sc.client_secret_hash, sc.client_name, sc.client_type,
                   sc.tenant_id, sc.allowed_origins, sc.redirect_uris, sc.oauth_providers, sc.scopes,
                   sc.features, sc.security_settings, sc.is_active, sc.is_development,
                   sc.rate_limit_requests_per_minute, sc.rate_limit_requests_per_hour,
                   sc.created_at, sc.updated_at, sc.created_by, sc.updated_by, sc.last_used_at
            FROM service_clients sc
            WHERE 1=1
            "#
        );

        let mut count_query = String::from("SELECT COUNT(*) FROM service_clients sc WHERE 1=1");
        let mut bind_count = 0;

        // Add filters
        if let Some(_tenant_id) = request.tenant_id {
            bind_count += 1;
            query.push_str(&format!(" AND sc.tenant_id = ${}", bind_count));
            count_query.push_str(&format!(" AND sc.tenant_id = ${}", bind_count));
        }

        if let Some(ref _client_type) = request.client_type {
            bind_count += 1;
            query.push_str(&format!(" AND sc.client_type = ${}", bind_count));
            count_query.push_str(&format!(" AND sc.client_type = ${}", bind_count));
        }

        if let Some(_is_active) = request.is_active {
            bind_count += 1;
            query.push_str(&format!(" AND sc.is_active = ${}", bind_count));
            count_query.push_str(&format!(" AND sc.is_active = ${}", bind_count));
        }

        if let Some(_is_development) = request.is_development {
            bind_count += 1;
            query.push_str(&format!(" AND sc.is_development = ${}", bind_count));
            count_query.push_str(&format!(" AND sc.is_development = ${}", bind_count));
        }

        if let Some(ref _search) = request.search {
            bind_count += 1;
            query.push_str(&format!(" AND (sc.client_name ILIKE ${} OR sc.client_id ILIKE ${})", bind_count, bind_count));
            count_query.push_str(&format!(" AND (sc.client_name ILIKE ${} OR sc.client_id ILIKE ${})", bind_count, bind_count));
        }

        query.push_str(" ORDER BY sc.created_at DESC");
        query.push_str(&format!(" LIMIT ${} OFFSET ${}", bind_count + 1, bind_count + 2));

        // Execute count query
        let mut count_query_builder = sqlx::query_scalar::<_, i64>(&count_query);
        let search_pattern = request.search.as_ref().map(|s| format!("%{}%", s));

        if let Some(tenant_id) = request.tenant_id {
            count_query_builder = count_query_builder.bind(tenant_id);
        }
        if let Some(ref client_type) = request.client_type {
            count_query_builder = count_query_builder.bind(client_type.to_string());
        }
        if let Some(is_active) = request.is_active {
            count_query_builder = count_query_builder.bind(is_active);
        }
        if let Some(is_development) = request.is_development {
            count_query_builder = count_query_builder.bind(is_development);
        }
        if let Some(ref pattern) = search_pattern {
            count_query_builder = count_query_builder.bind(pattern);
        }

        let total = count_query_builder
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))? as u64;

        // Execute main query
        let mut query_builder = sqlx::query_as::<_, ServiceClient>(&query);
        if let Some(tenant_id) = request.tenant_id {
            query_builder = query_builder.bind(tenant_id);
        }
        if let Some(ref client_type) = request.client_type {
            query_builder = query_builder.bind(client_type.to_string());
        }
        if let Some(is_active) = request.is_active {
            query_builder = query_builder.bind(is_active);
        }
        if let Some(is_development) = request.is_development {
            query_builder = query_builder.bind(is_development);
        }
        if let Some(ref pattern) = search_pattern {
            query_builder = query_builder.bind(pattern);
        }
        query_builder = query_builder.bind(limit as i64).bind(offset as i64);

        let clients = query_builder
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        let client_responses: Vec<ServiceClientResponse> = clients
            .into_iter()
            .map(ServiceClientResponse::from)
            .collect();

        let total_pages = (total + limit as u64 - 1) / limit as u64;

        Ok(ClientListResponse {
            clients: client_responses,
            total,
            page,
            limit,
            total_pages: total_pages as u32,
        })
    }

    #[instrument(skip(self, client_secret))]
    pub async fn verify_client_credentials(
        &self,
        client_id: &str,
        client_secret: &str,
    ) -> Result<ClientContext, AppError> {
        let client = sqlx::query_as::<_, ServiceClient>(
            r#"
            SELECT id, client_id, client_secret_hash, client_name, client_type,
                   tenant_id, allowed_origins, redirect_uris, oauth_providers, scopes,
                   features, security_settings, is_active, is_development,
                   rate_limit_requests_per_minute, rate_limit_requests_per_hour,
                   created_at, updated_at, created_by, updated_by, last_used_at
            FROM service_clients
            WHERE client_id = $1 AND is_active = true
            "#,
        )
        .bind(client_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        match client {
            Some(client) => {
                // Verify client secret
                let parsed_hash = PasswordHash::new(&client.client_secret_hash)
                    .map_err(|e| AppError::InternalServerError(format!("Invalid password hash: {}", e)))?;

                let argon2 = Argon2::default();
                if argon2.verify_password(client_secret.as_bytes(), &parsed_hash).is_ok() {
                    // Update last_used_at
                    let _ = sqlx::query(
                        "UPDATE service_clients SET last_used_at = NOW() WHERE id = $1"
                    )
                    .bind(client.id)
                    .execute(&self.db_pool)
                    .await;

                    Ok(ClientContext::from(client))
                } else {
                    Err(AppError::Unauthorized("Invalid client credentials".to_string()))
                }
            }
            None => Err(AppError::Unauthorized("Invalid client credentials".to_string())),
        }
    }

    #[instrument(skip(self))]
    pub async fn regenerate_client_secret(
        &self,
        client_id: &str,
        updated_by: Option<Uuid>,
    ) -> Result<String, AppError> {
        // Generate new client secret
        let new_client_secret = generate_secure_token(32);

        // Hash the new client secret
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let client_secret_hash = argon2
            .hash_password(new_client_secret.as_bytes(), &salt)
            .map_err(|e| AppError::InternalServerError(format!("Failed to hash client secret: {}", e)))?
            .to_string();

        let rows_affected = sqlx::query(
            r#"
            UPDATE service_clients SET
                client_secret_hash = $2,
                updated_at = NOW(),
                updated_by = $3
            WHERE client_id = $1
            "#,
        )
        .bind(client_id)
        .bind(&client_secret_hash)
        .bind(updated_by)
        .execute(&self.db_pool)
        .await
        .map_err(|e| AppError::DatabaseError(e.to_string()))?
        .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::NotFound("Service client not found".to_string()));
        }

        info!(
            client_id = %client_id,
            "Client secret regenerated successfully"
        );

        Ok(new_client_secret)
    }
}
