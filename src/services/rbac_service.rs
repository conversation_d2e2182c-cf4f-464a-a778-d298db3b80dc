use crate::models::{Role, Permission};
use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use redis::{Client as RedisClient, Commands, Connection};
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, Row};
use std::collections::{HashMap, HashSet};
use thiserror::Error;
use tracing::{error, info, warn};
use uuid::Uuid;
use validator::Validate;

#[derive(Error, Debug)]
pub enum RbacError {
    #[error("Database error: {0}")]
    DatabaseError(String),
    
    #[error("Redis error: {0}")]
    RedisError(String),
    
    #[error("Role not found: {0}")]
    RoleNotFound(String),
    
    #[error("Permission not found: {0}")]
    PermissionNotFound(String),
    
    #[error("User not found")]
    UserNotFound,
    
    #[error("Role already exists: {0}")]
    RoleAlreadyExists(String),
    
    #[error("Permission already exists: {0}")]
    PermissionAlreadyExists(String),
    
    #[error("User already has role: {0}")]
    UserAlreadyHasRole(String),
    
    #[error("Role already has permission: {0}")]
    RoleAlreadyHasPermission(String),
    
    #[error("Circular dependency detected")]
    CircularDependency,
    
    #[error("Access denied: insufficient permissions")]
    AccessDenied,
    
    #[error("Validation error: {0}")]
    ValidationError(String),
    
    #[error("Internal error: {0}")]
    InternalError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateRoleRequest {
    #[validate(length(min = 1, max = 100, message = "Role name must be between 1 and 100 characters"))]
    pub name: String,
    
    #[validate(length(max = 500, message = "Description cannot exceed 500 characters"))]
    pub description: Option<String>,
    
    pub parent_role_id: Option<Uuid>,
    pub is_system_role: bool,
    pub permissions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreatePermissionRequest {
    #[validate(length(min = 1, max = 100, message = "Permission name must be between 1 and 100 characters"))]
    pub name: String,
    
    #[validate(length(max = 500, message = "Description cannot exceed 500 characters"))]
    pub description: Option<String>,
    
    #[validate(length(min = 1, max = 100, message = "Resource must be between 1 and 100 characters"))]
    pub resource: String,
    
    #[validate(length(min = 1, max = 50, message = "Action must be between 1 and 50 characters"))]
    pub action: String,
    
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssignRoleRequest {
    pub user_id: Uuid,
    pub role_id: Uuid,
    pub assigned_by: Uuid,
    pub expires_at: Option<DateTime<Utc>>,
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckRequest {
    pub user_id: Uuid,
    pub resource: String,
    pub action: String,
    pub context: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckResponse {
    pub allowed: bool,
    pub reason: String,
    pub matched_permissions: Vec<String>,
    pub effective_roles: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPermissions {
    pub user_id: Uuid,
    pub roles: Vec<RoleInfo>,
    pub permissions: Vec<PermissionInfo>,
    pub effective_permissions: HashSet<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleInfo {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub is_inherited: bool,
    pub assigned_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionInfo {
    pub id: Uuid,
    pub name: String,
    pub resource: String,
    pub action: String,
    pub source_role: String,
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone)]
pub struct RbacPolicy {
    pub cache_ttl: Duration,
    pub max_role_hierarchy_depth: u32,
    pub enable_permission_inheritance: bool,
    pub enable_role_conditions: bool,
}

impl Default for RbacPolicy {
    fn default() -> Self {
        Self {
            cache_ttl: Duration::minutes(15),
            max_role_hierarchy_depth: 10,
            enable_permission_inheritance: true,
            enable_role_conditions: true,
        }
    }
}

pub struct RbacService {
    db_pool: PgPool,
    redis_client: RedisClient,
    policy: RbacPolicy,
}

impl RbacService {
    pub fn new(db_pool: PgPool, redis_client: RedisClient) -> Self {
        Self {
            db_pool,
            redis_client,
            policy: RbacPolicy::default(),
        }
    }

    pub fn with_policy(mut self, policy: RbacPolicy) -> Self {
        self.policy = policy;
        self
    }

    /// Create a new role
    pub async fn create_role(&self, request: CreateRoleRequest) -> Result<Role, RbacError> {
        request.validate()
            .map_err(|e| RbacError::ValidationError(e.to_string()))?;

        // Check if role already exists
        if self.role_exists(&request.name).await? {
            return Err(RbacError::RoleAlreadyExists(request.name));
        }

        // Validate parent role if specified
        if let Some(parent_id) = request.parent_role_id {
            if !self.role_exists_by_id(parent_id).await? {
                return Err(RbacError::RoleNotFound(parent_id.to_string()));
            }

            // Check for circular dependencies
            if self.would_create_circular_dependency(parent_id, &request.name).await? {
                return Err(RbacError::CircularDependency);
            }
        }

        let role_id = Uuid::new_v4();
        let now = Utc::now();

        // Create role
        let query = r#"
            INSERT INTO roles (id, name, display_name, description, parent_role_id, is_system_role, is_default_role, max_session_duration_seconds, require_mfa, created_at, updated_at, created_by, updated_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING *
        "#;

        let role = sqlx::query_as::<_, Role>(query)
            .bind(role_id)
            .bind(&request.name)
            .bind(&request.name) // display_name same as name for now
            .bind(&request.description)
            .bind(request.parent_role_id)
            .bind(request.is_system_role)
            .bind(false) // is_default_role
            .bind(None::<i32>) // max_session_duration_seconds
            .bind(false) // require_mfa
            .bind(now)
            .bind(now)
            .bind(None::<Uuid>) // created_by
            .bind(None::<Uuid>) // updated_by
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        // Assign permissions to role
        for permission_name in &request.permissions {
            if let Ok(permission) = self.get_permission_by_name(permission_name).await {
                self.assign_permission_to_role(role.id, permission.id).await?;
            }
        }

        // Clear cache
        self.clear_role_cache(&role.name).await?;

        info!(
            role_id = %role.id,
            role_name = %role.name,
            "Role created successfully"
        );

        Ok(role)
    }

    /// Create a new permission
    pub async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission, RbacError> {
        request.validate()
            .map_err(|e| RbacError::ValidationError(e.to_string()))?;

        // Check if permission already exists
        if self.permission_exists(&request.name).await? {
            return Err(RbacError::PermissionAlreadyExists(request.name));
        }

        let permission_id = Uuid::new_v4();
        let now = Utc::now();

        let query = r#"
            INSERT INTO permissions (id, name, description, resource, action, category, scope, is_system_permission, created_at, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        "#;

        let permission = sqlx::query_as::<_, Permission>(query)
            .bind(permission_id)
            .bind(&request.name)
            .bind(&request.description)
            .bind(&request.resource)
            .bind(&request.action)
            .bind("general") // default category
            .bind("GLOBAL") // default scope
            .bind(false) // is_system_permission
            .bind(now)
            .bind(None::<Uuid>) // created_by
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        info!(
            permission_id = %permission.id,
            permission_name = %permission.name,
            resource = %permission.resource,
            action = %permission.action,
            "Permission created successfully"
        );

        Ok(permission)
    }

    /// Assign role to user
    pub async fn assign_role_to_user(&self, request: AssignRoleRequest) -> Result<(), RbacError> {
        // Validate user exists
        if !self.user_exists(request.user_id).await? {
            return Err(RbacError::UserNotFound);
        }

        // Validate role exists
        if !self.role_exists_by_id(request.role_id).await? {
            return Err(RbacError::RoleNotFound(request.role_id.to_string()));
        }

        // Check if user already has this role
        if self.user_has_role(request.user_id, request.role_id).await? {
            return Err(RbacError::UserAlreadyHasRole(request.role_id.to_string()));
        }

        let now = Utc::now();

        let query = r#"
            INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at, expires_at, is_active)
            VALUES ($1, $2, $3, $4, $5, $6)
        "#;

        sqlx::query(query)
            .bind(request.user_id)
            .bind(request.role_id)
            .bind(request.assigned_by)
            .bind(now)
            .bind(request.expires_at)
            .bind(true) // is_active
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        // Clear user permissions cache
        self.clear_user_permissions_cache(request.user_id).await?;

        info!(
            user_id = %request.user_id,
            role_id = %request.role_id,
            assigned_by = %request.assigned_by,
            "Role assigned to user successfully"
        );

        Ok(())
    }

    /// Check if user has permission
    pub async fn check_permission(&self, request: PermissionCheckRequest) -> Result<PermissionCheckResponse, RbacError> {
        // Try to get from cache first
        if let Ok(cached_result) = self.get_cached_permission_check(&request).await {
            return Ok(cached_result);
        }

        let user_permissions = self.get_user_permissions(request.user_id).await?;

        // Check direct permissions
        let permission_key = format!("{}:{}", request.resource, request.action);
        let has_permission = user_permissions.effective_permissions.contains(&permission_key);

        let mut matched_permissions = Vec::new();
        let mut effective_roles = Vec::new();

        if has_permission {
            // Find which permissions and roles granted access
            for permission in &user_permissions.permissions {
                if permission.resource == request.resource && permission.action == request.action {
                    matched_permissions.push(permission.name.clone());
                    effective_roles.push(permission.source_role.clone());
                }
            }
        }

        let response = PermissionCheckResponse {
            allowed: has_permission,
            reason: if has_permission {
                "Permission granted".to_string()
            } else {
                format!("No permission for {}:{}", request.resource, request.action)
            },
            matched_permissions,
            effective_roles,
        };

        // Cache the result
        self.cache_permission_check(&request, &response).await?;

        Ok(response)
    }

    /// Get all permissions for a user
    pub async fn get_user_permissions(&self, user_id: Uuid) -> Result<UserPermissions, RbacError> {
        // Try cache first
        if let Ok(cached_permissions) = self.get_cached_user_permissions(user_id).await {
            return Ok(cached_permissions);
        }

        // Get user roles
        let roles = self.get_user_roles(user_id).await?;

        // Get permissions for each role (including inherited)
        let mut all_permissions = Vec::new();
        let mut effective_permissions = HashSet::new();

        for role_info in &roles {
            let role_permissions = self.get_role_permissions(role_info.id).await?;

            for permission in role_permissions {
                let permission_key = format!("{}:{}", permission.resource, permission.action);
                effective_permissions.insert(permission_key);

                all_permissions.push(PermissionInfo {
                    id: permission.id,
                    name: permission.name,
                    resource: permission.resource,
                    action: permission.action,
                    source_role: role_info.name.clone(),
                    conditions: None, // Conditions are stored in role_permissions table
                });
            }

            // Add inherited permissions if enabled
            if self.policy.enable_permission_inheritance {
                let inherited_permissions = self.get_inherited_permissions(role_info.id).await?;
                for permission in inherited_permissions {
                    let permission_key = format!("{}:{}", permission.resource, permission.action);
                    effective_permissions.insert(permission_key);

                    all_permissions.push(PermissionInfo {
                        id: permission.id,
                        name: permission.name,
                        resource: permission.resource,
                        action: permission.action,
                        source_role: format!("{} (inherited)", role_info.name),
                        conditions: None, // Conditions are stored in role_permissions table
                    });
                }
            }
        }

        let user_permissions = UserPermissions {
            user_id,
            roles,
            permissions: all_permissions,
            effective_permissions,
        };

        // Cache the result
        self.cache_user_permissions(&user_permissions).await?;

        Ok(user_permissions)
    }

    /// Remove role from user
    pub async fn remove_role_from_user(&self, user_id: Uuid, role_id: Uuid) -> Result<(), RbacError> {
        let query = "DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2";

        let result = sqlx::query(query)
            .bind(user_id)
            .bind(role_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        if result.rows_affected() == 0 {
            return Err(RbacError::RoleNotFound(role_id.to_string()));
        }

        // Clear cache
        self.clear_user_permissions_cache(user_id).await?;

        info!(
            user_id = %user_id,
            role_id = %role_id,
            "Role removed from user successfully"
        );

        Ok(())
    }

    /// Delete a role
    pub async fn delete_role(&self, role_id: Uuid) -> Result<(), RbacError> {
        // Check if role exists
        let role = self.get_role_by_id(role_id).await?;

        // Check if role is a system role
        if role.is_system_role {
            return Err(RbacError::ValidationError("Cannot delete system role".to_string()));
        }

        // Remove all user assignments
        let query = "DELETE FROM user_roles WHERE role_id = $1";
        sqlx::query(query)
            .bind(role_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        // Remove all permission assignments
        let query = "DELETE FROM role_permissions WHERE role_id = $1";
        sqlx::query(query)
            .bind(role_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        // Delete the role
        let query = "DELETE FROM roles WHERE id = $1";
        sqlx::query(query)
            .bind(role_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        // Clear cache
        self.clear_role_cache(&role.name).await?;

        info!(
            role_id = %role_id,
            role_name = %role.name,
            "Role deleted successfully"
        );

        Ok(())
    }

    // Private helper methods

    /// Get Redis connection
    fn get_redis_connection(&self) -> Result<Connection, RbacError> {
        self.redis_client
            .get_connection()
            .map_err(|e| RbacError::RedisError(e.to_string()))
    }

    /// Check if role exists by name
    async fn role_exists(&self, name: &str) -> Result<bool, RbacError> {
        let query = "SELECT EXISTS(SELECT 1 FROM roles WHERE name = $1)";
        let exists: (bool,) = sqlx::query_as(query)
            .bind(name)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;
        Ok(exists.0)
    }

    /// Check if role exists by ID
    async fn role_exists_by_id(&self, role_id: Uuid) -> Result<bool, RbacError> {
        let query = "SELECT EXISTS(SELECT 1 FROM roles WHERE id = $1)";
        let exists: (bool,) = sqlx::query_as(query)
            .bind(role_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;
        Ok(exists.0)
    }

    /// Check if permission exists by name
    async fn permission_exists(&self, name: &str) -> Result<bool, RbacError> {
        let query = "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1)";
        let exists: (bool,) = sqlx::query_as(query)
            .bind(name)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;
        Ok(exists.0)
    }

    /// Check if user exists
    async fn user_exists(&self, user_id: Uuid) -> Result<bool, RbacError> {
        let query = "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)";
        let exists: (bool,) = sqlx::query_as(query)
            .bind(user_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;
        Ok(exists.0)
    }

    /// Check if user has role
    async fn user_has_role(&self, user_id: Uuid, role_id: Uuid) -> Result<bool, RbacError> {
        let query = "SELECT EXISTS(SELECT 1 FROM user_roles WHERE user_id = $1 AND role_id = $2 AND is_active = true AND (expires_at IS NULL OR expires_at > NOW()))";
        let exists: (bool,) = sqlx::query_as(query)
            .bind(user_id)
            .bind(role_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;
        Ok(exists.0)
    }

    /// Get role by ID
    async fn get_role_by_id(&self, role_id: Uuid) -> Result<Role, RbacError> {
        let query = "SELECT * FROM roles WHERE id = $1";
        sqlx::query_as::<_, Role>(query)
            .bind(role_id)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RbacError::RoleNotFound(role_id.to_string()))
    }

    /// Get permission by name
    async fn get_permission_by_name(&self, name: &str) -> Result<Permission, RbacError> {
        let query = "SELECT * FROM permissions WHERE name = $1";
        sqlx::query_as::<_, Permission>(query)
            .bind(name)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?
            .ok_or_else(|| RbacError::PermissionNotFound(name.to_string()))
    }

    /// Get user roles
    async fn get_user_roles(&self, user_id: Uuid) -> Result<Vec<RoleInfo>, RbacError> {
        let query = r#"
            SELECT r.id, r.name, r.description, ur.assigned_at, ur.expires_at,
                   false as is_inherited
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1
            AND ur.is_active = true
            AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
            ORDER BY ur.assigned_at
        "#;

        let rows = sqlx::query(query)
            .bind(user_id)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        let mut roles = Vec::new();
        for row in rows {
            roles.push(RoleInfo {
                id: row.get("id"),
                name: row.get("name"),
                description: row.get("description"),
                is_inherited: row.get("is_inherited"),
                assigned_at: row.get("assigned_at"),
                expires_at: row.get("expires_at"),
            });
        }

        Ok(roles)
    }

    /// Get role permissions
    async fn get_role_permissions(&self, role_id: Uuid) -> Result<Vec<Permission>, RbacError> {
        let query = r#"
            SELECT p.*
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
        "#;

        sqlx::query_as::<_, Permission>(query)
            .bind(role_id)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))
    }

    /// Get inherited permissions from parent roles
    async fn get_inherited_permissions(&self, role_id: Uuid) -> Result<Vec<Permission>, RbacError> {
        let query = r#"
            WITH RECURSIVE role_hierarchy AS (
                SELECT id, parent_role_id, 1 as depth
                FROM roles
                WHERE id = $1

                UNION ALL

                SELECT r.id, r.parent_role_id, rh.depth + 1
                FROM roles r
                JOIN role_hierarchy rh ON r.id = rh.parent_role_id
                WHERE rh.depth < $2
            )
            SELECT DISTINCT p.*
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN role_hierarchy rh ON rp.role_id = rh.parent_role_id
            WHERE rh.parent_role_id IS NOT NULL
        "#;

        sqlx::query_as::<_, Permission>(query)
            .bind(role_id)
            .bind(self.policy.max_role_hierarchy_depth as i32)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))
    }

    /// Assign permission to role
    async fn assign_permission_to_role(&self, role_id: Uuid, permission_id: Uuid) -> Result<(), RbacError> {
        let query = r#"
            INSERT INTO role_permissions (role_id, permission_id, granted_at, granted_by, conditions)
            VALUES ($1, $2, NOW(), $3, $4)
            ON CONFLICT (role_id, permission_id) DO NOTHING
        "#;

        sqlx::query(query)
            .bind(role_id)
            .bind(permission_id)
            .bind(None::<Uuid>) // granted_by
            .bind(None::<serde_json::Value>) // conditions
            .execute(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// Check for circular dependencies
    async fn would_create_circular_dependency(&self, parent_id: Uuid, child_name: &str) -> Result<bool, RbacError> {
        let query = r#"
            WITH RECURSIVE role_hierarchy AS (
                SELECT id, parent_role_id, name
                FROM roles
                WHERE id = $1

                UNION ALL

                SELECT r.id, r.parent_role_id, r.name
                FROM roles r
                JOIN role_hierarchy rh ON r.id = rh.parent_role_id
            )
            SELECT EXISTS(SELECT 1 FROM role_hierarchy WHERE name = $2)
        "#;

        let exists: (bool,) = sqlx::query_as(query)
            .bind(parent_id)
            .bind(child_name)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| RbacError::DatabaseError(e.to_string()))?;

        Ok(exists.0)
    }

    // Cache methods

    /// Get cached user permissions
    async fn get_cached_user_permissions(&self, user_id: Uuid) -> Result<UserPermissions, RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_key = format!("rbac:user_permissions:{}", user_id);

        let cached_data: String = conn.get(&cache_key)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        serde_json::from_str(&cached_data)
            .map_err(|e| RbacError::InternalError(e.to_string()))
    }

    /// Cache user permissions
    async fn cache_user_permissions(&self, permissions: &UserPermissions) -> Result<(), RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_key = format!("rbac:user_permissions:{}", permissions.user_id);

        let serialized = serde_json::to_string(permissions)
            .map_err(|e| RbacError::InternalError(e.to_string()))?;

        let _: () = conn.set_ex(&cache_key, serialized, self.policy.cache_ttl.num_seconds() as u64)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        Ok(())
    }

    /// Get cached permission check
    async fn get_cached_permission_check(&self, request: &PermissionCheckRequest) -> Result<PermissionCheckResponse, RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_key = format!("rbac:permission_check:{}:{}:{}",
            request.user_id, request.resource, request.action);

        let cached_data: String = conn.get(&cache_key)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        serde_json::from_str(&cached_data)
            .map_err(|e| RbacError::InternalError(e.to_string()))
    }

    /// Cache permission check result
    async fn cache_permission_check(&self, request: &PermissionCheckRequest, response: &PermissionCheckResponse) -> Result<(), RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_key = format!("rbac:permission_check:{}:{}:{}",
            request.user_id, request.resource, request.action);

        let serialized = serde_json::to_string(response)
            .map_err(|e| RbacError::InternalError(e.to_string()))?;

        let _: () = conn.set_ex(&cache_key, serialized, self.policy.cache_ttl.num_seconds() as u64)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        Ok(())
    }

    /// Clear user permissions cache
    async fn clear_user_permissions_cache(&self, user_id: Uuid) -> Result<(), RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_key = format!("rbac:user_permissions:{}", user_id);
        let permission_pattern = format!("rbac:permission_check:{}:*", user_id);

        let _: () = conn.del(&cache_key)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        // Clear permission check cache for this user
        let keys: Vec<String> = conn.keys(&permission_pattern)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        if !keys.is_empty() {
            let _: () = conn.del(&keys)
                .map_err(|e| RbacError::RedisError(e.to_string()))?;
        }

        Ok(())
    }

    /// Clear role cache
    async fn clear_role_cache(&self, role_name: &str) -> Result<(), RbacError> {
        let mut conn = self.get_redis_connection()?;
        let cache_pattern = format!("rbac:*{}*", role_name);

        let keys: Vec<String> = conn.keys(&cache_pattern)
            .map_err(|e| RbacError::RedisError(e.to_string()))?;

        if !keys.is_empty() {
            let _: () = conn.del(&keys)
                .map_err(|e| RbacError::RedisError(e.to_string()))?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    // Helper function to create test RBAC service
    async fn create_test_rbac_service() -> RbacService {
        let database_url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgresql://admin:secret@localhost:5432/crabshield_db".to_string());
        let pool = PgPool::connect(&database_url).await.unwrap();

        let redis_client = redis::Client::open("redis://localhost:6379").unwrap();

        RbacService::new(pool, redis_client)
    }

    #[tokio::test]
    async fn test_role_creation() {
        let service = create_test_rbac_service().await;

        let request = CreateRoleRequest {
            name: format!("test_role_{}", Uuid::new_v4()),
            description: Some("Test role for unit testing".to_string()),
            parent_role_id: None,
            permissions: vec!["read:users".to_string()],
            is_system_role: false,
        };

        let result = service.create_role(request).await;
        assert!(result.is_ok());

        let role = result.unwrap();
        assert!(!role.name.is_empty());
        assert_eq!(role.description, Some("Test role for unit testing".to_string()));
    }

    #[tokio::test]
    async fn test_permission_creation() {
        let service = create_test_rbac_service().await;

        let request = CreatePermissionRequest {
            name: format!("test_permission_{}", Uuid::new_v4()),
            description: Some("Test permission for unit testing".to_string()),
            resource: "test_resource".to_string(),
            action: "test_action".to_string(),
            conditions: None,
        };

        let result = service.create_permission(request).await;
        assert!(result.is_ok());

        let permission = result.unwrap();
        assert!(!permission.name.is_empty());
        assert_eq!(permission.resource, "test_resource");
        assert_eq!(permission.action, "test_action");
    }

    #[tokio::test]
    async fn test_role_hierarchy() {
        let service = create_test_rbac_service().await;

        // Create parent role
        let parent_request = CreateRoleRequest {
            name: format!("parent_role_{}", Uuid::new_v4()),
            description: Some("Parent role".to_string()),
            parent_role_id: None,
            permissions: vec![],
            is_system_role: false,
        };

        let parent_role = service.create_role(parent_request).await.unwrap();

        // Create child role
        let child_request = CreateRoleRequest {
            name: format!("child_role_{}", Uuid::new_v4()),
            description: Some("Child role".to_string()),
            parent_role_id: Some(parent_role.id),
            permissions: vec![],
            is_system_role: false,
        };

        let result = service.create_role(child_request).await;
        assert!(result.is_ok());

        let child_role = result.unwrap();
        assert_eq!(child_role.parent_role_id, Some(parent_role.id));
    }

    #[tokio::test]
    async fn test_duplicate_role_creation() {
        let service = create_test_rbac_service().await;

        let role_name = format!("duplicate_test_role_{}", Uuid::new_v4());

        let request1 = CreateRoleRequest {
            name: role_name.clone(),
            description: Some("First role".to_string()),
            parent_role_id: None,
            permissions: vec![],
            is_system_role: false,
        };

        let request2 = CreateRoleRequest {
            name: role_name.clone(),
            description: Some("Second role".to_string()),
            parent_role_id: None,
            permissions: vec![],
            is_system_role: false,
        };

        // First creation should succeed
        let result1 = service.create_role(request1).await;
        assert!(result1.is_ok());

        // Second creation should fail
        let result2 = service.create_role(request2).await;
        assert!(result2.is_err());
        assert!(matches!(result2.unwrap_err(), RbacError::RoleAlreadyExists(_)));
    }

    #[tokio::test]
    async fn test_duplicate_permission_creation() {
        let service = create_test_rbac_service().await;

        let permission_name = format!("duplicate_test_permission_{}", Uuid::new_v4());

        let request1 = CreatePermissionRequest {
            name: permission_name.clone(),
            description: Some("First permission".to_string()),
            resource: "test_resource".to_string(),
            action: "test_action".to_string(),
        };

        let request2 = CreatePermissionRequest {
            name: permission_name.clone(),
            description: Some("Second permission".to_string()),
            resource: "test_resource".to_string(),
            action: "test_action".to_string(),
        };

        // First creation should succeed
        let result1 = service.create_permission(request1).await;
        assert!(result1.is_ok());

        // Second creation should fail
        let result2 = service.create_permission(request2).await;
        assert!(result2.is_err());
        assert!(matches!(result2.unwrap_err(), RbacError::PermissionAlreadyExists(_)));
    }

    #[test]
    fn test_rbac_policy_defaults() {
        let policy = RbacPolicy::default();
        assert_eq!(policy.cache_ttl, Duration::minutes(15));
        assert_eq!(policy.max_role_hierarchy_depth, 10);
        assert!(policy.enable_permission_inheritance);
        assert!(policy.enable_role_conditions);
    }

    #[test]
    fn test_create_role_request_validation() {
        // Valid request
        let valid_request = CreateRoleRequest {
            name: "valid_role".to_string(),
            description: Some("Valid role".to_string()),
            parent_role_id: None,
            permissions: vec!["read:users".to_string()],
            is_system_role: false,
        };
        assert!(valid_request.validate().is_ok());

        // Invalid request - empty name
        let invalid_request = CreateRoleRequest {
            name: "".to_string(),
            description: Some("Invalid role".to_string()),
            parent_role_id: None,
            permissions: vec![],
            is_system_role: false,
        };
        assert!(invalid_request.validate().is_err());
    }

    #[test]
    fn test_create_permission_request_validation() {
        // Valid request
        let valid_request = CreatePermissionRequest {
            name: "valid_permission".to_string(),
            description: Some("Valid permission".to_string()),
            resource: "users".to_string(),
            action: "read".to_string(),
        };
        assert!(valid_request.validate().is_ok());

        // Invalid request - empty name
        let invalid_request = CreatePermissionRequest {
            name: "".to_string(),
            description: Some("Invalid permission".to_string()),
            resource: "users".to_string(),
            action: "read".to_string(),
        };
        assert!(invalid_request.validate().is_err());
    }
}
