use actix_cors::Cors;
use actix_web::http::header;

/// Create CORS middleware with environment-specific configuration
pub fn create_cors(environment: &str) -> Cors {
    match environment.to_lowercase().as_str() {
        "development" | "dev" => development_cors(),
        "production" | "prod" => production_cors(),
        _ => default_cors(),
    }
}

/// Development CORS configuration - more permissive for local development
fn development_cors() -> Cors {
    Cors::default()
        .allowed_origin("http://localhost:3000")  // Next.js dev server
        .allowed_origin("http://localhost:3001")  // Alternative dev port
        .allowed_origin("http://127.0.0.1:3000")
        .allowed_origin("http://127.0.0.1:3001")
        .allowed_origin("http://localhost:8080")  // APISIX Gateway
        .allowed_origin("http://127.0.0.1:8080")
        .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"])
        .allowed_headers(common_allowed_headers())
        .expose_headers(vec![
            header::HeaderName::from_static("x-rate-limit-remaining"),
            header::HeaderName::from_static("x-rate-limit-reset"),
            header::HeaderName::from_static("x-request-id"),
        ])
        .supports_credentials()
        .max_age(3600) // 1 hour
}

/// Production CORS configuration - restrictive and secure
fn production_cors() -> Cors {
    Cors::default()
        .allowed_origin("https://app.crabshield.com")      // Main application
        .allowed_origin("https://portal.crabshield.com")   // Client portal
        .allowed_origin("https://admin.crabshield.com")    // Admin interface
        .allowed_origin("https://api.crabshield.com")      // API Gateway
        .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"])
        .allowed_headers(common_allowed_headers())
        .expose_headers(vec![
            header::HeaderName::from_static("x-rate-limit-remaining"),
            header::HeaderName::from_static("x-rate-limit-reset"),
            header::HeaderName::from_static("x-request-id"),
        ])
        .supports_credentials()
        .max_age(86400) // 24 hours
}

/// Default CORS configuration - balanced security
fn default_cors() -> Cors {
    Cors::default()
        .allowed_origin("http://localhost:3000")
        .allowed_origin("https://localhost:3000")
        .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS"])
        .allowed_headers(vec![
            header::AUTHORIZATION,
            header::ACCEPT,
            header::CONTENT_TYPE,
            header::HeaderName::from_static("x-csrf-token"),
        ])
        .expose_headers(vec![
            header::HeaderName::from_static("x-rate-limit-remaining"),
            header::HeaderName::from_static("x-rate-limit-reset"),
        ])
        .supports_credentials()
        .max_age(3600)
}

/// Create CORS middleware with custom allowed origins
pub fn create_cors_with_origins(allowed_origins: Vec<&str>) -> Cors {
    let mut cors = Cors::default();
    
    for origin in allowed_origins {
        cors = cors.allowed_origin(origin);
    }
    
    cors.allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"])
        .allowed_headers(common_allowed_headers())
        .expose_headers(vec![
            header::HeaderName::from_static("x-rate-limit-remaining"),
            header::HeaderName::from_static("x-rate-limit-reset"),
            header::HeaderName::from_static("x-request-id"),
        ])
        .supports_credentials()
        .max_age(3600)
}

fn common_allowed_headers() -> Vec<header::HeaderName> {
    vec![
        header::AUTHORIZATION,
        header::ACCEPT,
        header::CONTENT_TYPE,
        header::HeaderName::from_static("x-csrf-token"),
        header::HeaderName::from_static("x-xsrf-token"),
        header::HeaderName::from_static("x-device-fingerprint"),
        header::HeaderName::from_static("x-request-id"),
    ]
}
