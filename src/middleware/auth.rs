use actix_web::{dev::ServiceRequest, Error, FromRequest, HttpRequest, HttpMessage};
use actix_web_httpauth::extractors::bearer::{BearerAuth, Config};
use actix_web_httpauth::extractors::AuthenticationError;
use crate::services::JwtService;
use crate::utils::errors::AppError;
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use std::future::{Ready, ready};
use tracing::{error, warn};

pub async fn jwt_validator(
    req: ServiceRequest,
    credentials: BearerAuth,
) -> Result<ServiceRequest, (Error, ServiceRequest)> {
    let token = credentials.token();

    // Get JwtService from app data
    let jwt_service = match req.app_data::<actix_web::web::Data<JwtService>>() {
        Some(service) => service,
        None => {
            error!("JwtService not found in app data");
            let config = Config::default().realm("Restricted");
            let auth_error = AuthenticationError::from(config).into();
            return Err((auth_error, req));
        }
    };

    // Validate the token
    match jwt_service.validate_token(token) {
        Ok(claims) => {
            // Store the claims in request extensions for later use
            req.extensions_mut().insert(claims);
            Ok(req)
        }
        Err(e) => {
            warn!("JWT validation failed: {}", e);
            let config = Config::default().realm("Restricted");
            let auth_error = AuthenticationError::from(config).into();
            Err((auth_error, req))
        }
    }
}

pub fn auth_config() -> Config {
    Config::default().realm("Restricted")
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticatedUser {
    pub user_id: Uuid,
    pub email: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
    pub session_id: Uuid,
    pub mfa_verified: bool,
    pub trusted_device: bool,
}

impl FromRequest for AuthenticatedUser {
    type Error = Error;
    type Future = Ready<Result<Self, Self::Error>>;

    fn from_request(req: &HttpRequest, _payload: &mut actix_web::dev::Payload) -> Self::Future {
        // Extract JWT claims from request extensions
        let extensions = req.extensions();

        if let Some(claims) = extensions.get::<crate::services::jwt_service::Claims>() {
            let user = AuthenticatedUser {
                user_id: claims.user_id,
                email: claims.email.clone(),
                roles: claims.roles.clone(),
                permissions: claims.permissions.clone(),
                session_id: claims.session_id,
                mfa_verified: claims.mfa_verified,
                trusted_device: claims.trusted_device,
            };
            ready(Ok(user))
        } else {
            // No valid JWT claims found
            let app_error = AppError::Unauthorized("Authentication required".to_string());
            ready(Err(app_error.into()))
        }
    }
}

// Helper function to extract user ID from request
pub fn extract_user_id(req: &HttpRequest) -> Option<Uuid> {
    req.extensions().get::<crate::services::jwt_service::Claims>().map(|claims| claims.user_id)
}

// Helper function to extract user ID from ServiceRequest
pub fn extract_user_id_from_service_request(req: &ServiceRequest) -> Option<Uuid> {
    req.extensions().get::<crate::services::jwt_service::Claims>().map(|claims| claims.user_id)
}
