use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error,
    http::header::{HeaderName, HeaderValue},
};
use futures_util::future::LocalBoxFuture;
use std::{
    future::{ready, Ready},
    rc::Rc,
};

/// Security headers middleware that adds OWASP-recommended security headers
pub struct SecurityHeaders {
    config: SecurityHeadersConfig,
}

#[derive(Debug, Clone)]
pub struct SecurityHeadersConfig {
    pub content_security_policy: Option<String>,
    pub strict_transport_security: Option<String>,
    pub x_frame_options: String,
    pub x_content_type_options: String,
    pub x_xss_protection: String,
    pub referrer_policy: String,
    pub permissions_policy: Option<String>,
    pub cross_origin_embedder_policy: Option<String>,
    pub cross_origin_opener_policy: Option<String>,
    pub cross_origin_resource_policy: Option<String>,
}

impl Default for SecurityHeadersConfig {
    fn default() -> Self {
        Self {
            // Content Security Policy - restrictive by default
            content_security_policy: Some(
                "default-src 'self'; \
                 script-src 'self' 'unsafe-inline' 'unsafe-eval'; \
                 style-src 'self' 'unsafe-inline'; \
                 img-src 'self' data: https:; \
                 font-src 'self' data:; \
                 connect-src 'self'; \
                 frame-ancestors 'none'; \
                 base-uri 'self'; \
                 form-action 'self'".to_string()
            ),
            
            // HSTS - enforce HTTPS for 1 year, include subdomains
            strict_transport_security: Some(
                "max-age=31536000; includeSubDomains; preload".to_string()
            ),
            
            // Prevent clickjacking
            x_frame_options: "DENY".to_string(),
            
            // Prevent MIME type sniffing
            x_content_type_options: "nosniff".to_string(),
            
            // XSS protection (legacy but still useful)
            x_xss_protection: "1; mode=block".to_string(),
            
            // Control referrer information
            referrer_policy: "strict-origin-when-cross-origin".to_string(),
            
            // Permissions policy (formerly Feature Policy)
            permissions_policy: Some(
                "camera=(), microphone=(), geolocation=(), payment=()".to_string()
            ),
            
            // Cross-Origin policies for enhanced security
            cross_origin_embedder_policy: Some("require-corp".to_string()),
            cross_origin_opener_policy: Some("same-origin".to_string()),
            cross_origin_resource_policy: Some("same-origin".to_string()),
        }
    }
}

impl SecurityHeaders {
    pub fn new() -> Self {
        Self {
            config: SecurityHeadersConfig::default(),
        }
    }

    pub fn with_config(config: SecurityHeadersConfig) -> Self {
        Self { config }
    }

    /// Create a development-friendly configuration with relaxed CSP
    pub fn development() -> Self {
        let mut config = SecurityHeadersConfig::default();
        
        // More relaxed CSP for development
        config.content_security_policy = Some(
            "default-src 'self' 'unsafe-inline' 'unsafe-eval'; \
             script-src 'self' 'unsafe-inline' 'unsafe-eval'; \
             style-src 'self' 'unsafe-inline'; \
             img-src 'self' data: https: http:; \
             font-src 'self' data: https:; \
             connect-src 'self' ws: wss: http: https:; \
             frame-ancestors 'self'; \
             base-uri 'self'".to_string()
        );
        
        // Disable HSTS in development
        config.strict_transport_security = None;
        
        // Allow framing in development (for debugging tools)
        config.x_frame_options = "SAMEORIGIN".to_string();
        
        // Relax cross-origin policies for development
        config.cross_origin_embedder_policy = None;
        config.cross_origin_opener_policy = None;
        config.cross_origin_resource_policy = Some("cross-origin".to_string());

        Self { config }
    }

    /// Create a production configuration with strict security
    pub fn production() -> Self {
        let mut config = SecurityHeadersConfig::default();
        
        // Strict CSP for production
        config.content_security_policy = Some(
            "default-src 'self'; \
             script-src 'self'; \
             style-src 'self'; \
             img-src 'self' data:; \
             font-src 'self'; \
             connect-src 'self'; \
             frame-ancestors 'none'; \
             base-uri 'self'; \
             form-action 'self'; \
             upgrade-insecure-requests".to_string()
        );

        Self { config }
    }
}

impl<S, B> Transform<S, ServiceRequest> for SecurityHeaders
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = SecurityHeadersMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(SecurityHeadersMiddleware {
            service: Rc::new(service),
            config: self.config.clone(),
        }))
    }
}

pub struct SecurityHeadersMiddleware<S> {
    service: Rc<S>,
    config: SecurityHeadersConfig,
}

impl<S, B> Service<ServiceRequest> for SecurityHeadersMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let config = self.config.clone();

        Box::pin(async move {
            let mut response = service.call(req).await?;

            // Add security headers to the response
            let headers = response.headers_mut();

            // Content Security Policy
            if let Some(ref csp) = config.content_security_policy {
                headers.insert(
                    HeaderName::from_static("content-security-policy"),
                    HeaderValue::from_str(csp).unwrap_or_else(|_| HeaderValue::from_static("default-src 'self'")),
                );
            }

            // Strict Transport Security (HSTS)
            if let Some(ref hsts) = config.strict_transport_security {
                headers.insert(
                    HeaderName::from_static("strict-transport-security"),
                    HeaderValue::from_str(hsts).unwrap_or_else(|_| HeaderValue::from_static("max-age=31536000")),
                );
            }

            // X-Frame-Options
            headers.insert(
                HeaderName::from_static("x-frame-options"),
                HeaderValue::from_str(&config.x_frame_options).unwrap_or_else(|_| HeaderValue::from_static("DENY")),
            );

            // X-Content-Type-Options
            headers.insert(
                HeaderName::from_static("x-content-type-options"),
                HeaderValue::from_str(&config.x_content_type_options).unwrap_or_else(|_| HeaderValue::from_static("nosniff")),
            );

            // X-XSS-Protection
            headers.insert(
                HeaderName::from_static("x-xss-protection"),
                HeaderValue::from_str(&config.x_xss_protection).unwrap_or_else(|_| HeaderValue::from_static("1; mode=block")),
            );

            // Referrer-Policy
            headers.insert(
                HeaderName::from_static("referrer-policy"),
                HeaderValue::from_str(&config.referrer_policy).unwrap_or_else(|_| HeaderValue::from_static("strict-origin-when-cross-origin")),
            );

            // Permissions-Policy
            if let Some(ref permissions) = config.permissions_policy {
                headers.insert(
                    HeaderName::from_static("permissions-policy"),
                    HeaderValue::from_str(permissions).unwrap_or_else(|_| HeaderValue::from_static("camera=(), microphone=()")),
                );
            }

            // Cross-Origin-Embedder-Policy
            if let Some(ref coep) = config.cross_origin_embedder_policy {
                headers.insert(
                    HeaderName::from_static("cross-origin-embedder-policy"),
                    HeaderValue::from_str(coep).unwrap_or_else(|_| HeaderValue::from_static("require-corp")),
                );
            }

            // Cross-Origin-Opener-Policy
            if let Some(ref coop) = config.cross_origin_opener_policy {
                headers.insert(
                    HeaderName::from_static("cross-origin-opener-policy"),
                    HeaderValue::from_str(coop).unwrap_or_else(|_| HeaderValue::from_static("same-origin")),
                );
            }

            // Cross-Origin-Resource-Policy
            if let Some(ref corp) = config.cross_origin_resource_policy {
                headers.insert(
                    HeaderName::from_static("cross-origin-resource-policy"),
                    HeaderValue::from_str(corp).unwrap_or_else(|_| HeaderValue::from_static("same-origin")),
                );
            }

            // Additional security headers
            headers.insert(
                HeaderName::from_static("x-permitted-cross-domain-policies"),
                HeaderValue::from_static("none"),
            );

            headers.insert(
                HeaderName::from_static("x-download-options"),
                HeaderValue::from_static("noopen"),
            );

            // Remove server information
            headers.remove("server");

            Ok(response)
        })
    }
}

/// Helper function to create security headers middleware with environment-specific config
pub fn create_security_headers(environment: &str) -> SecurityHeaders {
    match environment.to_lowercase().as_str() {
        "development" | "dev" => SecurityHeaders::development(),
        "production" | "prod" => SecurityHeaders::production(),
        _ => SecurityHeaders::new(), // Default configuration
    }
}
