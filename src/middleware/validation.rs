use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpResponse, Result, body::BoxBody,
};
use futures_util::future::LocalBoxFuture;
use std::{
    future::{ready, Ready},
    rc::Rc,
};
use serde_json::Value;
use tracing::warn;
use crate::utils::errors::AppError;

/// Middleware for input validation and sanitization
pub struct InputValidation {
    max_body_size: usize,
    allowed_content_types: Vec<String>,
    enable_csrf_protection: bool,
}

impl InputValidation {
    pub fn new() -> Self {
        Self {
            max_body_size: 1024 * 1024, // 1MB default
            allowed_content_types: vec![
                "application/json".to_string(),
                "application/x-www-form-urlencoded".to_string(),
            ],
            enable_csrf_protection: true,
        }
    }

    pub fn max_body_size(mut self, size: usize) -> Self {
        self.max_body_size = size;
        self
    }

    pub fn allowed_content_types(mut self, types: Vec<String>) -> Self {
        self.allowed_content_types = types;
        self
    }

    pub fn csrf_protection(mut self, enabled: bool) -> Self {
        self.enable_csrf_protection = enabled;
        self
    }
}

impl Default for InputValidation {
    fn default() -> Self {
        Self::new()
    }
}

impl<S> Transform<S, ServiceRequest> for InputValidation
where
    S: Service<ServiceRequest, Response = ServiceResponse<BoxBody>, Error = Error> + 'static,
    S::Future: 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Transform = InputValidationMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(InputValidationMiddleware {
            service: Rc::new(service),
            max_body_size: self.max_body_size,
            allowed_content_types: self.allowed_content_types.clone(),
            enable_csrf_protection: self.enable_csrf_protection,
        }))
    }
}

pub struct InputValidationMiddleware<S> {
    service: Rc<S>,
    max_body_size: usize,
    allowed_content_types: Vec<String>,
    enable_csrf_protection: bool,
}

impl<S> Service<ServiceRequest> for InputValidationMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<BoxBody>, Error = Error> + 'static,
    S::Future: 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = self.service.clone();
        let max_body_size = self.max_body_size;
        let allowed_content_types = self.allowed_content_types.clone();
        let enable_csrf_protection = self.enable_csrf_protection;

        Box::pin(async move {
            // Validate content type for POST/PUT/PATCH requests
            if matches!(req.method().as_str(), "POST" | "PUT" | "PATCH") {
                if let Some(content_type) = req.headers().get("content-type") {
                    let content_type_str = content_type.to_str().unwrap_or("");
                    let is_allowed = allowed_content_types.iter().any(|allowed| {
                        content_type_str.starts_with(allowed)
                    });

                    if !is_allowed {
                        warn!("Invalid content type: {}", content_type_str);
                        let error = AppError::BadRequest("Invalid content type".to_string());
                        return Ok(req.into_response(
                            HttpResponse::BadRequest()
                                .json(serde_json::json!({
                                    "error": "invalid_content_type",
                                    "message": error.to_string()
                                }))
                        ));
                    }
                } else if req.method() != "GET" && req.method() != "DELETE" {
                    warn!("Missing content type header");
                    let error = AppError::BadRequest("Content-Type header required".to_string());
                    return Ok(req.into_response(
                        HttpResponse::BadRequest()
                            .json(serde_json::json!({
                                "error": "missing_content_type",
                                "message": error.to_string()
                            }))
                    ));
                }
            }

            // Validate content length
            if let Some(content_length) = req.headers().get("content-length") {
                if let Ok(length_str) = content_length.to_str() {
                    if let Ok(length) = length_str.parse::<usize>() {
                        if length > max_body_size {
                            warn!("Request body too large: {} bytes", length);
                            let error = AppError::BadRequest(format!(
                                "Request body too large. Maximum size: {} bytes", 
                                max_body_size
                            ));
                            return Ok(req.into_response(
                                HttpResponse::PayloadTooLarge()
                                    .json(serde_json::json!({
                                        "error": "payload_too_large",
                                        "message": error.to_string(),
                                        "max_size": max_body_size
                                    }))
                            ));
                        }
                    }
                }
            }

            // CSRF Protection for state-changing operations
            if enable_csrf_protection && matches!(req.method().as_str(), "POST" | "PUT" | "PATCH" | "DELETE") {
                // Check for CSRF token in header
                let csrf_token = req.headers().get("x-csrf-token")
                    .or_else(|| req.headers().get("x-xsrf-token"));

                if csrf_token.is_none() {
                    // For API endpoints, we can be more lenient and check for proper authentication
                    // instead of requiring CSRF tokens for every request
                    if !req.path().starts_with("/api/") {
                        warn!("Missing CSRF token for state-changing operation");
                        let error = AppError::BadRequest("CSRF token required".to_string());
                        return Ok(req.into_response(
                            HttpResponse::Forbidden()
                                .json(serde_json::json!({
                                    "error": "csrf_token_required",
                                    "message": error.to_string()
                                }))
                        ));
                    }
                }
            }

            // Validate common headers
            if let Err(response) = validate_headers(&req) {
                return Ok(response);
            }

            // Continue to the next service
            service.call(req).await
        })
    }
}

/// Validate common security headers and request format
fn validate_headers(req: &ServiceRequest) -> Result<(), ServiceResponse<BoxBody>> {
    // Check for suspicious user agents
    if let Some(user_agent) = req.headers().get("user-agent") {
        if let Ok(ua_str) = user_agent.to_str() {
            // Block known malicious patterns
            let suspicious_patterns = [
                "sqlmap", "nikto", "nmap", "masscan", "zap", "burp",
                "python-requests", "curl", "wget"
            ];
            
            let ua_lower = ua_str.to_lowercase();
            for pattern in &suspicious_patterns {
                if ua_lower.contains(pattern) {
                    warn!("Suspicious user agent detected: {}", ua_str);
                    // Don't block completely, just log for now
                    break;
                }
            }
        }
    }

    // Validate Accept header for API endpoints
    if req.path().starts_with("/api/") {
        if let Some(accept) = req.headers().get("accept") {
            if let Ok(accept_str) = accept.to_str() {
                if !accept_str.contains("application/json") && !accept_str.contains("*/*") {
                    warn!("Invalid Accept header for API endpoint: {}", accept_str);
                    // Log but don't block
                }
            }
        }
    }

    Ok(())
}

/// Sanitize JSON input to prevent injection attacks
pub fn sanitize_json_input(value: &mut Value) {
    match value {
        Value::String(s) => {
            // Remove potentially dangerous characters
            *s = s.chars()
                .filter(|c| !matches!(c, '<' | '>' | '&' | '"' | '\'' | '\0'..='\x1F'))
                .collect();
            
            // Limit string length
            if s.len() > 10000 {
                s.truncate(10000);
            }
        }
        Value::Object(map) => {
            for (_, v) in map.iter_mut() {
                sanitize_json_input(v);
            }
        }
        Value::Array(arr) => {
            for v in arr.iter_mut() {
                sanitize_json_input(v);
            }
        }
        _ => {} // Numbers, booleans, and null are safe
    }
}

/// Create input validation middleware with default settings
pub fn create_input_validation() -> InputValidation {
    InputValidation::new()
        .max_body_size(2 * 1024 * 1024) // 2MB
        .csrf_protection(true)
}

/// Create input validation middleware for development (more permissive)
pub fn create_input_validation_dev() -> InputValidation {
    InputValidation::new()
        .max_body_size(10 * 1024 * 1024) // 10MB
        .csrf_protection(false) // Disabled for easier testing
}
