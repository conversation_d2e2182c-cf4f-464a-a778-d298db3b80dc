use crate::middleware::auth::AuthenticatedUser;
use crate::services::rbac_service::{
    RbacService, CreateRoleRequest, CreatePermissionRequest, AssignRoleRequest,
    PermissionCheckRequest, RbacError,
};
use crate::utils::errors::AppError;
use actix_web::{web, HttpResponse, Result};
use serde_json::json;
use tracing::{error, info};
use uuid::Uuid;

/// Create a new role
/// POST /api/v1/rbac/roles
pub async fn create_role(
    rbac_service: web::Data<RbacService>,
    request: web::Json<CreateRoleRequest>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let role_name = request.name.clone();

    info!(
        user_id = %user.user_id,
        role_name = %role_name,
        "Creating new role"
    );

    match rbac_service.create_role(request.into_inner()).await {
        Ok(role) => {
            info!(
                role_id = %role.id,
                role_name = %role.name,
                created_by = %user.user_id,
                "Role created successfully"
            );

            Ok(HttpResponse::Created().json(json!({
                "success": true,
                "message": "Role created successfully",
                "data": {
                    "role": {
                        "id": role.id,
                        "name": role.name,
                        "description": role.description,
                        "parent_role_id": role.parent_role_id,
                        "is_system_role": role.is_system_role,
                        "created_at": role.created_at,
                        "updated_at": role.updated_at
                    }
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                role_name = %role_name,
                user_id = %user.user_id,
                "Failed to create role"
            );

            match e {
                RbacError::RoleAlreadyExists(name) => {
                    Ok(HttpResponse::Conflict().json(json!({
                        "success": false,
                        "message": format!("Role '{}' already exists", name),
                        "error": "role_already_exists"
                    })))
                }
                RbacError::ValidationError(msg) => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Validation failed",
                        "error": "validation_error",
                        "details": msg
                    })))
                }
                RbacError::CircularDependency => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Circular dependency detected in role hierarchy",
                        "error": "circular_dependency"
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Create a new permission
/// POST /api/v1/rbac/permissions
pub async fn create_permission(
    rbac_service: web::Data<RbacService>,
    request: web::Json<CreatePermissionRequest>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let permission_name = request.name.clone();
    let resource = request.resource.clone();
    let action = request.action.clone();

    info!(
        user_id = %user.user_id,
        permission_name = %permission_name,
        resource = %resource,
        action = %action,
        "Creating new permission"
    );

    match rbac_service.create_permission(request.into_inner()).await {
        Ok(permission) => {
            info!(
                permission_id = %permission.id,
                permission_name = %permission.name,
                created_by = %user.user_id,
                "Permission created successfully"
            );

            Ok(HttpResponse::Created().json(json!({
                "success": true,
                "message": "Permission created successfully",
                "data": {
                    "permission": {
                        "id": permission.id,
                        "name": permission.name,
                        "description": permission.description,
                        "resource": permission.resource,
                        "action": permission.action,
                        "category": permission.category,
                        "scope": permission.scope,
                        "is_system_permission": permission.is_system_permission,
                        "created_at": permission.created_at
                    }
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                permission_name = %permission_name,
                user_id = %user.user_id,
                "Failed to create permission"
            );

            match e {
                RbacError::PermissionAlreadyExists(name) => {
                    Ok(HttpResponse::Conflict().json(json!({
                        "success": false,
                        "message": format!("Permission '{}' already exists", name),
                        "error": "permission_already_exists"
                    })))
                }
                RbacError::ValidationError(msg) => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Validation failed",
                        "error": "validation_error",
                        "details": msg
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Assign role to user
/// POST /api/v1/rbac/users/{user_id}/roles
pub async fn assign_role_to_user(
    rbac_service: web::Data<RbacService>,
    path: web::Path<Uuid>,
    request: web::Json<AssignRoleRequest>,
    user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let target_user_id = path.into_inner();
    let mut assign_request = request.into_inner();
    assign_request.user_id = target_user_id;
    assign_request.assigned_by = user.user_id;

    let role_id = assign_request.role_id;

    info!(
        user_id = %user.user_id,
        target_user_id = %target_user_id,
        role_id = %role_id,
        "Assigning role to user"
    );

    match rbac_service.assign_role_to_user(assign_request).await {
        Ok(_) => {
            info!(
                target_user_id = %target_user_id,
                role_id = %role_id,
                assigned_by = %user.user_id,
                "Role assigned to user successfully"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "Role assigned to user successfully",
                "data": {
                    "user_id": target_user_id,
                    "role_id": role_id,
                    "assigned_by": user.user_id
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                target_user_id = %target_user_id,
                role_id = %role_id,
                user_id = %user.user_id,
                "Failed to assign role to user"
            );

            match e {
                RbacError::UserNotFound => {
                    Ok(HttpResponse::NotFound().json(json!({
                        "success": false,
                        "message": "User not found",
                        "error": "user_not_found"
                    })))
                }
                RbacError::RoleNotFound(role_id) => {
                    Ok(HttpResponse::NotFound().json(json!({
                        "success": false,
                        "message": format!("Role '{}' not found", role_id),
                        "error": "role_not_found"
                    })))
                }
                RbacError::UserAlreadyHasRole(role_id) => {
                    Ok(HttpResponse::Conflict().json(json!({
                        "success": false,
                        "message": format!("User already has role '{}'", role_id),
                        "error": "user_already_has_role"
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Check user permission
/// POST /api/v1/rbac/check-permission
pub async fn check_permission(
    rbac_service: web::Data<RbacService>,
    request: web::Json<PermissionCheckRequest>,
    _user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let check_request = request.into_inner();
    let user_id = check_request.user_id;
    let resource = check_request.resource.clone();
    let action = check_request.action.clone();

    info!(
        user_id = %user_id,
        resource = %resource,
        action = %action,
        "Checking user permission"
    );

    match rbac_service.check_permission(check_request).await {
        Ok(response) => {
            info!(
                user_id = %user_id,
                resource = %resource,
                action = %action,
                allowed = response.allowed,
                "Permission check completed"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "Permission check completed",
                "data": {
                    "allowed": response.allowed,
                    "reason": response.reason,
                    "matched_permissions": response.matched_permissions,
                    "effective_roles": response.effective_roles
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                user_id = %user_id,
                resource = %resource,
                action = %action,
                "Permission check failed"
            );

            Ok(HttpResponse::InternalServerError().json(json!({
                "success": false,
                "message": "Permission check failed",
                "error": "internal_error"
            })))
        }
    }
}

/// Get user permissions
/// GET /api/v1/rbac/users/{user_id}/permissions
pub async fn get_user_permissions(
    rbac_service: web::Data<RbacService>,
    path: web::Path<Uuid>,
    _user: AuthenticatedUser,
) -> Result<HttpResponse, AppError> {
    let user_id = path.into_inner();

    info!(
        user_id = %user_id,
        "Getting user permissions"
    );

    match rbac_service.get_user_permissions(user_id).await {
        Ok(permissions) => {
            info!(
                user_id = %user_id,
                roles_count = permissions.roles.len(),
                permissions_count = permissions.permissions.len(),
                "User permissions retrieved successfully"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "User permissions retrieved successfully",
                "data": permissions
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                user_id = %user_id,
                "Failed to get user permissions"
            );

            match e {
                RbacError::UserNotFound => {
                    Ok(HttpResponse::NotFound().json(json!({
                        "success": false,
                        "message": "User not found",
                        "error": "user_not_found"
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Configure RBAC routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/rbac")
            .route("/roles", web::post().to(create_role))
            .route("/permissions", web::post().to(create_permission))
            .route("/users/{user_id}/roles", web::post().to(assign_role_to_user))
            .route("/check-permission", web::post().to(check_permission))
            .route("/users/{user_id}/permissions", web::get().to(get_user_permissions))
    );
}
