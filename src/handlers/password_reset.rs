use crate::middleware::auth::AuthenticatedUser;
use crate::services::password_reset_service::{
    PasswordResetService, PasswordResetRequest, PasswordResetConfirmRequest,
    PasswordResetError,
};
use crate::utils::errors::AppError;
use actix_web::{web, HttpRequest, HttpResponse, Result};
use serde_json::json;
use tracing::{error, info, warn};

/// Initiate password reset
/// POST /api/v1/auth/password-reset/initiate
pub async fn initiate_password_reset(
    password_reset_service: web::Data<PasswordResetService>,
    mut request: web::Json<PasswordResetRequest>,
    http_req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    // Extract client information
    let client_ip = http_req
        .connection_info()
        .realip_remote_addr()
        .map(|ip| ip.to_string());
    
    let user_agent = http_req
        .headers()
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    // Add client info to request
    request.client_ip = client_ip.clone();
    request.user_agent = user_agent.clone();

    let email = request.email.clone();

    info!(
        email = %email,
        client_ip = ?client_ip,
        "Password reset initiated"
    );

    match password_reset_service.initiate_password_reset(request.into_inner()).await {
        Ok(response) => {
            if response.success {
                info!(
                    email = %email,
                    reset_token_sent = response.reset_token_sent,
                    "Password reset request processed successfully"
                );
            } else {
                warn!(
                    email = %email,
                    rate_limit_info = ?response.rate_limit_info,
                    "Password reset request rate limited"
                );
            }

            Ok(HttpResponse::Ok().json(json!({
                "success": response.success,
                "message": response.message,
                "data": {
                    "reset_token_sent": response.reset_token_sent,
                    "rate_limit_info": response.rate_limit_info
                }
            })))
        }
        Err(e) => {
            error!(
                email = %email,
                error = %e,
                "Password reset initiation failed"
            );

            match e {
                PasswordResetError::RateLimitExceeded => {
                    Ok(HttpResponse::TooManyRequests().json(json!({
                        "success": false,
                        "message": "Too many password reset requests. Please try again later.",
                        "error": "rate_limit_exceeded"
                    })))
                }
                PasswordResetError::EmailServiceError(_) => {
                    Ok(HttpResponse::ServiceUnavailable().json(json!({
                        "success": false,
                        "message": "Email service temporarily unavailable. Please try again later.",
                        "error": "email_service_error"
                    })))
                }
                PasswordResetError::PasswordValidationFailed(msg) => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Invalid request data",
                        "error": "validation_failed",
                        "details": msg
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred. Please try again later.",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Confirm password reset with new password
/// POST /api/v1/auth/password-reset/confirm
pub async fn confirm_password_reset(
    password_reset_service: web::Data<PasswordResetService>,
    mut request: web::Json<PasswordResetConfirmRequest>,
    http_req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    // Extract client information
    let client_ip = http_req
        .connection_info()
        .realip_remote_addr()
        .map(|ip| ip.to_string());
    
    let user_agent = http_req
        .headers()
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    // Add client info to request
    request.client_ip = client_ip.clone();
    request.user_agent = user_agent.clone();

    info!(
        client_ip = ?client_ip,
        "Password reset confirmation attempted"
    );

    match password_reset_service.confirm_password_reset(request.into_inner()).await {
        Ok(response) => {
            info!(
                password_changed = response.password_changed,
                "Password reset completed successfully"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": response.success,
                "message": response.message,
                "data": {
                    "password_changed": response.password_changed
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                "Password reset confirmation failed"
            );

            match e {
                PasswordResetError::InvalidToken => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Invalid or malformed reset token.",
                        "error": "invalid_token"
                    })))
                }
                PasswordResetError::TokenExpired => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Reset token has expired. Please request a new password reset.",
                        "error": "token_expired"
                    })))
                }
                PasswordResetError::TokenAlreadyUsed => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Reset token has already been used. Please request a new password reset.",
                        "error": "token_already_used"
                    })))
                }
                PasswordResetError::PasswordValidationFailed(msg) => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Password validation failed",
                        "error": "password_validation_failed",
                        "details": msg
                    })))
                }
                PasswordResetError::UserNotFound => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "User associated with this token was not found.",
                        "error": "user_not_found"
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred. Please try again later.",
                        "error": "internal_error"
                    })))
                }
            }
        }
    }
}

/// Validate password reset token (without consuming it)
/// GET /api/v1/auth/password-reset/validate/{token}
pub async fn validate_password_reset_token(
    password_reset_service: web::Data<PasswordResetService>,
    path: web::Path<String>,
) -> Result<HttpResponse, AppError> {
    let token = path.into_inner();

    info!("Password reset token validation requested");

    match password_reset_service.validate_reset_token(&token).await {
        Ok(reset_token) => {
            info!(
                user_id = %reset_token.user_id,
                expires_at = %reset_token.expires_at,
                "Password reset token validated successfully"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "Token is valid",
                "data": {
                    "valid": true,
                    "expires_at": reset_token.expires_at,
                    "email": reset_token.email
                }
            })))
        }
        Err(e) => {
            warn!(
                error = %e,
                "Password reset token validation failed"
            );

            match e {
                PasswordResetError::InvalidToken => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Invalid reset token",
                        "data": {
                            "valid": false,
                            "reason": "invalid_token"
                        }
                    })))
                }
                PasswordResetError::TokenExpired => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Reset token has expired",
                        "data": {
                            "valid": false,
                            "reason": "token_expired"
                        }
                    })))
                }
                PasswordResetError::TokenAlreadyUsed => {
                    Ok(HttpResponse::BadRequest().json(json!({
                        "success": false,
                        "message": "Reset token has already been used",
                        "data": {
                            "valid": false,
                            "reason": "token_already_used"
                        }
                    })))
                }
                _ => {
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "success": false,
                        "message": "An internal error occurred",
                        "data": {
                            "valid": false,
                            "reason": "internal_error"
                        }
                    })))
                }
            }
        }
    }
}

/// Admin endpoint to clean up expired tokens
/// POST /api/v1/auth/password-reset/cleanup
pub async fn cleanup_expired_tokens(
    password_reset_service: web::Data<PasswordResetService>,
    _user: AuthenticatedUser, // Requires admin authentication
) -> Result<HttpResponse, AppError> {
    info!("Password reset token cleanup initiated");

    match password_reset_service.cleanup_expired_tokens().await {
        Ok(cleaned_count) => {
            info!(
                cleaned_count = cleaned_count,
                "Password reset token cleanup completed"
            );

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "Token cleanup completed",
                "data": {
                    "cleaned_count": cleaned_count
                }
            })))
        }
        Err(e) => {
            error!(
                error = %e,
                "Password reset token cleanup failed"
            );

            Ok(HttpResponse::InternalServerError().json(json!({
                "success": false,
                "message": "Token cleanup failed",
                "error": "cleanup_failed"
            })))
        }
    }
}

/// Configure password reset routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/auth/password-reset")
            .route("/initiate", web::post().to(initiate_password_reset))
            .route("/confirm", web::post().to(confirm_password_reset))
            .route("/validate/{token}", web::get().to(validate_password_reset_token))
            .route("/cleanup", web::post().to(cleanup_expired_tokens))
    );
}
