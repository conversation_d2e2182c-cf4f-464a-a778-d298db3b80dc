use crate::models::{CreateSessionRequest, SessionResponse, LogoutRequest};
use crate::services::{SessionService, SessionError};
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{error, info, warn};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
/// Response struct for session creation
/// 
/// Contains information about the success of the operation,
/// and optionally includes the created session details and tokens
pub struct CreateSessionResponse {
    pub success: bool,
    pub message: String,
    pub session: Option<SessionResponse>,
    pub session_token: Option<String>,
    pub refresh_token: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
/// Request struct for refreshing a session token
/// 
/// Contains the refresh token required to obtain a new session token
pub struct RefreshSessionRequest {
    pub refresh_token: String,
}

#[derive(Debug, Serialize, Deserialize)]
/// Response struct for session refresh operation
/// 
/// Contains the new session and refresh tokens if successful
pub struct RefreshSessionResponse {
    pub success: bool,
    pub message: String,
    pub session_token: Option<String>,
    pub refresh_token: Option<String>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
/// Request struct for session validation
/// 
/// Contains the session token to be validated
pub struct ValidateSessionRequest {
    pub session_token: String,
}

#[derive(Debug, Serialize, Deserialize)]
/// Response struct for session validation
/// 
/// Includes information about the session's validity and MFA status
pub struct ValidateSessionResponse {
    pub valid: bool,
    pub session: Option<SessionResponse>,
    pub requires_mfa: bool,
    pub mfa_verified: bool,
}

#[derive(Debug, Serialize, Deserialize)]
/// Response struct for listing user sessions
/// 
/// Contains a list of active sessions and the total count
pub struct SessionListResponse {
    pub sessions: Vec<SessionResponse>,
    pub total_count: usize,
}

#[derive(Debug, Serialize, Deserialize)]
/// Response struct for logout operations
/// 
/// Includes information about the success of the logout operation
/// and the number of sessions that were invalidated
pub struct LogoutResponse {
    pub success: bool,
    pub message: String,
    pub sessions_invalidated: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
/// Response struct containing session metrics
/// 
/// Provides statistical information about active sessions and cleanup status
pub struct SessionMetricsResponse {
    pub total_active_sessions: u64,
    pub user_session_count: u32,
    pub device_session_count: u32,
    pub last_cleanup_at: chrono::DateTime<chrono::Utc>,
}

/// Creates a new session for a user
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `request` - The session creation request containing user details
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Success response with session details or error response
/// 
/// # Errors
/// * Returns `409 Conflict` if maximum concurrent sessions are exceeded
/// * Returns `400 Bad Request` for validation errors
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn create_session(
    session_service: web::Data<SessionService>,
    request: web::Json<CreateSessionRequest>,
) -> ActixResult<HttpResponse> {
    let user_id = request.user_id;
    info!("Creating session for user {}", user_id);

    match session_service.create_session(request.into_inner()).await {
        Ok(session) => {
            let response = CreateSessionResponse {
                success: true,
                message: "Session created successfully".to_string(),
                session_token: Some(session.session_token.clone()),
                refresh_token: Some(session.refresh_token.clone()),
                session: Some(SessionResponse::from(session)),
            };

            info!("Session created successfully");
            Ok(HttpResponse::Created().json(response))
        }
        Err(SessionError::MaxSessionsExceeded) => {
            warn!("Maximum concurrent sessions exceeded for user {}", user_id);
            let response = CreateSessionResponse {
                success: false,
                message: "Maximum concurrent sessions exceeded".to_string(),
                session_token: None,
                refresh_token: None,
                session: None,
            };
            Ok(HttpResponse::Conflict().json(response))
        }
        Err(SessionError::ValidationError(msg)) => {
            warn!("Session creation validation failed: {}", msg);
            let response = CreateSessionResponse {
                success: false,
                message: format!("Validation error: {msg}"),
                session_token: None,
                refresh_token: None,
                session: None,
            };
            Ok(HttpResponse::BadRequest().json(response))
        }
        Err(e) => {
            error!("Failed to create session: {}", e);
            let response = CreateSessionResponse {
                success: false,
                message: "Internal server error".to_string(),
                session_token: None,
                refresh_token: None,
                session: None,
            };
            Ok(HttpResponse::InternalServerError().json(response))
        }
    }
}

/// Validates an existing session token
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `request` - The validation request containing the session token
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Response containing session validity status
/// 
/// # Errors
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn validate_session(
    session_service: web::Data<SessionService>,
    request: web::Json<ValidateSessionRequest>,
) -> ActixResult<HttpResponse> {
    match session_service.validate_session(&request.session_token).await {
        Ok(session) => {
            let response = ValidateSessionResponse {
                valid: true,
                session: Some(SessionResponse::from(session.clone())),
                requires_mfa: session.requires_mfa,
                mfa_verified: session.mfa_verified_at.is_some(),
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(SessionError::SessionNotFound | SessionError::SessionExpired) => {
            let response = ValidateSessionResponse {
                valid: false,
                session: None,
                requires_mfa: false,
                mfa_verified: false,
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to validate session: {}", e);
            let response = ValidateSessionResponse {
                valid: false,
                session: None,
                requires_mfa: false,
                mfa_verified: false,
            };
            Ok(HttpResponse::InternalServerError().json(response))
        }
    }
}

/// Refreshes an existing session using a refresh token
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `request` - The refresh request containing the refresh token
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - New session tokens or error response
/// 
/// # Errors
/// * Returns `401 Unauthorized` for invalid or expired refresh tokens
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn refresh_session(
    session_service: web::Data<SessionService>,
    request: web::Json<RefreshSessionRequest>,
) -> ActixResult<HttpResponse> {
    match session_service.refresh_session(&request.refresh_token).await {
        Ok(session) => {
            let response = RefreshSessionResponse {
                success: true,
                message: "Session refreshed successfully".to_string(),
                session_token: Some(session.session_token),
                refresh_token: Some(session.refresh_token),
                expires_at: Some(session.expires_at),
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(SessionError::SessionNotFound) => {
            let response = RefreshSessionResponse {
                success: false,
                message: "Invalid refresh token".to_string(),
                session_token: None,
                refresh_token: None,
                expires_at: None,
            };
            Ok(HttpResponse::Unauthorized().json(response))
        }
        Err(SessionError::SessionExpired) => {
            let response = RefreshSessionResponse {
                success: false,
                message: "Refresh token expired".to_string(),
                session_token: None,
                refresh_token: None,
                expires_at: None,
            };
            Ok(HttpResponse::Unauthorized().json(response))
        }
        Err(e) => {
            error!("Failed to refresh session: {}", e);
            let response = RefreshSessionResponse {
                success: false,
                message: "Internal server error".to_string(),
                session_token: None,
                refresh_token: None,
                expires_at: None,
            };
            Ok(HttpResponse::InternalServerError().json(response))
        }
    }
}

/// Retrieves all active sessions for a specific user
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `path` - The user ID path parameter
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - List of user's active sessions
/// 
/// # Errors
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn get_user_sessions(
    session_service: web::Data<SessionService>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let user_id = path.into_inner();

    match session_service.get_active_user_sessions(user_id).await {
        Ok(sessions) => {
            let response = SessionListResponse {
                total_count: sessions.len(),
                sessions,
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to get user sessions: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to retrieve sessions"
            })))
        }
    }
}

/// Invalidates a specific session
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `request` - The logout request containing session details
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Success or error response
/// 
/// # Errors
/// * Returns `400 Bad Request` if session ID is missing
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn logout(
    session_service: web::Data<SessionService>,
    request: web::Json<LogoutRequest>,
) -> ActixResult<HttpResponse> {
    if let Some(session_id) = request.session_id {
        // Logout specific session
        match session_service.invalidate_session_by_id(session_id, Some("user_logout".to_string())).await {
            Ok(()) => {
                let response = LogoutResponse {
                    success: true,
                    message: "Session invalidated successfully".to_string(),
                    sessions_invalidated: Some(1),
                };
                Ok(HttpResponse::Ok().json(response))
            }
            Err(e) => {
                error!("Failed to invalidate session: {}", e);
                let response = LogoutResponse {
                    success: false,
                    message: "Failed to invalidate session".to_string(),
                    sessions_invalidated: None,
                };
                Ok(HttpResponse::InternalServerError().json(response))
            }
        }
    } else {
        // This would require user_id from authentication context
        // For now, return an error
        let response = LogoutResponse {
            success: false,
            message: "Session ID required".to_string(),
            sessions_invalidated: None,
        };
        Ok(HttpResponse::BadRequest().json(response))
    }
}

/// Invalidates all active sessions for a specific user
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `path` - The user ID path parameter
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Success response with count of invalidated sessions
/// 
/// # Errors
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn logout_all_sessions(
    session_service: web::Data<SessionService>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let user_id = path.into_inner();

    match session_service.invalidate_all_user_sessions(user_id, Some("user_logout_all".to_string())).await {
        Ok(count) => {
            let response = LogoutResponse {
                success: true,
                message: format!("Invalidated {count} sessions"),
                sessions_invalidated: Some(count),
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to invalidate all user sessions: {}", e);
            let response = LogoutResponse {
                success: false,
                message: "Failed to invalidate sessions".to_string(),
                sessions_invalidated: None,
            };
            Ok(HttpResponse::InternalServerError().json(response))
        }
    }
}

/// Records successful MFA verification for a session
/// 
/// # Arguments
/// * `session_service` - The session service instance
/// * `path` - The session ID path parameter
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Success or error response
/// 
/// # Errors
/// * Returns `500 Internal Server Error` if MFA verification fails or for database errors
pub async fn mark_mfa_verified(
    session_service: web::Data<SessionService>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let session_id = path.into_inner();

    match session_service.mark_mfa_verified(session_id).await {
        Ok(()) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": "MFA verification recorded"
            })))
        }
        Err(e) => {
            error!("Failed to mark MFA as verified: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "message": "Failed to record MFA verification"
            })))
        }
    }
}

/// Retrieves session metrics for administrative purposes
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Session statistics and metrics
/// 
/// # Errors
/// * Returns `500 Internal Server Error` for database or internal errors
pub async fn get_session_metrics(
    session_service: web::Data<SessionService>,
) -> ActixResult<HttpResponse> {
    match session_service.get_session_metrics().await {
        Ok(metrics) => {
            let response = SessionMetricsResponse {
                total_active_sessions: metrics.total_active_sessions,
                user_session_count: metrics.sessions_by_user.len().try_into().unwrap_or(u32::MAX),
                device_session_count: metrics.sessions_by_device.len().try_into().unwrap_or(u32::MAX),
                last_cleanup_at: metrics.last_cleanup_at,
            };
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            error!("Failed to get session metrics: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to retrieve session metrics"
            })))
        }
    }
}

/// Removes expired sessions from the system
/// 
/// # Returns
/// * `ActixResult<HttpResponse>` - Cleanup operation results and metrics
/// 
/// # Errors
/// * Returns `500 Internal Server Error` if cleanup fails or for database errors
pub async fn cleanup_expired_sessions(
    session_service: web::Data<SessionService>,
) -> ActixResult<HttpResponse> {
    match session_service.cleanup_expired_sessions().await {
        Ok(metrics) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": "Cleanup completed",
                "metrics": {
                    "total_active_sessions": metrics.total_active_sessions,
                    "last_cleanup_at": metrics.last_cleanup_at
                }
            })))
        }
        Err(e) => {
            error!("Failed to cleanup expired sessions: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "message": "Cleanup failed"
            })))
        }
    }
}

/// Configures and registers all session-related routes
/// 
/// # Arguments
/// * `cfg` - Service configuration instance to register routes with
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/sessions")
            .route("", web::post().to(create_session))
            .route("/validate", web::post().to(validate_session))
            .route("/refresh", web::post().to(refresh_session))
            .route("/logout", web::post().to(logout))
            .route("/users/{user_id}", web::get().to(get_user_sessions))
            .route("/users/{user_id}/logout-all", web::post().to(logout_all_sessions))
            .route("/{session_id}/mfa-verified", web::post().to(mark_mfa_verified))
            .route("/metrics", web::get().to(get_session_metrics))
            .route("/cleanup", web::post().to(cleanup_expired_sessions))
    );
}
