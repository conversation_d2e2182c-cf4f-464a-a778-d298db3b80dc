mod database;

use actix_web::{web, App, HttpServer, middleware::Logger};
use tracing_actix_web::TracingLogger;

use crabshield::{Config, create_pool};
use crabshield::services::{SessionService, EmailVerificationService, AuthService, PasswordService, LockoutService, EmailService, MfaService, JwtService};
use crabshield::middleware::{cors::create_cors, security_headers::create_security_headers, rate_limiting::create_auth_rate_limiting};
use crabshield::handlers;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Load configuration
    let config = Config::from_env().expect("Failed to load configuration");

    // Create database pool
    let pool = create_pool(&config.database_url)
        .await
        .expect("Failed to create database pool");

    // Initialize services
    let password_service = PasswordService::new();
    let lockout_service = LockoutService::new(&config.redis_url, pool.clone())
        .expect("Failed to initialize lockout service");

    // Initialize MFA and JWT services
    let mfa_service = MfaService::new(pool.clone(), "CrabShield".to_string());
    let jwt_service = JwtService::new(
        &config.jwt_secret,
        "crabshield-auth".to_string(),
        "crabshield-api".to_string(),
    );

    let auth_service = AuthService::new(pool.clone(), password_service, lockout_service, mfa_service, jwt_service);
    let session_service = SessionService::new(pool.clone(), config.redis_url.clone());
    let email_service = EmailService::new(config.email.clone())
        .expect("Failed to initialize email service");
    let email_verification_service = EmailVerificationService::new(pool.clone(), email_service);

    // Clone config for the bind call
    let bind_address = format!("{}:{}", config.host, config.port);
    let environment = std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string());

    // Start HTTP server
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(pool.clone()))
            .app_data(web::Data::new(config.clone()))
            .app_data(web::Data::new(auth_service.clone()))
            .app_data(web::Data::new(session_service.clone()))
            .app_data(web::Data::new(email_verification_service.clone()))
            .wrap(create_cors(&environment))
            .wrap(TracingLogger::default())
            .wrap(Logger::default())
            .service(
                web::scope("/api/v1")
                    .configure(handlers::configure_routes)
            )
            .service(handlers::health::health_check)
    })
    .bind(bind_address)?
    .run()
    .await
}
