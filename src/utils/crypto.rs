use rand::{Rng, rng};
use rand::distr::Alphanumeric;

/// Generate a secure random token of specified length
pub fn generate_secure_token(length: usize) -> String {
    rng()
        .sample_iter(&Alphanumeric)
        .take(length)
        .map(char::from)
        .collect()
}

/// Generate a secure random token with custom character set
pub fn generate_secure_token_with_charset(length: usize, charset: &[u8]) -> String {
    let mut rng = thread_rng();
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..charset.len());
            charset[idx] as char
        })
        .collect()
}

/// Mask sensitive data for safe logging
///
/// This function masks sensitive information like emails, usernames, etc.
/// while preserving enough information for debugging and audit purposes.
///
/// # Arguments
/// * `data` - The sensitive data to mask
/// * `visible_chars` - Number of characters to keep visible at the start
///
/// # Returns
/// * `String` - The masked data
///
/// # Examples
/// * `mask_sensitive_data("<EMAIL>", 3)` -> `"use***@***.com"`
/// * `mask_sensitive_data("username123", 4)` -> `"user***"`
pub fn mask_sensitive_data(data: &str, visible_chars: usize) -> String {
    if data.is_empty() {
        return "***".to_string();
    }

    if data.len() <= visible_chars {
        return "*".repeat(data.len());
    }

    // Special handling for email addresses
    if data.contains('@') {
        let parts: Vec<&str> = data.split('@').collect();
        if parts.len() == 2 {
            let local = parts[0];
            let domain = parts[1];

            let masked_local = if local.len() <= visible_chars {
                "*".repeat(local.len())
            } else {
                format!("{}***", &local[..visible_chars])
            };

            let masked_domain = if domain.contains('.') {
                let domain_parts: Vec<&str> = domain.split('.').collect();
                let masked_parts: Vec<String> = domain_parts.iter().map(|part| {
                    if part.len() <= 2 {
                        part.to_string()
                    } else {
                        format!("{}***", &part[..1])
                    }
                }).collect();
                masked_parts.join(".")
            } else {
                format!("{}***", &domain[..1.min(domain.len())])
            };

            return format!("{}@{}", masked_local, masked_domain);
        }
    }

    // General masking for non-email data
    format!("{}***", &data[..visible_chars])
}

/// Generate a secure API key with prefix
pub fn generate_api_key(prefix: &str, length: usize) -> String {
    let token = generate_secure_token(length);
    format!("{}_{}", prefix, token)
}

/// Generate a secure client ID
pub fn generate_client_id(client_type: &str) -> String {
    let timestamp = chrono::Utc::now().timestamp();
    let random_part = generate_secure_token(8);
    format!("{}_{:x}_{}", client_type, timestamp, random_part)
}

/// Generate a secure client secret
pub fn generate_client_secret() -> String {
    generate_secure_token(64)
}

/// Generate a secure session token
pub fn generate_session_token() -> String {
    generate_secure_token(32)
}

/// Generate a secure refresh token
pub fn generate_refresh_token() -> String {
    generate_secure_token(48)
}

/// Generate a secure verification code (numeric)
pub fn generate_verification_code(length: usize) -> String {
    let charset = b"0123456789";
    generate_secure_token_with_charset(length, charset)
}

/// Generate a secure backup code
pub fn generate_backup_code() -> String {
    let charset = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    generate_secure_token_with_charset(8, charset)
}

/// Generate multiple backup codes
pub fn generate_backup_codes(count: usize) -> Vec<String> {
    (0..count).map(|_| generate_backup_code()).collect()
}



/// Generate a secure tenant ID
pub fn generate_tenant_id() -> String {
    let timestamp = chrono::Utc::now().timestamp();
    let random_part = generate_secure_token(12);
    format!("tenant_{:x}_{}", timestamp, random_part)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_secure_token() {
        let token = generate_secure_token(32);
        assert_eq!(token.len(), 32);
        assert!(token.chars().all(|c| c.is_alphanumeric()));
    }

    #[test]
    fn test_generate_secure_token_with_charset() {
        let charset = b"ABC123";
        let token = generate_secure_token_with_charset(10, charset);
        assert_eq!(token.len(), 10);
        assert!(token.chars().all(|c| "ABC123".contains(c)));
    }

    #[test]
    fn test_generate_api_key() {
        let api_key = generate_api_key("test", 16);
        assert!(api_key.starts_with("test_"));
        assert_eq!(api_key.len(), 5 + 16); // "test_" + 16 chars
    }

    #[test]
    fn test_generate_client_id() {
        let client_id = generate_client_id("web");
        assert!(client_id.starts_with("web_"));
        let parts: Vec<&str> = client_id.split('_').collect();
        assert_eq!(parts.len(), 3);
        assert_eq!(parts[0], "web");
    }

    #[test]
    fn test_generate_verification_code() {
        let code = generate_verification_code(6);
        assert_eq!(code.len(), 6);
        assert!(code.chars().all(|c| c.is_ascii_digit()));
    }

    #[test]
    fn test_generate_backup_codes() {
        let codes = generate_backup_codes(10);
        assert_eq!(codes.len(), 10);
        for code in codes {
            assert_eq!(code.len(), 8);
            assert!(code.chars().all(|c| c.is_alphanumeric() && c.is_ascii_uppercase()));
        }
    }

    #[test]
    fn test_mask_sensitive_data() {
        let data = "sensitive_data_123";
        let masked = mask_sensitive_data(data, 4);
        assert_eq!(masked, "sens**************");
        
        let short_data = "abc";
        let masked_short = mask_sensitive_data(short_data, 4);
        assert_eq!(masked_short, "***");
    }

    #[test]
    fn test_tokens_are_unique() {
        let token1 = generate_secure_token(32);
        let token2 = generate_secure_token(32);
        assert_ne!(token1, token2);
    }
}
