# CrabShield Auth Service - Standalone Deployment

This guide covers deploying the CrabShield Auth Service as a standalone, independent service on a VPS or dedicated server.

## 🎯 Overview

The standalone deployment allows the auth service to run completely independently with:
- ✅ Multi-tenant support with Row-Level Security (RLS)
- ✅ Multi-app client management
- ✅ Dedicated PostgreSQL database
- ✅ Redis for session storage and caching
- ✅ Docker containerization
- ✅ SSL/HTTPS support
- ✅ Automated deployment scripts
- ✅ Health monitoring and logging

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- curl
- openssl (for SSL certificates)

### Network Requirements
- Port 80 (HTTP)
- Port 443 (HTTPS)
- Port 8080 (Auth Service - can be internal)

## 🚀 Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd CrabShield/services/auth-service

# Copy environment template
cp .env.standalone.example .env.standalone
```

### 2. Configure Environment

Edit `.env.standalone` with your settings:

```bash
# Required: Set secure passwords
POSTGRES_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password

# Required: Set secure secrets (minimum 32 characters)
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Required: Email configuration
RESEND_API_KEY=re_your_resend_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>

# Required: Client configuration
CrabShield_WEB_CLIENT_ID=your_web_client_id
CrabShield_WEB_CLIENT_SECRET=your_web_client_secret
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
REDIRECT_URIS=https://yourdomain.com/auth/callback
```

### 3. Deploy

```bash
# Run the deployment script
./scripts/deploy.sh
```

The script will:
- ✅ Check prerequisites
- ✅ Validate configuration
- ✅ Build Docker images
- ✅ Start all services
- ✅ Wait for health checks
- ✅ Show deployment status

## 🔧 Manual Deployment

If you prefer manual deployment:

```bash
# Build and start services
docker-compose -f docker-compose.yml --env-file .env.standalone up -d

# Check status
docker-compose -f docker-compose.yml --env-file .env.standalone ps

# View logs
docker-compose -f docker-compose.yml --env-file .env.standalone logs -f
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  Auth Service   │    │   PostgreSQL    │
│   (SSL Term.)   │◄──►│   (Rust/Actix)  │◄──►│   (Database)    │
│   Port 80/443   │    │   Port 8080     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │   (Sessions)    │
                       │   Port 6379     │
                       └─────────────────┘
```

## 🔐 Security Features

### Multi-Tenant Isolation
- Row-Level Security (RLS) policies
- Tenant-specific data isolation
- Client-based access control

### Authentication & Authorization
- JWT tokens with secure secrets
- Argon2 password hashing
- MFA support (TOTP, SMS)
- OAuth integration (Google, GitHub, Microsoft)

### Network Security
- HTTPS/SSL termination
- CORS protection
- Rate limiting
- Security headers

## 📊 Monitoring & Health Checks

### Health Endpoints
- `GET /health` - Overall service health
- `GET /api/v1/tenants/health` - Tenant service health
- `GET /api/v1/clients/health` - Client service health

### Monitoring
```bash
# Check service status
curl http://localhost:8080/health

# View logs
docker-compose logs -f auth-service

# Monitor resource usage
docker stats
```

## 🔄 Management Commands

### Service Management
```bash
# Stop services
./scripts/deploy.sh stop

# Restart services
./scripts/deploy.sh restart

# View logs
./scripts/deploy.sh logs

# Check status
./scripts/deploy.sh status
```

### Database Management
```bash
# Connect to database
docker-compose exec auth-postgres psql -U auth_user -d crabshield_auth

# Backup database
docker-compose exec auth-postgres pg_dump -U auth_user crabshield_auth > backup.sql

# Restore database
docker-compose exec -T auth-postgres psql -U auth_user -d crabshield_auth < backup.sql
```

## 🌐 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh token

### Multi-App Management
- `GET /api/v1/tenants` - List tenants
- `POST /api/v1/tenants` - Create tenant
- `GET /api/v1/clients` - List clients
- `POST /api/v1/clients` - Create client

### OAuth
- `GET /auth/oauth/{provider}` - Initiate OAuth flow
- `GET /auth/oauth/{provider}/callback` - OAuth callback

## 🔧 Configuration Reference

### Environment Variables

| Variable             | Required | Description                    |
|----------------------|----------|--------------------------------|
| `POSTGRES_PASSWORD`  | ✅        | PostgreSQL password            |
| `REDIS_PASSWORD`     | ✅        | Redis password                 |
| `JWT_SECRET`         | ✅        | JWT signing secret (32+ chars) |
| `ENCRYPTION_KEY`     | ✅        | Encryption key (32 chars)      |
| `RESEND_API_KEY`     | ✅        | Resend email API key           |
| `EMAIL_FROM_ADDRESS` | ✅        | From email address             |
| `ALLOWED_ORIGINS`    | ✅        | CORS allowed origins           |
| `OAUTH_ENABLED`      | ❌        | Enable OAuth (default: false)  |

### Client Configuration
Each client application needs:
- Unique `client_id` and `client_secret`
- Allowed origins for CORS
- Redirect URIs for OAuth
- Feature flags and permissions

## 🚨 Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs auth-service

# Check environment
docker-compose config
```

**Database connection issues:**
```bash
# Check database health
docker-compose exec auth-postgres pg_isready -U auth_user

# Check connection from service
docker-compose exec auth-service curl http://localhost:8080/health
```

**SSL/HTTPS issues:**
```bash
# Generate self-signed certificates for testing
openssl req -x509 -newkey rsa:4096 -keyout docker/ssl/key.pem -out docker/ssl/cert.pem -days 365 -nodes
```

### Performance Tuning

**Database:**
- Adjust `DATABASE_MAX_CONNECTIONS` based on load
- Monitor connection pool usage
- Consider read replicas for high traffic

**Redis:**
- Adjust `REDIS_MAX_CONNECTIONS`
- Monitor memory usage
- Configure persistence settings

## 📈 Scaling

### Horizontal Scaling
- Deploy multiple auth service instances
- Use load balancer (nginx, HAProxy)
- Shared database and Redis

### Vertical Scaling
- Increase container resources
- Optimize database configuration
- Tune connection pools

## 🔄 Updates & Maintenance

### Updating the Service
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose build auth-service
docker-compose up -d auth-service
```

### Database Migrations
```bash
# Migrations are handled automatically on startup
# Check logs for migration status
docker-compose logs auth-service | grep migration
```

## 📞 Support

For issues and questions:
1. Check the logs: `docker-compose logs -f`
2. Verify configuration: `docker-compose config`
3. Test health endpoints: `curl http://localhost:8080/health`
4. Review this documentation
5. Check the main project documentation

## 📦 Extracting to Independent Project

To move the auth service from this monorepo to its own independent project:

### **Step 1: Create New Repository**

```bash
# Create new directory for the standalone auth service
mkdir crabshield-auth-service
cd crabshield-auth-service

# Initialize new git repository
git init
```

### **Step 2: Copy Required Files**

Copy these files and directories from `CrabShield/services/auth-service/`:

```bash
# Core application files
cp -r CrabShield/services/auth-service/src/ ./src/
cp -r CrabShield/services/auth-service/migrations/ ./migrations/
cp CrabShield/services/auth-service/Cargo.toml ./
cp CrabShield/services/auth-service/Cargo.lock ./

# Standalone deployment files
cp -r CrabShield/services/auth-service/docker/ ./docker/
cp -r CrabShield/services/auth-service/scripts/ ./scripts/
cp CrabShield/services/auth-service/Dockerfile ./Dockerfile
cp CrabShield/services/auth-service/docker-compose.yml ./docker-compose.yml
cp CrabShield/services/auth-service/.env.standalone.example ./.env.example

# Documentation
cp CrabShield/services/auth-service/README-STANDALONE.md ./README.md

# Optional: Development files
cp CrabShield/services/auth-service/.gitignore ./
```

### **Step 3: Update Cargo.toml**

Edit `Cargo.toml` to remove workspace dependencies:

```toml
[package]
name = "crabshield-auth-service"
version = "0.1.0"
edition = "2021"

[dependencies]
# Replace workspace dependencies with specific versions
actix-web = "4.11"
actix-cors = "0.7"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
config = "0.14"
dotenvy = "0.15"
argon2 = "0.5"
jsonwebtoken = "9.0"
validator = { version = "0.18", features = ["derive"] }
thiserror = "1.0"
anyhow = "1.0"
redis = { version = "0.26", features = ["tokio-comp"] }
reqwest = { version = "0.12", features = ["json"] }
oauth2 = "4.4"
resend-rs = "0.7"
totp-rs = "5.4"
qrcode = "0.14"
base64 = "0.22"
rand = { version = "0.8", features = ["std"] }
futures-util = "0.3"
```

### **Step 4: Update File Paths**

Update any hardcoded paths in the code:

1. **Update `main.rs`** (if it references workspace paths)
2. **Update `docker-compose.yml`** build context:
   ```yaml
   build:
     context: .
     dockerfile: Dockerfile
   ```
3. **Update deployment script** paths in `scripts/deploy-standalone.sh`

### **Step 5: Create Project Structure**

Your final project structure should look like:

```
crabshield-auth-service/
├── src/
│   ├── main.rs
│   ├── lib.rs
│   ├── config/
│   ├── models/
│   ├── services/
│   ├── handlers/
│   ├── middleware/
│   ├── utils/
│   └── database/
├── migrations/
│   ├── 001_initial_schema.sql
│   ├── 002_add_mfa_support.sql
│   ├── ...
│   ├── 007_create_multi_app_tables.sql
│   └── 008_create_rls_policies.sql
├── docker/
│   ├── standalone-entrypoint.sh
│   ├── postgres-init.sql
│   └── nginx.conf (optional)
├── scripts/
│   └── deploy-standalone.sh
├── Cargo.toml
├── Cargo.lock
├── Dockerfile
├── docker-compose.yml
├── .env.example
├── .gitignore
└── README.md
```

### **Step 6: Test Independent Build**

```bash
# Test compilation
cargo check
cargo build --release

# Test Docker build
docker build -t crabshield-auth-service .

# Test deployment
cp .env.example .env
# Edit .env with your configuration
./scripts/deploy.sh
```

### **Step 7: Update Documentation**

1. **Update README.md** to reflect the new project name and structure
2. **Update API documentation** if you have any
3. **Create CHANGELOG.md** for version tracking
4. **Add LICENSE** file if needed

### **Step 8: Initialize Git Repository**

```bash
# Add all files
git add .

# Create initial commit
git commit -m "Initial commit: CrabShield Auth Service standalone"

# Add remote repository (replace with your repo URL)
git remote add origin https://github.com/yourusername/crabshield-auth-service.git
git push -u origin main
```

### **Step 9: Optional Enhancements**

Consider adding these for a complete standalone project:

1. **CI/CD Pipeline** (`.github/workflows/` or `.gitlab-ci.yml`)
2. **Development Docker Compose** (`docker-compose.dev.yml`)
3. **API Documentation** (OpenAPI/Swagger)
4. **Helm Charts** (for Kubernetes deployment)
5. **Monitoring Configuration** (Prometheus, Grafana)
6. **Backup Scripts** for database
7. **Load Testing Scripts**

### **Step 10: Verify Independence**

Ensure the service works completely independently:

```bash
# Test all endpoints
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/tenants/health
curl http://localhost:8080/api/v1/clients/health

# Test tenant creation
curl -X POST http://localhost:8080/api/v1/tenants \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Tenant", "domain": "test.example.com"}'

# Test client creation
curl -X POST http://localhost:8080/api/v1/clients \
  -H "Content-Type: application/json" \
  -d '{
    "client_name": "Test App",
    "client_type": "web",
    "tenant_id": "TENANT_ID_FROM_ABOVE",
    "allowed_origins": ["https://test.example.com"],
    "redirect_uris": ["https://test.example.com/auth/callback"],
    "oauth_providers": ["google", "github"]
  }'
```

### **Files NOT to Copy**

Don't copy these monorepo-specific files:
- `nx.json`
- `workspace.json`
- `package.json` (if it exists in the service directory)
- Any workspace configuration files
- Parent directory `.gitignore` (create service-specific one)

## 🔒 Production Checklist

Before going to production:

- [ ] Set secure passwords for all services
- [ ] Configure proper SSL certificates
- [ ] Set up monitoring and alerting
- [ ] Configure automated backups
- [ ] Review and test disaster recovery
- [ ] Set up log aggregation
- [ ] Configure firewall rules
- [ ] Test all authentication flows
- [ ] Verify multi-tenant isolation
- [ ] Load test the service
