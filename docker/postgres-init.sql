-- PostgreSQL initialization script for CrabShield Auth Service
-- This script sets up the database for standalone deployment

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create auth service role for RLS
DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'auth_service_role') THEN
            CREATE ROLE auth_service_role;
        END IF;
    END
$$;

-- Grant necessary permissions to auth_service_role
GRANT CONNECT ON DATABASE crabshield_auth TO auth_service_role;
GRANT USAGE ON SCHEMA public TO auth_service_role;
GRANT CREATE ON SCHEMA public TO auth_service_role;

-- Grant permissions to the main auth_user
GRANT auth_service_role TO auth_user;

-- Create function to set tenant context (for RLS)
-- Updated for PostgreSQL 17 search path safety
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    -- Explicitly set search_path for security in PostgreSQL 17
    SET LOCAL search_path = public;
    PERFORM set_config('app.current_tenant_id', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get current tenant context
-- Updated for PostgreSQL 17 search path safety
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
    -- Explicitly set search_path for security in PostgreSQL 17
    SET LOCAL search_path = public;
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create function to clear tenant context
-- Updated for PostgreSQL 17 search path safety
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS void AS $$
BEGIN
    -- Explicitly set search_path for security in PostgreSQL 17
    SET LOCAL search_path = public;
    PERFORM set_config('app.current_tenant_id', '', true);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on tenant context functions
GRANT EXECUTE ON FUNCTION set_tenant_context(UUID) TO auth_service_role;
GRANT EXECUTE ON FUNCTION get_current_tenant_id() TO auth_service_role;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO auth_service_role;

-- Create a function to generate secure tokens
-- Updated for PostgreSQL 17 search path safety
CREATE OR REPLACE FUNCTION generate_secure_token(length INTEGER DEFAULT 32)
RETURNS TEXT AS $$
BEGIN
    -- Explicitly set search_path for security in PostgreSQL 17
    SET LOCAL search_path = public;
    RETURN encode(gen_random_bytes(length), 'hex');
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION generate_secure_token(INTEGER) TO auth_service_role;

-- Create updated_at trigger function
-- Updated for PostgreSQL 17 search path safety
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    -- Explicitly set search_path for security in PostgreSQL 17
    SET LOCAL search_path = public;
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'CrabShield Auth Service database initialized successfully';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pgcrypto';
    RAISE NOTICE 'Auth service role created and configured';
    RAISE NOTICE 'Tenant context functions created';
    RAISE NOTICE 'Ready for application migrations';
END $$;
