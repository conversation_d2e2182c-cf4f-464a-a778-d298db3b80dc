#!/bin/bash

# CrabShield Auth Service - Deployment Script
# This script deploys the auth service to a VPS using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_DIR/.env"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.yml"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        log_info "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if user is in docker group
    if ! groups "$USER" | grep -q docker; then
        log_error "User $USER is not in the docker group."
        log_info "Please add your user to the docker group: sudo usermod -aG docker $USER"
        log_info "Then log out and log back in."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Setup environment file
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f "$PROJECT_DIR/.env" ]]; then
            log_info "Creating environment file from example..."
            cp "$PROJECT_DIR/.env" "$ENV_FILE"
            log_warning "Please edit $ENV_FILE with your configuration before continuing"
            log_info "Required changes:"
            echo "  - Set secure passwords for POSTGRES_PASSWORD and REDIS_PASSWORD"
            echo "  - Set JWT_SECRET and ENCRYPTION_KEY to secure random values"
            echo "  - Configure RESEND_API_KEY for email functionality"
            echo "  - Update ALLOWED_ORIGINS and REDIRECT_URIS for your domain"
            echo "  - Set CRABSHIELD_WEB_CLIENT_ID and CRABSHIELD_WEB_CLIENT_SECRET"
            echo ""
            read -r -p "Press Enter after you've configured the environment file..."
        else
            log_error "Environment example file not found: $PROJECT_DIR/.env"
            exit 1
        fi
    fi
    
    # Validate critical environment variables
    # shellcheck source=/dev/null
    source "$ENV_FILE"

    REQUIRED_VARS=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
    )
    
    MISSING_VARS=()
    for var in "${REQUIRED_VARS[@]}"; do
        if [[ -z "${!var}" ]]; then
            MISSING_VARS+=("$var")
        fi
    done
    
    if [[ ${#MISSING_VARS[@]} -ne 0 ]]; then
        log_error "Missing required environment variables:"
        for var in "${MISSING_VARS[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi
    
    log_success "Environment configuration validated"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    sudo mkdir -p /var/lib/crabshield-auth/{postgres,redis,logs,ssl}
    sudo chown -R "$USER":"$USER" /var/lib/crabshield-auth
    
    log_success "Directories created"
}

# Setup SSL certificates (optional)
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    SSL_DIR="$PROJECT_DIR/docker/ssl"
    mkdir -p "$SSL_DIR"
    
    if [[ ! -f "$SSL_DIR/cert.pem" ]] || [[ ! -f "$SSL_DIR/key.pem" ]]; then
        log_warning "SSL certificates not found"
        log_info "You can:"
        echo "  1. Place your SSL certificates in $SSL_DIR/ (cert.pem and key.pem)"
        echo "  2. Use Let's Encrypt with certbot"
        echo "  3. Generate self-signed certificates for testing"
        echo ""
        read -p "Generate self-signed certificates for testing? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Generating self-signed certificates..."
            openssl req -x509 -newkey rsa:4096 -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
                -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
            log_success "Self-signed certificates generated"
        fi
    else
        log_success "SSL certificates found"
    fi
}

# Build and deploy
deploy() {
    log_info "Building and deploying CrabShield Auth Service..."
    
    cd "$PROJECT_DIR"
    
    # Pull latest images
    log_info "Pulling latest base images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull auth-postgres auth-redis
    
    # Build the auth service
    log_info "Building auth service..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build auth-service
    
    # Start services
    log_info "Starting services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_success "Services started"
}

# Wait for services to be healthy
wait_for_services() {
    log_info "Waiting for services to be healthy..."
    
    # Wait for database
    log_info "Waiting for database..."
    for i in {1..30}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T auth-postgres pg_isready -U auth_user -d crabshield_auth; then
            log_success "Database is ready"
            break
        fi
        echo "  Attempt $i/30: Database not ready, waiting 5 seconds..."
        sleep 5
    done
    
    # Wait for Redis
    log_info "Waiting for Redis..."
    for i in {1..30}; do
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T auth-redis redis-cli ping; then
            log_success "Redis is ready"
            break
        fi
        echo "  Attempt $i/30: Redis not ready, waiting 5 seconds..."
        sleep 5
    done
    
    # Wait for auth service
    log_info "Waiting for auth service..."
    for i in {1..60}; do
        if curl -f http://localhost:8080/health >/dev/null 2>&1; then
            log_success "Auth service is ready"
            break
        fi
        echo "  Attempt $i/60: Auth service not ready, waiting 5 seconds..."
        sleep 5
    done
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # For now, we'll just check if the service is running
    # In a full implementation, this would run the actual migrations
    if curl -f http://localhost:8080/health >/dev/null 2>&1; then
        log_success "Service is running - migrations will be handled by the application"
    else
        log_error "Service is not responding"
        exit 1
    fi
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo ""
    
    # Show running containers
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    
    # Show service URLs
    log_info "Service URLs:"
    echo "  Auth Service: http://localhost:8080"
    echo "  Health Check: http://localhost:8080/health"
    echo "  API Documentation: http://localhost:8080/docs (if enabled)"
    echo ""
    
    # Show logs command
    log_info "To view logs:"
    echo "  docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE logs -f"
    echo ""
    
    # Show stop command
    log_info "To stop services:"
    echo "  docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down"
    echo ""
}

# Main deployment function
main() {
    echo "🚀 CrabShield Auth Service -  Deployment"
    echo "=============================================="
    echo ""
    
    check_root
    check_prerequisites
    setup_environment
    create_directories
    setup_ssl
    deploy
    wait_for_services
    run_migrations
    show_status
    
    log_success "🎉 Deployment completed successfully!"
    log_info "Shields Up! You're protected by the crabbiest of shields"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping CrabShield Auth Service..."
        cd "$PROJECT_DIR"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting CrabShield Auth Service..."
        cd "$PROJECT_DIR"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart
        log_success "Services restarted"
        ;;
    "logs")
        cd "$PROJECT_DIR"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
        ;;
    "status")
        cd "$PROJECT_DIR"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Deploy the auth service (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show service logs"
        echo "  status  - Show service status"
        exit 1
        ;;
esac
