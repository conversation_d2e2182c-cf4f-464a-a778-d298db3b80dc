version: '3.8'

services:
  postgres:
    image: postgres:17.5-alpine
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  signoz-otel-collector:
    image: signoz/otelcontribcol:v0.100.0
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./signoz/otel-collector.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"
      - "4318:4318"

  signoz-clickhouse:
    image: clickhouse/clickhouse-server:25.5.2.47-alpine
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"

  crab-stash:
    build: ./crab-stash
    environment:
      - CONTABO_S3_ENDPOINT=${CONTABO_S3_ENDPOINT}
      - CONTABO_ACCESS_KEY=${CONTABO_ACCESS_KEY}
      - CONTABO_SECRET_KEY=${CONTABO_SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CRAB_SHIELD_URL=${CRAB_SHIELD_URL}
    depends_on:
      - postgres
      - redis
      - crab-shield
    ports:
      - "8082:8080"

  minio:
    image: quay.io/minio/minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9002:9000"
      - "9003:9001"

volumes:
  postgres_data:
  clickhouse_data:
  minio_data: