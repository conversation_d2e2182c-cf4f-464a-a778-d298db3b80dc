name: I think I found a bug
description: File a bug report issue. If you have a question or are experiencing a problem, please start a [new discussion](https://github.com/stalwartlabs/stalwart/discussions/new?category=q-a) instead.
title: "🐛: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Use this form only for reporting bugs. If you have a question or problem, please use the [Q&A discussion](https://github.com/stalwartlabs/stalwart/discussions/new?category=q-a).
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
      value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: How can we reproduce the problem?
      description: Please provide detailed steps for reproducing the problem.
      placeholder: Tell us how to reproduce the problem!
      value: "I can reproduce the problem by doing the following steps:"
    validations:
      required: true
  - type: dropdown
    id: version
    attributes:
      label: Version
      description: What version of our software are you running?
      options:
        - v0.12.x
        - v0.11.x
        - v0.10.x or lower
    validations:
      required: true
  - type: dropdown
    id: db
    attributes:
      label: What database are you using?
      options:
        - RocksDB
        - FoundationDB
        - PostgreSQL
        - mySQL
        - SQLite
  - type: dropdown
    id: blob
    attributes:
      label: What blob storage are you using?
      options:
        - RocksDB
        - FoundationDB
        - PostgreSQL
        - mySQL
        - SQLite
        - Filesystem
        - S3-compatible
        - Azure
  - type: dropdown
    id: directory
    attributes:
      label: Where is your directory located?
      options:
        - Internal
        - SQL
        - LDAP
        - OIDC
  - type: dropdown
    id: os
    attributes:
      label: What operating system are you using?
      options:
        - Linux
        - Docker
        - MacOS
        - NixOS
        - FreeBSD
        - Windows
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output, set logging level to `trace` if you can't find any relevant errors in the log.
      render: shell
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/stalwartlabs/.github/blob/main/CODE_OF_CONDUCT.md)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
