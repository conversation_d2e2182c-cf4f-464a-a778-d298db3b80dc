[package]
name = "services"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
store = { path = "../store" }
common = { path =  "../common" }
utils = { path =  "../utils" }
trc = { path = "../trc" }
email = { path = "../email" }
smtp = { path = "../smtp" }
groupware = { path = "../groupware" }
jmap_proto = { path = "../jmap-proto" }
directory = { path =  "../directory" }
smtp-proto = { version = "0.1.6", features = ["rkyv", "serde"] }
tokio = { version = "1.45", features = ["rt"] }
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] }
mail-builder = { version = "0.4" } 
calcard = { version = "0.1.3", features = ["rkyv"] }
chrono = { version = "0.4", features = ["unstable-locales"] }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
memory-stats = "1.2.0"
aes-gcm = "0.10.1"
aes-gcm-siv = "0.11.1"
rsa = "0.9.2"
p256 = { version = "0.13", features = ["ecdh"] }
hkdf = "0.12.3"
sha2 = "0.10"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2"]}
base64 = "0.22"
compact_str = "0.9.0"

[dev-dependencies]

[features]
test_mode = []
enterprise = []
