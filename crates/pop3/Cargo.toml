[package]
name = "pop3"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
store = { path = "../store" }
common = { path = "../common" }
directory = { path = "../directory" }
imap = { path = "../imap" }
utils = { path = "../utils" }
trc = { path = "../trc" }
jmap_proto = { path = "../jmap-proto" }
email = { path = "../email" }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
tokio = { version = "1.45", features = ["full"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }

[features]
test_mode = []
