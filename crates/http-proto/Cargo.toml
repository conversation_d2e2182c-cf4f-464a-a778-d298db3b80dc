[package]
name = "http_proto"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
common = { path = "../common" }
trc = { path = "../trc" }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
hyper = { version = "1.0.1", features = ["server", "http1", "http2"] }
hyper-util = { version = "0.1.1", features = ["tokio"] }
http-body-util = "0.1.0"
form_urlencoded = "1.1.0"
percent-encoding = "2.3.1"
compact_str = "0.9.0"

[dev-dependencies]

[features]
test_mode = []
enterprise = []
