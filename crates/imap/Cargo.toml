[package]
name = "imap"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
imap_proto = { path = "../imap-proto" }
jmap_proto = { path = "../jmap-proto" }
directory = { path = "../directory" }
trc = { path = "../trc" }
store = { path = "../store" }
common = { path = "../common" }
email = { path = "../email" }
nlp = { path = "../nlp" }
utils = { path = "../utils" }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
rustls-pemfile = "2.0"
tokio = { version = "1.45", features = ["full"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }
parking_lot = "0.12"
ahash = { version = "0.8" }
md5 = "0.7.0"
rand = "0.9.0"
indexmap = "2.7.1"
compact_str = "0.9.0"

[features]
test_mode = []
