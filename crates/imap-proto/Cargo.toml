[package]
name = "imap_proto"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
jmap_proto = { path = "../jmap-proto" }
store = { path = "../store" }
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] } 
ahash = { version = "0.8" }
chrono = { version = "0.4"}
trc = { path = "../trc" }
hashify = { version = "0.2" }
compact_str = "0.9.0"

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
