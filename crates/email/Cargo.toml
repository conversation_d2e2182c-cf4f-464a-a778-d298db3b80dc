[package]
name = "email"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
utils = { path = "../utils" }
nlp = { path = "../nlp" }
store = { path = "../store" }
trc = { path = "../trc" }
jmap_proto = { path = "../jmap-proto" }
common = { path =  "../common" }
directory = { path =  "../directory" }
spam-filter = { path =  "../spam-filter" }
smtp-proto = { version = "0.1", features = ["rkyv"] }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-builder = { version = "0.4" }
sieve-rs = { version = "0.7", features = ["rkyv"] } 
tokio = { version = "1.45", features = ["net", "macros"] }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
aes = "0.8.3"
aes-gcm = "0.10.1"
aes-gcm-siv = "0.11.1"
cbc = { version = "0.1.2", features = ["alloc"] }
rasn = "0.10"
rasn-cms = "0.10"
rasn-pkix = "0.10"
rsa = "0.9.2"
rand = "0.8"
sequoia-openpgp = { version = "2.0", default-features = false, features = ["crypto-rust", "allow-experimental-crypto", "allow-variable-time-crypto"] }
hashify = "0.2"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"

[features]
test_mode = []
enterprise = []

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
