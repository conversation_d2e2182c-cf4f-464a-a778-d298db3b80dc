[package]
name = "jmap"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
store = { path = "../store" }
nlp = { path = "../nlp" }
http_proto = { path = "../http-proto" }
jmap_proto = { path = "../jmap-proto" }
smtp = { path =  "../smtp" }
utils = { path =  "../utils" }
common = { path =  "../common" }
services = { path =  "../services" }
directory = { path =  "../directory" }
trc = { path = "../trc" }
spam-filter = { path = "../spam-filter" }
email = { path = "../email" }
smtp-proto = { version = "0.1" }
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] } 
mail-builder = { version = "0.4" }
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
mail-auth = { version = "0.7.1", features = ["generate"] }
sieve-rs = { version = "0.7", features = ["rkyv"] } 
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
hyper = { version = "1.0.1", features = ["server", "http1", "http2"] }
hyper-util = { version = "0.1.1", features = ["tokio"] }
http-body-util = "0.1.0"
tokio = { version = "1.45", features = ["rt"] }
futures-util = "0.3.28"
async-stream = "0.3.5"
base64 = "0.22"
p256 = { version = "0.13", features = ["ecdh"] }
hkdf = "0.12.3"
sha1 = "0.10"
sha2 = "0.10"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2"]}
tokio-tungstenite = "0.26"
tungstenite = "0.26"
chrono = "0.4"
rand = "0.9.0"
pkcs8 = { version = "0.10.2", features = ["alloc", "std"] }
lz4_flex = { version = "0.11", default-features = false }
aes-gcm = "0.10.1"
aes-gcm-siv = "0.11.1"
rsa = "0.9.2"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"

[features]
test_mode = []
enterprise = []
