[package]
name = "store"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
utils = { path = "../utils" }
nlp = { path = "../nlp" }
trc = { path = "../trc" }
rocksdb = { version = "0.23", optional = true, features = ["multi-threaded-cf"] }
foundationdb = { version = "0.9.2", features = ["embedded-fdb-include", "fdb-7_3"], optional = true }
rusqlite = { version = "0.35", features = ["bundled"], optional = true }
rust-s3 = { version = "0.35", default-features = false, features = ["tokio-rustls-tls", "no-verify-ssl"], optional = true }
async-nats = { version = "0.40", default-features = false, features = ["server_2_10", "server_2_11", "ring"], optional = true }
azure_core = { version = "0.21.0", optional = true }
azure_storage = { version = "0.21.0", default-features = false, features = ["enable_reqwest_rustls", "hmac_rust"], optional = true }
azure_storage_blobs = { version = "0.21.0", default-features = false, features = ["enable_reqwest_rustls", "hmac_rust"], optional = true }
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2", "stream"]}
tokio = { version = "1.45", features = ["sync", "fs", "io-util"] }
r2d2 = { version = "0.8.10", optional = true }
futures = { version = "0.3", optional = true }
rand = "0.9.0"
roaring = "0.10.1"
rayon = { version = "1.5.1", optional = true }
serde = { version = "1.0", features = ["derive"]}
ahash = { version = "0.8.2", features = ["serde"] }
xxhash-rust = { version = "0.8.5", features = ["xxh3"] }
farmhash = "1.1.5"
parking_lot = "0.12.1"
lru-cache = { version = "0.1.2", optional = true }
num_cpus = { version = "1.15.0", optional = true }
blake3 = "1.3.3"
lz4_flex = { version = "0.11", default-features = false }
deadpool-postgres = { version = "0.14", optional = true }
tokio-postgres = { version = "0.7.10", optional = true }
tokio-rustls = { version = "0.26", optional = true, default-features = false, features = ["ring", "tls12"] }
rustls = { version = "0.23.5", optional = true, default-features = false, features = ["std", "ring", "tls12"] }
rustls-pki-types = { version = "1", optional = true }
ring = { version = "0.17", optional = true }
bytes = { version = "1.0", optional = true }
mysql_async = { version = "0.36", default-features = false, features = ["default-rustls-ring", "minimal"], optional = true }
elasticsearch = { version = "8.5.0-alpha.1", default-features = false, features = ["rustls-tls"], optional = true }
serde_json = {version = "1.0.64", optional = true }
regex = "1.7.0"
flate2 = "1.0"
async-trait = "0.1.68"
redis = { version = "0.31", features = [ "tokio-comp", "tokio-rustls-comp", "tls-rustls-insecure", "tls-rustls-webpki-roots", "cluster-async"], optional = true }
deadpool = { version = "0.12", features = ["managed"], optional = true }
arc-swap = "1.6.0"
bitpacking = "0.9.2"
memchr = { version = "2" }
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"
zenoh = { version = "1.3.4", default-features = false, features = ["auth_pubkey", "transport_multilink", "transport_compression", "transport_quic", "transport_tcp", "transport_tls", "transport_udp"], optional = true }
rdkafka = { version = "0.37.0", features = ["cmake-build"], optional = true }

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }

[features]

# Data Stores
rocks = ["rocksdb", "rayon", "num_cpus"]
sqlite = ["rusqlite", "rayon", "r2d2", "num_cpus", "lru-cache"]
postgres = ["tokio-postgres", "deadpool-postgres", "tokio-rustls", "rustls", "ring", "rustls-pki-types", "futures", "bytes"]
mysql = ["mysql_async", "futures"]
foundation = ["foundationdb", "futures"]
fdb-chunked-bm = []

# Blob stores
s3 = ["rust-s3"]
azure = ["azure_core", "azure_storage", "azure_storage_blobs"]

# Full-text stores
elastic = ["elasticsearch", "serde_json"]

# In-memory stores
redis = ["dep:redis", "deadpool", "futures"]

# Pubsub
nats = ["async-nats"]
zenoh = ["dep:zenoh"]
kafka = ["rdkafka"]

enterprise = []
test_mode = []
