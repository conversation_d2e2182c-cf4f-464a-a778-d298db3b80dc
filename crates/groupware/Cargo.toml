[package]
name = "groupware"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
utils = { path = "../utils" }
store = { path = "../store" }
common = { path =  "../common" }
jmap_proto = { path =  "../jmap-proto" }
trc = { path = "../trc" }
directory = { path =  "../directory" }
dav-proto = { path =  "../dav-proto" }
calcard = { version = "0.1.3", features = ["rkyv"] }
hashify = "0.2"
tokio = { version = "1.45", features = ["net", "macros"] }
rkyv = { version = "0.8.10", features = ["little_endian"] }
percent-encoding = "2.3.1"
compact_str = "0.9.0"
ahash = { version = "0.8" }
chrono = "0.4.40"

[features]
test_mode = []
enterprise = []

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
