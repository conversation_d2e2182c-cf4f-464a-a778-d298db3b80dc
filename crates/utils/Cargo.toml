[package]
name = "utils"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
trc = { path = "../trc" }
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
rustls-pemfile = "2.0"
rustls-pki-types = { version = "1" }
tokio = { version = "1.45", features = ["net", "macros", "signal"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }
serde = { version = "1.0", features = ["derive"]}
mail-auth = { version = "0.7.1" }
smtp-proto = { version = "0.1" }
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
ahash = { version = "0.8", features = ["serde"] }
chrono = "0.4"
rand = "0.9.0"
webpki-roots = { version = "1.0"}
ring = { version = "0.17" }
base64 = "0.22"
serde_json = "1.0"
rcgen = "0.13"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2", "stream"]}
x509-parser = "0.17.0"
pem = "3.0"
parking_lot = "0.12"
futures = "0.3"
regex = "1.7.0"
blake3 = "1.3.3"
http-body-util = "0.1.0"
form_urlencoded = "1.1.0"
psl = "2"
quick_cache = "0.6.9"
downcast-rs = "2.0.1"
fast-float = "0.2.0"
erased-serde = "0.4.5"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"

[target.'cfg(unix)'.dependencies]
privdrop = "0.5.3"

[features]
test_mode = []

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
