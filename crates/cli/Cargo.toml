[package]
name = "stalwart-cli"
description = "Stalwart Server CLI"
authors = ["Stalwart Labs LLC <<EMAIL>>"]
license = "AGPL-3.0-only OR LicenseRef-SEL"
repository = "https://github.com/stalwartlabs/cli"
homepage = "https://github.com/stalwartlabs/cli"
version = "0.12.4"
edition = "2024"
readme = "README.md"
resolver = "2"

[dependencies]
jmap-client = { version = "0.3", features = ["async"] } 
mail-parser = { version = "0.11", features = ["full_encoding", "serde"] } 
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2"]}
tokio = { version = "1.45", features = ["full"] }
num_cpus = "1.13.1"
clap = { version = "4.1.6", features = ["derive"] }
prettytable-rs = "0.10.0"
rpassword = "7.0"
indicatif = "0.17.0"
console = { version = "0.15", default-features = false, features = ["ansi-parsing"] }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
csv = "1.1"
form_urlencoded = "1.1.0"
human-size = "0.4.2"
futures = "0.3.28"
pwhash = "1.0.0"
rand = "0.9.0"
mail-auth = { version = "0.7.1" }
