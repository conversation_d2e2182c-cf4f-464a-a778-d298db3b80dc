[package]
name = "migration"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
utils = { path = "../utils" }
nlp = { path = "../nlp" }
store = { path = "../store" }
trc = { path = "../trc" }
jmap_proto = { path = "../jmap-proto" }
common = { path =  "../common" }
email = { path =  "../email" }
directory = { path =  "../directory" }
smtp = { path =  "../smtp" }
groupware = { path =  "../groupware" }
dav-proto = { path =  "../dav-proto" }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-auth = { version = "0.7.1", features = ["rkyv"] }
sieve-rs = { version = "0.7", features = ["rkyv"] } 
calcard = { version = "0.1.3", features = ["rkyv"] }
tokio = { version = "1.45", features = ["net", "macros"] }
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"
bincode = "1.3.3" 
lz4_flex = { version = "0.11", default-features = false } 
base64 = "0.22"

[features]
test_mode = []
enterprise = []

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
