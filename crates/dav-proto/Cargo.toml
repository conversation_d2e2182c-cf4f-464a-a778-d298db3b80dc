[package]
name = "dav-proto"
version = "0.12.4"
edition = "2021"

[dependencies]
trc = { path = "../trc" }
hashify = "0.2.6"
quick-xml = "0.37.2"
calcard = { version = "0.1.3", features = ["rkyv"] }
mail-parser = { version = "0.11", features = ["full_encoding", "rkyv"] }
hyper = "1.6.0"
rkyv = { version = "0.8.10", features = ["little_endian"] }
chrono = { version = "0.4.40", features = ["serde"], optional = true }
compact_str = "0.9.0"

[dev-dependencies]
calcard = { version = "0.1.3", features = ["serde", "rkyv"] }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.138"
chrono = { version = "0.4.40", features = ["serde"] }

[features]
test_mode = ["chrono"]
enterprise = []
