   <?xml version="1.0" encoding="utf-8" ?>
   <D:multistatus xmlns:D="DAV:">
     <D:response>
       <D:href>http://www.example.com/papers/</D:href>
       <D:propstat>
         <D:prop>
           <D:supported-privilege-set>
             <D:supported-privilege>
               <D:privilege><D:all/></D:privilege>
              <D:abstract/>
               <D:description>Any operation</D:description>
               <D:supported-privilege>
                 <D:privilege><D:read/></D:privilege>
                 <D:description>Read any object</D:description>
                 <D:supported-privilege>
                   <D:privilege><D:read-acl/></D:privilege>
                   <D:abstract/>
                   <D:description>Read ACL</D:description>
                 </D:supported-privilege>
                 <D:supported-privilege>
                   <D:privilege>
                     <D:read-current-user-privilege-set/>
                   </D:privilege>
                   <D:abstract/>
                   <D:description>Read current user privilege set property</D:description>
                 </D:supported-privilege>
               </D:supported-privilege>
               <D:supported-privilege>
                 <D:privilege><D:write/></D:privilege>
                 <D:description>Write any object</D:description>
                 <D:supported-privilege>
                   <D:privilege><D:write-acl/></D:privilege>
                   <D:abstract/>
                   <D:description>Write ACL</D:description>
                 </D:supported-privilege>
                 <D:supported-privilege>
                   <D:privilege><D:write-properties/></D:privilege>
                   <D:description>Write properties</D:description>
                 </D:supported-privilege>
                 <D:supported-privilege>
                   <D:privilege><D:write-content/></D:privilege>
                   <D:description>Write resource content</D:description>
                 </D:supported-privilege>
               </D:supported-privilege>
               <D:supported-privilege>
                 <D:privilege><D:unlock/></D:privilege>
                 <D:description>Unlock resource</D:description>
               </D:supported-privilege>
             </D:supported-privilege>
           </D:supported-privilege-set>
         </D:prop>
         <D:status>HTTP/1.1 200 OK</D:status>
       </D:propstat>
     </D:response>
   </D:multistatus>