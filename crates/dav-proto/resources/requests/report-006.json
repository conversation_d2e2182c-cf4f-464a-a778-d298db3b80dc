{"type": "CalendarQuery", "properties": {"type": "Prop", "data": [{"type": "WebDav", "data": {"type": "GetETag"}}, {"type": "CalDav", "data": {"type": "CalendarData", "data": {"properties": [], "expand": null, "limit_recurrence": null, "limit_freebusy": null}}}]}, "filters": [{"type": "Property", "comp": ["VCalendar", "VEvent"], "prop": {"type": "<PERSON><PERSON>"}, "op": {"type": "TextMatch", "data": {"type": "TextMatch", "match_type": "Contains", "value": "<EMAIL>", "collation": "Octet", "negate": false}}}], "timezone": {"type": "None"}}