{"type": "CalendarQuery", "properties": {"type": "Prop", "data": [{"type": "WebDav", "data": {"type": "GetETag"}}, {"type": "CalDav", "data": {"type": "CalendarData", "data": {"properties": [], "expand": null, "limit_recurrence": null, "limit_freebusy": null}}}]}, "filters": [{"type": "Property", "comp": ["VCalendar", "VTodo"], "prop": {"type": "Completed"}, "op": {"type": "Undefined"}}, {"type": "Property", "comp": ["VCalendar", "VTodo"], "prop": {"type": "Status"}, "op": {"type": "TextMatch", "data": {"type": "TextMatch", "match_type": "Contains", "value": "CANCELLED", "collation": "AsciiCasemap", "negate": true}}}], "timezone": {"type": "None"}}