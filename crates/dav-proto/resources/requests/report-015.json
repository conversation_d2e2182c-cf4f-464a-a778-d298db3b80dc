{"type": "AddressbookQuery", "properties": {"type": "Prop", "data": [{"type": "WebDav", "data": {"type": "GetETag"}}, {"type": "CardDav", "data": {"type": "AddressData", "data": [{"group": null, "name": {"type": "Version"}, "no_value": false}, {"group": null, "name": {"type": "<PERSON><PERSON>"}, "no_value": false}, {"group": null, "name": {"type": "Nickname"}, "no_value": false}, {"group": null, "name": {"type": "Email"}, "no_value": false}, {"group": null, "name": {"type": "Fn"}, "no_value": false}]}}]}, "filters": [{"type": "AnyOf"}, {"type": "Property", "comp": null, "prop": {"name": {"type": "Fn"}, "group": null}, "op": {"type": "TextMatch", "data": {"type": "TextMatch", "match_type": "Contains", "value": "daboo", "collation": "UnicodeCasemap", "negate": false}}}, {"type": "Property", "comp": null, "prop": {"name": {"type": "Email"}, "group": null}, "op": {"type": "TextMatch", "data": {"type": "TextMatch", "match_type": "Contains", "value": "daboo", "collation": "UnicodeCasemap", "negate": false}}}], "limit": null}