/*
 * SPDX-FileCopyrightText: 2020 Stalwart Labs LLC <<EMAIL>>
 *
 * SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-SEL
 */

pub type StopwordFnc = fn(&str) -> bool;

pub static STOP_WORDS: &[Option<StopwordFnc>] = &[
    None,              // Esperanto = 0,
    Some(english),     // English = 1,
    Some(russian),     // Russian = 2,
    None,              // Mandarin = 3,
    Some(spanish),     // Spanish = 4,
    Some(portuguese),  // Portuguese = 5,
    Some(italian),     // Italian = 6,
    None,              // Bengali = 7,
    Some(french),      // French = 8,
    Some(german),      // German = 9,
    None,              // Ukrainian = 10,
    None,              // Georgian = 11,
    Some(arabic),      // Arabic = 12,
    None,              // Hindi = 13,
    None,              // Japanese = 14,
    None,              // Hebrew = 15,
    None,              // Yiddish = 16,
    None,              // Polish = 17,
    None,              // Amharic = 18,
    None,              // Javanese = 19,
    None,              // Korean = 20,
    Some(norwegian),   // Bokmal = 21,
    Some(danish),      // Danish = 22,
    Some(swedish),     // Swedish = 23,
    Some(finnish),     // Finnish = 24,
    Some(turkish),     // Turkish = 25,
    Some(dutch),       // Dutch = 26,
    Some(hungarian),   // Hungarian = 27,
    None,              // Czech = 28,
    Some(greek),       // Greek = 29,
    None,              // Bulgarian = 30,
    None,              // Belarusian = 31,
    None,              // Marathi = 32,
    None,              // Kannada = 33,
    Some(romanian),    // Romanian = 34,
    None,              // Slovene = 35,
    None,              // Croatian = 36,
    None,              // Serbian = 37,
    None,              // Macedonian = 38,
    None,              // Lithuanian = 39,
    None,              // Latvian = 40,
    None,              // Estonian = 41,
    None,              // Tamil = 42,
    None,              // Vietnamese = 43,
    None,              // Urdu = 44,
    None,              // Thai = 45,
    None,              // Gujarati = 46,
    None,              // Uzbek = 47,
    None,              // Punjabi = 48,
    Some(azarbaijani), // Azerbaijani = 49,
    None,              // Indonesian = 50,
    None,              // Telugu = 51,
    None,              // Persian = 52,
    None,              // Malayalam = 53,
    None,              // Oriya = 54,
    None,              // Burmese = 55,
    Some(nepali),      // Nepali = 56,
    None,              // Sinhalese = 57,
    None,              // Khmer = 58,
    None,              // Turkmen = 59,
    None,              // Akan = 60,
    None,              // Zulu = 61,
    None,              // Shona = 62,
    None,              // Afrikaans = 63,
    None,              // Latin = 64,
    None,              // Slovak = 65,
    None,              // Catalan = 66,
    None,              // Tagalog = 67,
    None,              // Armenian = 68,
    None,              // Unknown = 69,
    None,              // None = 70,
];

fn arabic(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "آه",
        "آي",
        "أف",
        "أم",
        "أن",
        "أو",
        "أي",
        "إذ",
        "إن",
        "إي",
        "بخ",
        "بس",
        "بك",
        "بل",
        "به",
        "بي",
        "ته",
        "تي",
        "ثم",
        "ذا",
        "ذه",
        "ذو",
        "ذي",
        "عل",
        "عن",
        "في",
        "قد",
        "كل",
        "كم",
        "كي",
        "لا",
        "لك",
        "لم",
        "لن",
        "له",
        "لو",
        "لي",
        "ما",
        "مذ",
        "مع",
        "من",
        "مه",
        "ها",
        "هل",
        "هم",
        "هن",
        "هو",
        "هي",
        "يا",
        "آها",
        "أقل",
        "ألا",
        "أما",
        "أنا",
        "أنت",
        "أنى",
        "أوه",
        "أين",
        "إذا",
        "إذن",
        "إلا",
        "إلى",
        "إما",
        "إنا",
        "إنه",
        "إيه",
        "بعد",
        "بعض",
        "بكم",
        "بكن",
        "بلى",
        "بما",
        "بمن",
        "بنا",
        "بها",
        "بهم",
        "بهن",
        "بيد",
        "بين",
        "تلك",
        "تين",
        "ثمة",
        "حتى",
        "حيث",
        "حين",
        "خلا",
        "دون",
        "ذات",
        "ذاك",
        "ذان",
        "ذلك",
        "ذوا",
        "ذين",
        "ريث",
        "سوف",
        "سوى",
        "عدا",
        "عسى",
        "على",
        "عما",
        "عند",
        "غير",
        "فإن",
        "فلا",
        "فمن",
        "فيم",
        "فيه",
        "كأن",
        "كأي",
        "كذا",
        "كلا",
        "كما",
        "كيت",
        "كيف",
        "لئن",
        "لدى",
        "لست",
        "لسن",
        "لعل",
        "لكم",
        "لكن",
        "لكي",
        "لما",
        "لنا",
        "لها",
        "لهم",
        "لهن",
        "ليت",
        "ليس",
        "متى",
        "مما",
        "ممن",
        "منذ",
        "منه",
        "نحن",
        "نحو",
        "نعم",
        "هاك",
        "هذا",
        "هذه",
        "هذي",
        "هلا",
        "هما",
        "هنا",
        "هيا",
        "هيت",
        "وإذ",
        "وإن",
        "ولا",
        "ولو",
        "وما",
        "ومن",
        "وهو",
        "أكثر",
        "أنتم",
        "أنتن",
        "أيها",
        "إذما",
        "إليك",
        "إنما",
        "التي",
        "الذي",
        "بكما",
        "بهما",
        "تلكم",
        "تينك",
        "حاشا",
        "حبذا",
        "ذانك",
        "ذلكم",
        "ذلكن",
        "ذينك",
        "شتان",
        "عليك",
        "عليه",
        "فإذا",
        "فيما",
        "فيها",
        "كأين",
        "كذلك",
        "كلتا",
        "كلما",
        "لستم",
        "لستن",
        "لسنا",
        "لكما",
        "لهما",
        "لولا",
        "لوما",
        "ليسا",
        "ليست",
        "ماذا",
        "منها",
        "مهما",
        "هاته",
        "هاتي",
        "هذان",
        "هذين",
        "هكذا",
        "هناك",
        "وإذا",
        "ولكن",
        "أنتما",
        "أولئك",
        "أولاء",
        "أينما",
        "إليكم",
        "إليكن",
        "الذين",
        "بماذا",
        "تلكما",
        "حيثما",
        "ذلكما",
        "ذواتا",
        "ذواتي",
        "كأنما",
        "كيفما",
        "لستما",
        "لكنما",
        "لكيلا",
        "ليستا",
        "ليسوا",
        "هؤلاء",
        "هاتان",
        "هاتين",
        "هاهنا",
        "هنالك",
        "هيهات",
        "والذي",
        "إليكما",
        "اللائي",
        "اللاتي",
        "اللتان",
        "اللتيا",
        "اللتين",
        "اللذان",
        "اللذين",
        "كلاهما",
        "كليكما",
        "كليهما",
        "لاسيما",
        "والذين",
        "اللواتي",
    )
}

fn azarbaijani(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "ad",
        "altmış",
        "altı",
        "amma",
        "arasında",
        "artıq",
        "ay",
        "az",
        "bax",
        "belə",
        "beş",
        "bilər",
        "bir",
        "biraz",
        "biri",
        "birşey",
        "biz",
        "bizim",
        "bizlər",
        "bu",
        "buna",
        "bundan",
        "bunların",
        "bunu",
        "bunun",
        "buradan",
        "bütün",
        "bəli",
        "bəlkə",
        "bəy",
        "bəzi",
        "bəzən",
        "ci",
        "çox",
        "cu",
        "cü",
        "çünki",
        "cı",
        "da",
        "daha",
        "dedi",
        "deyil",
        "dir",
        "doqquz",
        "doqsan",
        "dörd",
        "düz",
        "də",
        "dək",
        "dən",
        "dəqiqə",
        "edir",
        "edən",
        "elə",
        "et",
        "etdi",
        "etmə",
        "etmək",
        "faiz",
        "gilə",
        "görə",
        "ha",
        "haqqında",
        "harada",
        "heç",
        "hə",
        "həm",
        "həmin",
        "həmişə",
        "hər",
        "idi",
        "iki",
        "il",
        "ildə",
        "ilk",
        "ilə",
        "in",
        "indi",
        "istifadə",
        "isə",
        "iyirmi",
        "ki",
        "kim",
        "kimi",
        "kimə",
        "lakin",
        "lap",
        "mirşey",
        "məhz",
        "mən",
        "mənə",
        "niyə",
        "nə",
        "nəhayət",
        "o",
        "obirisi",
        "of",
        "olan",
        "olar",
        "olaraq",
        "oldu",
        "olduğu",
        "olmadı",
        "olmaz",
        "olmuşdur",
        "olsun",
        "olur",
        "on",
        "ona",
        "ondan",
        "onlar",
        "onlardan",
        "onların",
        "onsuzda",
        "onu",
        "onun",
        "oradan",
        "otuz",
        "öz",
        "özü",
        "qarşı",
        "qədər",
        "qırx",
        "saat",
        "sadəcə",
        "saniyə",
        "siz",
        "sizin",
        "sizlər",
        "sonra",
        "səhv",
        "səkkiz",
        "səksən",
        "sən",
        "sənin",
        "sənə",
        "təəssüf",
        "ü",
        "üç",
        "üçün",
        "var",
        "və",
        "xan",
        "xanım",
        "xeyr",
        "ya",
        "yalnız",
        "yaxşı",
        "yeddi",
        "yenə",
        "yetmiş",
        "yox",
        "yoxdur",
        "yoxsa",
        "yüz",
        "yəni",
        "zaman",
        "ı",
        "ə",
        "əgər",
        "əlbəttə",
        "əlli",
        "ən",
        "əslində",
    )
}

fn danish(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "ad",
        "af",
        "alle",
        "alt",
        "anden",
        "at",
        "blev",
        "blive",
        "bliver",
        "da",
        "de",
        "dem",
        "den",
        "denne",
        "der",
        "deres",
        "det",
        "dette",
        "dig",
        "din",
        "disse",
        "dog",
        "du",
        "efter",
        "eller",
        "en",
        "end",
        "er",
        "et",
        "for",
        "fra",
        "ham",
        "han",
        "hans",
        "har",
        "havde",
        "have",
        "hende",
        "hendes",
        "her",
        "hos",
        "hun",
        "hvad",
        "hvis",
        "hvor",
        "i",
        "ikke",
        "ind",
        "jeg",
        "jer",
        "jo",
        "kunne",
        "man",
        "mange",
        "med",
        "meget",
        "men",
        "mig",
        "min",
        "mine",
        "mit",
        "mod",
        "når",
        "ned",
        "noget",
        "nogle",
        "nu",
        "og",
        "også",
        "om",
        "op",
        "os",
        "over",
        "på",
        "sådan",
        "selv",
        "sig",
        "sin",
        "sine",
        "sit",
        "skal",
        "skulle",
        "som",
        "thi",
        "til",
        "ud",
        "under",
        "var",
        "være",
        "været",
        "vi",
        "vil",
        "ville",
        "vor",
    )
}

fn dutch(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "aan",
        "al",
        "alles",
        "als",
        "altijd",
        "andere",
        "ben",
        "bij",
        "daar",
        "dan",
        "dat",
        "de",
        "der",
        "deze",
        "die",
        "dit",
        "doch",
        "doen",
        "door",
        "dus",
        "een",
        "eens",
        "en",
        "er",
        "ge",
        "geen",
        "geweest",
        "haar",
        "had",
        "heb",
        "hebben",
        "heeft",
        "hem",
        "het",
        "hier",
        "hij",
        "hoe",
        "hun",
        "iemand",
        "iets",
        "ik",
        "in",
        "is",
        "ja",
        "je",
        "kan",
        "kon",
        "kunnen",
        "maar",
        "me",
        "meer",
        "men",
        "met",
        "mij",
        "mijn",
        "moet",
        "na",
        "naar",
        "niet",
        "niets",
        "nog",
        "nu",
        "of",
        "om",
        "omdat",
        "onder",
        "ons",
        "ook",
        "op",
        "over",
        "reeds",
        "te",
        "tegen",
        "toch",
        "toen",
        "tot",
        "u",
        "uit",
        "uw",
        "van",
        "veel",
        "voor",
        "want",
        "waren",
        "was",
        "wat",
        "werd",
        "wezen",
        "wie",
        "wil",
        "worden",
        "wordt",
        "zal",
        "ze",
        "zelf",
        "zich",
        "zij",
        "zijn",
        "zo",
        "zonder",
        "zou",
    )
}

fn english(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "about",
        "above",
        "after",
        "again",
        "against",
        "ain",
        "all",
        "am",
        "an",
        "and",
        "any",
        "are",
        "aren",
        "aren't",
        "as",
        "at",
        "be",
        "because",
        "been",
        "before",
        "being",
        "below",
        "between",
        "both",
        "but",
        "by",
        "can",
        "couldn",
        "couldn't",
        "d",
        "did",
        "didn",
        "didn't",
        "do",
        "does",
        "doesn",
        "doesn't",
        "doing",
        "don",
        "don't",
        "down",
        "during",
        "each",
        "few",
        "for",
        "from",
        "further",
        "had",
        "hadn",
        "hadn't",
        "has",
        "hasn",
        "hasn't",
        "have",
        "haven",
        "haven't",
        "having",
        "he",
        "her",
        "here",
        "hers",
        "herself",
        "him",
        "himself",
        "his",
        "how",
        "i",
        "if",
        "in",
        "into",
        "is",
        "isn",
        "isn't",
        "it",
        "it's",
        "its",
        "itself",
        "just",
        "ll",
        "m",
        "ma",
        "me",
        "mightn",
        "mightn't",
        "more",
        "most",
        "mustn",
        "mustn't",
        "my",
        "myself",
        "needn",
        "needn't",
        "no",
        "nor",
        "not",
        "now",
        "o",
        "of",
        "off",
        "on",
        "once",
        "only",
        "or",
        "other",
        "our",
        "ours",
        "ourselves",
        "out",
        "over",
        "own",
        "re",
        "s",
        "same",
        "shan",
        "shan't",
        "she",
        "she's",
        "should",
        "should've",
        "shouldn",
        "shouldn't",
        "so",
        "some",
        "such",
        "t",
        "than",
        "that",
        "that'll",
        "the",
        "their",
        "theirs",
        "them",
        "themselves",
        "then",
        "there",
        "these",
        "they",
        "this",
        "those",
        "through",
        "to",
        "too",
        "under",
        "until",
        "up",
        "ve",
        "very",
        "was",
        "wasn",
        "wasn't",
        "we",
        "were",
        "weren",
        "weren't",
        "what",
        "when",
        "where",
        "which",
        "while",
        "who",
        "whom",
        "why",
        "will",
        "with",
        "won",
        "won't",
        "wouldn",
        "wouldn't",
        "y",
        "you",
        "you'd",
        "you'll",
        "you're",
        "you've",
        "your",
        "yours",
        "yourself",
        "yourselves",
    )
}

fn finnish(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "ei",
        "eivät",
        "emme",
        "en",
        "et",
        "että",
        "ette",
        "hän",
        "häneen",
        "hänellä",
        "hänelle",
        "häneltä",
        "hänen",
        "hänessä",
        "hänestä",
        "hänet",
        "häntä",
        "he",
        "heidän",
        "heidät",
        "heihin",
        "heillä",
        "heille",
        "heiltä",
        "heissä",
        "heistä",
        "heitä",
        "itse",
        "ja",
        "johon",
        "joiden",
        "joihin",
        "joiksi",
        "joilla",
        "joille",
        "joilta",
        "joina",
        "joissa",
        "joista",
        "joita",
        "joka",
        "joksi",
        "jolla",
        "jolle",
        "jolta",
        "jona",
        "jonka",
        "jos",
        "jossa",
        "josta",
        "jota",
        "jotka",
        "kanssa",
        "keiden",
        "keihin",
        "keiksi",
        "keillä",
        "keille",
        "keiltä",
        "keinä",
        "keissä",
        "keistä",
        "keitä",
        "keneen",
        "keneksi",
        "kenellä",
        "kenelle",
        "keneltä",
        "kenen",
        "kenenä",
        "kenessä",
        "kenestä",
        "kenet",
        "ketä",
        "ketkä",
        "koska",
        "kuin",
        "kuka",
        "kun",
        "me",
        "meidän",
        "meidät",
        "meihin",
        "meillä",
        "meille",
        "meiltä",
        "meissä",
        "meistä",
        "meitä",
        "mihin",
        "mikä",
        "miksi",
        "millä",
        "mille",
        "miltä",
        "minä",
        "minkä",
        "minua",
        "minulla",
        "minulle",
        "minulta",
        "minun",
        "minussa",
        "minusta",
        "minut",
        "minuun",
        "missä",
        "mistä",
        "mitä",
        "mitkä",
        "mukaan",
        "mutta",
        "näiden",
        "näihin",
        "näiksi",
        "näillä",
        "näille",
        "näiltä",
        "näinä",
        "näissä",
        "näistä",
        "näitä",
        "nämä",
        "ne",
        "niiden",
        "niihin",
        "niiksi",
        "niillä",
        "niille",
        "niiltä",
        "niin",
        "niinä",
        "niissä",
        "niistä",
        "niitä",
        "noiden",
        "noihin",
        "noiksi",
        "noilla",
        "noille",
        "noilta",
        "noin",
        "noina",
        "noissa",
        "noista",
        "noita",
        "nuo",
        "nyt",
        "ole",
        "olemme",
        "olen",
        "olet",
        "olette",
        "oli",
        "olimme",
        "olin",
        "olisi",
        "olisimme",
        "olisin",
        "olisit",
        "olisitte",
        "olisivat",
        "olit",
        "olitte",
        "olivat",
        "olla",
        "olleet",
        "ollut",
        "on",
        "ovat",
        "poikki",
        "se",
        "sekä",
        "sen",
        "siihen",
        "siinä",
        "siitä",
        "siksi",
        "sillä",
        "sille",
        "siltä",
        "sinä",
        "sinua",
        "sinulla",
        "sinulle",
        "sinulta",
        "sinun",
        "sinussa",
        "sinusta",
        "sinut",
        "sinuun",
        "sitä",
        "tähän",
        "tai",
        "täksi",
        "tallä",
        "tälle",
        "tältä",
        "tämä",
        "tämän",
        "tänä",
        "tässä",
        "tästä",
        "tätä",
        "te",
        "teidän",
        "teidät",
        "teihin",
        "teillä",
        "teille",
        "teiltä",
        "teissä",
        "teistä",
        "teitä",
        "tuo",
        "tuohon",
        "tuoksi",
        "tuolla",
        "tuolle",
        "tuolta",
        "tuon",
        "tuona",
        "tuossa",
        "tuosta",
        "tuotä",
        "vaan",
        "vai",
        "vaikka",
        "yli",
    )
}

fn french(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "à",
        "ai",
        "aie",
        "aient",
        "aies",
        "ait",
        "as",
        "au",
        "aura",
        "aurai",
        "auraient",
        "aurais",
        "aurait",
        "auras",
        "aurez",
        "auriez",
        "aurions",
        "aurons",
        "auront",
        "aux",
        "avaient",
        "avais",
        "avait",
        "avec",
        "avez",
        "aviez",
        "avions",
        "avons",
        "ayant",
        "ayante",
        "ayantes",
        "ayants",
        "ayez",
        "ayons",
        "c",
        "ce",
        "ces",
        "d",
        "dans",
        "de",
        "des",
        "du",
        "elle",
        "en",
        "es",
        "est",
        "et",
        "étaient",
        "étais",
        "était",
        "étant",
        "étante",
        "étantes",
        "étants",
        "été",
        "étée",
        "étées",
        "étés",
        "êtes",
        "étiez",
        "étions",
        "eu",
        "eue",
        "eues",
        "eûmes",
        "eurent",
        "eus",
        "eusse",
        "eussent",
        "eusses",
        "eussiez",
        "eussions",
        "eut",
        "eût",
        "eûtes",
        "eux",
        "fûmes",
        "furent",
        "fus",
        "fusse",
        "fussent",
        "fusses",
        "fussiez",
        "fussions",
        "fut",
        "fût",
        "fûtes",
        "il",
        "j",
        "je",
        "l",
        "la",
        "le",
        "leur",
        "lui",
        "m",
        "ma",
        "mais",
        "me",
        "même",
        "mes",
        "moi",
        "mon",
        "n",
        "ne",
        "nos",
        "notre",
        "nous",
        "on",
        "ont",
        "ou",
        "par",
        "pas",
        "pour",
        "qu",
        "que",
        "qui",
        "s",
        "sa",
        "se",
        "sera",
        "serai",
        "seraient",
        "serais",
        "serait",
        "seras",
        "serez",
        "seriez",
        "serions",
        "serons",
        "seront",
        "ses",
        "soient",
        "sois",
        "soit",
        "sommes",
        "son",
        "sont",
        "soyez",
        "soyons",
        "suis",
        "sur",
        "t",
        "ta",
        "te",
        "tes",
        "toi",
        "ton",
        "tu",
        "un",
        "une",
        "vos",
        "votre",
        "vous",
        "y",
    )
}

fn german(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "aber",
        "alle",
        "allem",
        "allen",
        "aller",
        "alles",
        "als",
        "also",
        "am",
        "an",
        "ander",
        "andere",
        "anderem",
        "anderen",
        "anderer",
        "anderes",
        "anderm",
        "andern",
        "anderr",
        "anders",
        "auch",
        "auf",
        "aus",
        "bei",
        "bin",
        "bis",
        "bist",
        "da",
        "damit",
        "dann",
        "das",
        "dasselbe",
        "dazu",
        "daß",
        "dein",
        "deine",
        "deinem",
        "deinen",
        "deiner",
        "deines",
        "dem",
        "demselben",
        "den",
        "denn",
        "denselben",
        "der",
        "derer",
        "derselbe",
        "derselben",
        "des",
        "desselben",
        "dessen",
        "dich",
        "die",
        "dies",
        "diese",
        "dieselbe",
        "dieselben",
        "diesem",
        "diesen",
        "dieser",
        "dieses",
        "dir",
        "doch",
        "dort",
        "du",
        "durch",
        "ein",
        "eine",
        "einem",
        "einen",
        "einer",
        "eines",
        "einig",
        "einige",
        "einigem",
        "einigen",
        "einiger",
        "einiges",
        "einmal",
        "er",
        "es",
        "etwas",
        "euch",
        "euer",
        "eure",
        "eurem",
        "euren",
        "eurer",
        "eures",
        "für",
        "gegen",
        "gewesen",
        "hab",
        "habe",
        "haben",
        "hat",
        "hatte",
        "hatten",
        "hier",
        "hin",
        "hinter",
        "ich",
        "ihm",
        "ihn",
        "ihnen",
        "ihr",
        "ihre",
        "ihrem",
        "ihren",
        "ihrer",
        "ihres",
        "im",
        "in",
        "indem",
        "ins",
        "ist",
        "jede",
        "jedem",
        "jeden",
        "jeder",
        "jedes",
        "jene",
        "jenem",
        "jenen",
        "jener",
        "jenes",
        "jetzt",
        "kann",
        "kein",
        "keine",
        "keinem",
        "keinen",
        "keiner",
        "keines",
        "können",
        "könnte",
        "machen",
        "man",
        "manche",
        "manchem",
        "manchen",
        "mancher",
        "manches",
        "mein",
        "meine",
        "meinem",
        "meinen",
        "meiner",
        "meines",
        "mich",
        "mir",
        "mit",
        "muss",
        "musste",
        "nach",
        "nicht",
        "nichts",
        "noch",
        "nun",
        "nur",
        "ob",
        "oder",
        "ohne",
        "sehr",
        "sein",
        "seine",
        "seinem",
        "seinen",
        "seiner",
        "seines",
        "selbst",
        "sich",
        "sie",
        "sind",
        "so",
        "solche",
        "solchem",
        "solchen",
        "solcher",
        "solches",
        "soll",
        "sollte",
        "sondern",
        "sonst",
        "über",
        "um",
        "und",
        "uns",
        "unser",
        "unsere",
        "unserem",
        "unseren",
        "unseres",
        "unter",
        "viel",
        "vom",
        "von",
        "vor",
        "während",
        "war",
        "waren",
        "warst",
        "was",
        "weg",
        "weil",
        "weiter",
        "welche",
        "welchem",
        "welchen",
        "welcher",
        "welches",
        "wenn",
        "werde",
        "werden",
        "wie",
        "wieder",
        "will",
        "wir",
        "wird",
        "wirst",
        "wo",
        "wollen",
        "wollte",
        "würde",
        "würden",
        "zu",
        "zum",
        "zur",
        "zwar",
        "zwischen",
    )
}

fn greek(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "η",
        "κ",
        "ο",
        "ἃ",
        "ἡ",
        "ἢ",
        "ἣ",
        "ἤ",
        "ἥ",
        "ὁ",
        "ὃ",
        "ὅ",
        "ὦ",
        "ᾧ",
        "δ'",
        "αν",
        "αἱ",
        "αἳ",
        "αἵ",
        "αὖ",
        "γα",
        "γε",
        "δέ",
        "δή",
        "δε",
        "δὲ",
        "δὴ",
        "δ’",
        "επ",
        "εἰ",
        "εἴ",
        "θα",
        "κι",
        "μή",
        "μα",
        "με",
        "μη",
        "μὴ",
        "να",
        "οι",
        "οἱ",
        "οἳ",
        "οὐ",
        "οὗ",
        "σε",
        "σύ",
        "σὺ",
        "τά",
        "τί",
        "τα",
        "τε",
        "τι",
        "το",
        "τό",
        "τὰ",
        "τὸ",
        "τῇ",
        "τῷ",
        "ωσ",
        "ἀπ",
        "ἀφ",
        "ἂν",
        "ἄν",
        "ἐκ",
        "ἐν",
        "ἐξ",
        "ἐφ",
        "ἧς",
        "ὃν",
        "ὃς",
        "ὅς",
        "ὅσ",
        "ὑπ",
        "ὡς",
        "ὡσ",
        "ὥς",
        "δι'",
        "γα^",
        "απο",
        "γάρ",
        "για",
        "γὰρ",
        "δαί",
        "δαὶ",
        "δεν",
        "διά",
        "διὰ",
        "εαν",
        "ενω",
        "επι",
        "εἰς",
        "εἰσ",
        "καί",
        "καθ",
        "και",
        "κατ",
        "καὶ",
        "κἀν",
        "κἂν",
        "μέν",
        "μεθ",
        "μετ",
        "μην",
        "μἐν",
        "μὲν",
        "μὴν",
        "οσο",
        "οτι",
        "οἷς",
        "οὐδ",
        "οὐκ",
        "οὐχ",
        "οὓς",
        "οὖν",
        "παρ",
        "που",
        "ποῦ",
        "προ",
        "πρὸ",
        "πως",
        "πωσ",
        "στη",
        "στο",
        "σόσ",
        "σύν",
        "σὸς",
        "σὺν",
        "τήν",
        "τίς",
        "τίσ",
        "την",
        "τησ",
        "τις",
        "τισ",
        "τοί",
        "τοι",
        "τον",
        "του",
        "τοῦ",
        "των",
        "τόν",
        "τὰς",
        "τὴν",
        "τὸν",
        "τῆς",
        "τῆσ",
        "τῶν",
        "ἀπό",
        "ἀπὸ",
        "ἄρα",
        "ἅμα",
        "ἐάν",
        "ἐγώ",
        "ἐγὼ",
        "ἐπί",
        "ἐπὶ",
        "ἐὰν",
        "ἔτι",
        "ἵνα",
        "ὅδε",
        "ὅτε",
        "ὅτι",
        "ὑπό",
        "ὑπὸ",
        "ἀλλ'",
        "αλλα",
        "αντι",
        "αυτα",
        "αυτη",
        "αυτο",
        "γοῦν",
        "δαίσ",
        "δαὶς",
        "εἰμί",
        "εἰμὶ",
        "εἴμι",
        "εἴτε",
        "ισωσ",
        "κατά",
        "κατα",
        "κατὰ",
        "μήτε",
        "μετά",
        "μετα",
        "μετὰ",
        "ομωσ",
        "οπωσ",
        "οὐδέ",
        "οὐδὲ",
        "οὐχὶ",
        "οὔτε",
        "οὕτω",
        "παρά",
        "παρα",
        "παρὰ",
        "περί",
        "περὶ",
        "ποια",
        "ποιο",
        "ποτε",
        "προσ",
        "πρόσ",
        "πρὸς",
        "στην",
        "στον",
        "ταῖς",
        "τινα",
        "τοτε",
        "τούσ",
        "τοὺς",
        "τοῖς",
        "τότε",
        "ἀλλά",
        "ἀλλὰ",
        "ἀλλ’",
        "ἐμόσ",
        "ἐμὸς",
        "ἐπεὶ",
        "ἐστι",
        "ὅθεν",
        "ὅπερ",
        "ὑμόσ",
        "ὑπέρ",
        "ὑπὲρ",
        "ὥστε",
        "αυτεσ",
        "αυτοι",
        "αυτοσ",
        "αυτων",
        "αὐτόσ",
        "αὐτὸς",
        "ειμαι",
        "ειναι",
        "εισαι",
        "ειστε",
        "οὐδὲν",
        "οὕτως",
        "οὕτωσ",
        "οὗτος",
        "οὗτοσ",
        "ποιεσ",
        "ποιοι",
        "ποιοσ",
        "ποιων",
        "ἄλλος",
        "ἄλλοσ",
        "ὅστις",
        "ὅστισ",
        "αυτουσ",
        "εκεινα",
        "εκεινη",
        "εκεινο",
        "καίτοι",
        "οὐδείσ",
        "οὐδεὶς",
        "ποιουσ",
        "ἑαυτοῦ",
        "ειμαστε",
        "εκεινεσ",
        "εκεινοι",
        "εκεινοσ",
        "εκεινων",
        "εκεινουσ",
        "τοιοῦτος",
        "τοιοῦτοσ",
    )
}

fn hungarian(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "abban",
        "ahhoz",
        "ahogy",
        "ahol",
        "aki",
        "akik",
        "akkor",
        "alatt",
        "által",
        "általában",
        "amely",
        "amelyek",
        "amelyekben",
        "amelyeket",
        "amelyet",
        "amelynek",
        "ami",
        "amíg",
        "amikor",
        "amit",
        "amolyan",
        "annak",
        "arra",
        "arról",
        "át",
        "az",
        "azért",
        "azok",
        "azon",
        "azonban",
        "azt",
        "aztán",
        "azután",
        "azzal",
        "bár",
        "be",
        "belül",
        "benne",
        "cikk",
        "cikkek",
        "cikkeket",
        "csak",
        "de",
        "e",
        "ebben",
        "eddig",
        "egész",
        "egy",
        "egyéb",
        "egyes",
        "egyetlen",
        "egyik",
        "egyre",
        "ehhez",
        "ekkor",
        "el",
        "elég",
        "ellen",
        "elõ",
        "elõször",
        "elõtt",
        "elsõ",
        "emilyen",
        "én",
        "ennek",
        "éppen",
        "erre",
        "és",
        "ez",
        "ezek",
        "ezen",
        "ezért",
        "ezt",
        "ezzel",
        "fel",
        "felé",
        "hanem",
        "hiszen",
        "hogy",
        "hogyan",
        "igen",
        "így",
        "ill",
        "ill.",
        "illetve",
        "ilyen",
        "ilyenkor",
        "ismét",
        "ison",
        "itt",
        "jó",
        "jobban",
        "jól",
        "kell",
        "kellett",
        "keressünk",
        "keresztül",
        "ki",
        "kívül",
        "között",
        "közül",
        "legalább",
        "legyen",
        "lehet",
        "lehetett",
        "lenne",
        "lenni",
        "lesz",
        "lett",
        "maga",
        "magát",
        "majd",
        "már",
        "más",
        "másik",
        "meg",
        "még",
        "mellett",
        "mely",
        "melyek",
        "mert",
        "mi",
        "miért",
        "míg",
        "mikor",
        "milyen",
        "minden",
        "mindenki",
        "mindent",
        "mindig",
        "mint",
        "mintha",
        "mit",
        "mivel",
        "most",
        "nagy",
        "nagyobb",
        "nagyon",
        "ne",
        "néha",
        "néhány",
        "nekem",
        "neki",
        "nélkül",
        "nem",
        "nincs",
        "õ",
        "õk",
        "õket",
        "olyan",
        "össze",
        "ott",
        "pedig",
        "persze",
        "rá",
        "s",
        "saját",
        "sem",
        "semmi",
        "sok",
        "sokat",
        "sokkal",
        "számára",
        "szemben",
        "szerint",
        "szinte",
        "talán",
        "tehát",
        "teljes",
        "több",
        "tovább",
        "továbbá",
        "úgy",
        "ugyanis",
        "új",
        "újabb",
        "újra",
        "után",
        "utána",
        "utolsó",
        "vagy",
        "vagyis",
        "vagyok",
        "valaki",
        "valami",
        "valamint",
        "való",
        "van",
        "vannak",
        "vele",
        "vissza",
        "viszont",
        "volna",
        "volt",
        "voltak",
        "voltam",
        "voltunk",
    )
}

fn italian(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "abbia",
        "abbiamo",
        "abbiano",
        "abbiate",
        "ad",
        "agl",
        "agli",
        "ai",
        "al",
        "all",
        "alla",
        "alle",
        "allo",
        "anche",
        "avemmo",
        "avendo",
        "avesse",
        "avessero",
        "avessi",
        "avessimo",
        "aveste",
        "avesti",
        "avete",
        "aveva",
        "avevamo",
        "avevano",
        "avevate",
        "avevi",
        "avevo",
        "avrà",
        "avrai",
        "avranno",
        "avrebbe",
        "avrebbero",
        "avrei",
        "avremmo",
        "avremo",
        "avreste",
        "avresti",
        "avrete",
        "avrò",
        "avuta",
        "avute",
        "avuti",
        "avuto",
        "c",
        "che",
        "chi",
        "ci",
        "coi",
        "col",
        "come",
        "con",
        "contro",
        "cui",
        "da",
        "dagl",
        "dagli",
        "dai",
        "dal",
        "dall",
        "dalla",
        "dalle",
        "dallo",
        "degl",
        "degli",
        "dei",
        "del",
        "dell",
        "della",
        "delle",
        "dello",
        "di",
        "dov",
        "dove",
        "e",
        "è",
        "ebbe",
        "ebbero",
        "ebbi",
        "ed",
        "era",
        "erano",
        "eravamo",
        "eravate",
        "eri",
        "ero",
        "essendo",
        "faccia",
        "facciamo",
        "facciano",
        "facciate",
        "faccio",
        "facemmo",
        "facendo",
        "facesse",
        "facessero",
        "facessi",
        "facessimo",
        "faceste",
        "facesti",
        "faceva",
        "facevamo",
        "facevano",
        "facevate",
        "facevi",
        "facevo",
        "fai",
        "fanno",
        "farà",
        "farai",
        "faranno",
        "farebbe",
        "farebbero",
        "farei",
        "faremmo",
        "faremo",
        "fareste",
        "faresti",
        "farete",
        "farò",
        "fece",
        "fecero",
        "feci",
        "fosse",
        "fossero",
        "fossi",
        "fossimo",
        "foste",
        "fosti",
        "fu",
        "fui",
        "fummo",
        "furono",
        "gli",
        "ha",
        "hai",
        "hanno",
        "ho",
        "i",
        "il",
        "in",
        "io",
        "l",
        "la",
        "le",
        "lei",
        "li",
        "lo",
        "loro",
        "lui",
        "ma",
        "mi",
        "mia",
        "mie",
        "miei",
        "mio",
        "ne",
        "negl",
        "negli",
        "nei",
        "nel",
        "nell",
        "nella",
        "nelle",
        "nello",
        "noi",
        "non",
        "nostra",
        "nostre",
        "nostri",
        "nostro",
        "o",
        "per",
        "perché",
        "più",
        "quale",
        "quanta",
        "quante",
        "quanti",
        "quanto",
        "quella",
        "quelle",
        "quelli",
        "quello",
        "questa",
        "queste",
        "questi",
        "questo",
        "sarà",
        "sarai",
        "saranno",
        "sarebbe",
        "sarebbero",
        "sarei",
        "saremmo",
        "saremo",
        "sareste",
        "saresti",
        "sarete",
        "sarò",
        "se",
        "sei",
        "si",
        "sia",
        "siamo",
        "siano",
        "siate",
        "siete",
        "sono",
        "sta",
        "stai",
        "stando",
        "stanno",
        "starà",
        "starai",
        "staranno",
        "starebbe",
        "starebbero",
        "starei",
        "staremmo",
        "staremo",
        "stareste",
        "staresti",
        "starete",
        "starò",
        "stava",
        "stavamo",
        "stavano",
        "stavate",
        "stavi",
        "stavo",
        "stemmo",
        "stesse",
        "stessero",
        "stessi",
        "stessimo",
        "steste",
        "stesti",
        "stette",
        "stettero",
        "stetti",
        "stia",
        "stiamo",
        "stiano",
        "stiate",
        "sto",
        "su",
        "sua",
        "sue",
        "sugl",
        "sugli",
        "sui",
        "sul",
        "sull",
        "sulla",
        "sulle",
        "sullo",
        "suo",
        "suoi",
        "ti",
        "tra",
        "tu",
        "tua",
        "tue",
        "tuo",
        "tuoi",
        "tutti",
        "tutto",
        "un",
        "una",
        "uno",
        "vi",
        "voi",
        "vostra",
        "vostre",
        "vostri",
        "vostro",
    )
}

fn norwegian(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "å",
        "alle",
        "at",
        "av",
        "både",
        "båe",
        "bare",
        "begge",
        "ble",
        "blei",
        "bli",
        "blir",
        "blitt",
        "da",
        "då",
        "de",
        "deg",
        "dei",
        "deim",
        "deira",
        "deires",
        "dem",
        "den",
        "denne",
        "der",
        "dere",
        "deres",
        "det",
        "dette",
        "di",
        "din",
        "disse",
        "ditt",
        "du",
        "dykk",
        "dykkar",
        "eg",
        "ein",
        "eit",
        "eitt",
        "eller",
        "elles",
        "en",
        "enn",
        "er",
        "et",
        "ett",
        "etter",
        "for",
        "før",
        "fordi",
        "fra",
        "ha",
        "hadde",
        "han",
        "hans",
        "har",
        "hennar",
        "henne",
        "hennes",
        "her",
        "hjå",
        "ho",
        "hoe",
        "honom",
        "hoss",
        "hossen",
        "hun",
        "hva",
        "hvem",
        "hver",
        "hvilke",
        "hvilken",
        "hvis",
        "hvor",
        "hvordan",
        "hvorfor",
        "i",
        "ikke",
        "ikkje",
        "ingen",
        "ingi",
        "inkje",
        "inn",
        "inni",
        "ja",
        "jeg",
        "kan",
        "kom",
        "korleis",
        "korso",
        "kun",
        "kunne",
        "kva",
        "kvar",
        "kvarhelst",
        "kven",
        "kvi",
        "kvifor",
        "man",
        "mange",
        "me",
        "med",
        "medan",
        "meg",
        "meget",
        "mellom",
        "men",
        "mi",
        "min",
        "mine",
        "mitt",
        "mot",
        "mykje",
        "nå",
        "når",
        "ned",
        "no",
        "noe",
        "noen",
        "noka",
        "noko",
        "nokon",
        "nokor",
        "nokre",
        "og",
        "også",
        "om",
        "opp",
        "oss",
        "over",
        "på",
        "så",
        "samme",
        "sånn",
        "seg",
        "selv",
        "si",
        "sia",
        "sidan",
        "siden",
        "sin",
        "sine",
        "sitt",
        "sjøl",
        "skal",
        "skulle",
        "slik",
        "so",
        "som",
        "somme",
        "somt",
        "til",
        "um",
        "upp",
        "ut",
        "uten",
        "var",
        "vår",
        "være",
        "vart",
        "vært",
        "varte",
        "ved",
        "vere",
        "verte",
        "vi",
        "vil",
        "ville",
        "vore",
        "vors",
        "vort",
    )
}

fn nepali(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "छ",
        "त",
        "न",
        "म",
        "र",
        "अब",
        "आए",
        "उप",
        "एक",
        "ओठ",
        "औं",
        "का",
        "कि",
        "के",
        "को",
        "गए",
        "छु",
        "छू",
        "जब",
        "जे",
        "जो",
        "तर",
        "तल",
        "ती",
        "नि",
        "नै",
        "नौ",
        "भए",
        "भन",
        "भर",
        "मा",
        "यस",
        "या",
        "यी",
        "यो",
        "ले",
        "सो",
        "हो",
        "कम से कम",
        "अझै",
        "अरु",
        "अलग",
        "आदि",
        "आफू",
        "आयो",
        "कतै",
        "कसै",
        "किन",
        "गयौ",
        "गरि",
        "गरी",
        "गैर",
        "चार",
        "छन्",
        "छैन",
        "छौं",
        "जान",
        "जुन",
        "ठीक",
        "तथा",
        "तिर",
        "तीन",
        "थिए",
        "दिए",
        "दुई",
        "पछि",
        "पटक",
        "पनि",
        "बने",
        "बरु",
        "बीच",
        "भने",
        "भन्",
        "यति",
        "यदि",
        "यसो",
        "रही",
        "रूप",
        "लाई",
        "संग",
        "सधै",
        "सबै",
        "समय",
        "सही",
        "सात",
        "साथ",
        "हरे",
        "हुन",
        "अन्य",
        "आजको",
        "आत्म",
        "उनको",
        "उनले",
        "एउटै",
        "एकदम",
        "कसरी",
        "कुनै",
        "कुरा",
        "केही",
        "कोही",
        "गरेर",
        "गरौं",
        "गर्छ",
        "गर्न",
        "चाले",
        "जबकि",
        "जसको",
        "जसमा",
        "जसले",
        "जहाँ",
        "तपाई",
        "तिनी",
        "तिमी",
        "त्यो",
        "थिएन",
        "थियो",
        "देखि",
        "देखे",
        "धेरै",
        "नत्र",
        "नयाँ",
        "पर्छ",
        "पाँच",
        "प्लस",
        "फेरी",
        "बारे",
        "भएको",
        "मलाई",
        "माथि",
        "मेरो",
        "यसको",
        "यसरी",
        "यहाँ",
        "राखे",
        "लगभग",
        "लागि",
        "शायद",
        "संगै",
        "सक्छ",
        "सम्म",
        "साथै",
        "सायद",
        "सारा",
        "सोही",
        "हरेक",
        "हुने",
        "हुन्",
        "अक्सर",
        "अगाडी",
        "अर्को",
        "आफ्नै",
        "आफ्नो",
        "कसैले",
        "कृपया",
        "गरेका",
        "गरेको",
        "गर्छु",
        "गर्दै",
        "गर्नु",
        "गर्ने",
        "चाहिए",
        "जसबाट",
        "जसलाई",
        "जस्तै",
        "जस्तो",
        "जाहिर",
        "तापनी",
        "देखेर",
        "नजिकै",
        "निम्न",
        "पक्का",
        "पक्कै",
        "पहिले",
        "पहिलो",
        "पूर्व",
        "प्रति",
        "बाहिर",
        "बाहेक",
        "बिशेष",
        "बीचमा",
        "भन्छु",
        "भन्दा",
        "भन्ने",
        "भित्र",
        "मात्र",
        "मुख्य",
        "यसपछि",
        "यस्तो",
        "रहेका",
        "रहेको",
        "राख्छ",
        "सट्टा",
        "सम्भव",
        "हुन्छ",
        "अनुसार",
        "अन्यथा",
        "अरुलाई",
        "अर्थात",
        "आफूलाई",
        "उदाहरण",
        "उहालाई",
        "किनभने",
        "क्रमशः",
        "जताततै",
        "तत्काल",
        "तपाईको",
        "तेस्रो",
        "त्यहाँ",
        "त्सपछि",
        "त्सैले",
        "देखियो",
        "देखेको",
        "दोस्रो",
        "निम्ति",
        "पाँचौं",
        "प्रतेक",
        "भन्छन्",
        "भित्री",
        "यथोचित",
        "यद्यपि",
        "राम्रो",
        "वरीपरी",
        "सबैलाई",
        "स्पष्ट",
        "अन्यत्र",
        "अर्थात्",
        "कहाँबाट",
        "चाहन्छु",
        "तदनुसार",
        "तिनीहरू",
        "देखिन्छ",
        "पछिल्लो",
        "पर्थ्यो",
        "पहिल्यै",
        "बिरुद्ध",
        "यसबाहेक",
        "साँच्चै",
        "अन्तर्गत",
        "तुरुन्तै",
        "तेस्कारण",
        "दिनुभएको",
        "पर्याप्त",
        "भन्नुभयो",
        "यहाँसम्म",
        "वास्तवमा",
        "गर्नुपर्छ",
        "जस्तोसुकै",
        "तिनीहरुको",
        "दिनुहुन्छ",
        "निर्दिष्ट",
        "कहिलेकाहीं",
        "चाहनुहुन्छ",
        "तिनिहरुलाई",
        "निम्नानुसार",
    )
}

fn portuguese(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "à",
        "ao",
        "aos",
        "aquela",
        "aquelas",
        "aquele",
        "aqueles",
        "aquilo",
        "as",
        "às",
        "até",
        "com",
        "como",
        "da",
        "das",
        "de",
        "dela",
        "delas",
        "dele",
        "deles",
        "depois",
        "do",
        "dos",
        "e",
        "ela",
        "elas",
        "ele",
        "eles",
        "em",
        "entre",
        "era",
        "eram",
        "éramos",
        "essa",
        "essas",
        "esse",
        "esses",
        "esta",
        "está",
        "estamos",
        "estão",
        "estas",
        "estava",
        "estavam",
        "estávamos",
        "este",
        "esteja",
        "estejam",
        "estejamos",
        "estes",
        "esteve",
        "estive",
        "estivemos",
        "estiver",
        "estivera",
        "estiveram",
        "estivéramos",
        "estiverem",
        "estivermos",
        "estivesse",
        "estivessem",
        "estivéssemos",
        "estou",
        "eu",
        "foi",
        "fomos",
        "for",
        "fora",
        "foram",
        "fôramos",
        "forem",
        "formos",
        "fosse",
        "fossem",
        "fôssemos",
        "fui",
        "há",
        "haja",
        "hajam",
        "hajamos",
        "hão",
        "havemos",
        "hei",
        "houve",
        "houvemos",
        "houver",
        "houvera",
        "houverá",
        "houveram",
        "houvéramos",
        "houverão",
        "houverei",
        "houverem",
        "houveremos",
        "houveria",
        "houveriam",
        "houveríamos",
        "houvermos",
        "houvesse",
        "houvessem",
        "houvéssemos",
        "isso",
        "isto",
        "já",
        "lhe",
        "lhes",
        "mais",
        "mas",
        "me",
        "mesmo",
        "meu",
        "meus",
        "minha",
        "minhas",
        "muito",
        "na",
        "não",
        "nas",
        "nem",
        "no",
        "nos",
        "nós",
        "nossa",
        "nossas",
        "nosso",
        "nossos",
        "num",
        "numa",
        "o",
        "os",
        "ou",
        "para",
        "pela",
        "pelas",
        "pelo",
        "pelos",
        "por",
        "qual",
        "quando",
        "que",
        "quem",
        "são",
        "se",
        "seja",
        "sejam",
        "sejamos",
        "sem",
        "será",
        "serão",
        "serei",
        "seremos",
        "seria",
        "seriam",
        "seríamos",
        "seu",
        "seus",
        "só",
        "somos",
        "sou",
        "sua",
        "suas",
        "também",
        "te",
        "tem",
        "tém",
        "temos",
        "tenha",
        "tenham",
        "tenhamos",
        "tenho",
        "terá",
        "terão",
        "terei",
        "teremos",
        "teria",
        "teriam",
        "teríamos",
        "teu",
        "teus",
        "teve",
        "tinha",
        "tinham",
        "tínhamos",
        "tive",
        "tivemos",
        "tiver",
        "tivera",
        "tiveram",
        "tivéramos",
        "tiverem",
        "tivermos",
        "tivesse",
        "tivessem",
        "tivéssemos",
        "tu",
        "tua",
        "tuas",
        "um",
        "uma",
        "você",
        "vocês",
        "vos",
    )
}

fn romanian(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "abia",
        "acea",
        "aceasta",
        "această",
        "aceea",
        "aceeasi",
        "acei",
        "aceia",
        "acel",
        "acela",
        "acelasi",
        "acele",
        "acelea",
        "acest",
        "acesta",
        "aceste",
        "acestea",
        "acestei",
        "acestia",
        "acestui",
        "aceşti",
        "aceştia",
        "adica",
        "ai",
        "aia",
        "aibă",
        "aici",
        "al",
        "ala",
        "ale",
        "alea",
        "alt",
        "alta",
        "altceva",
        "altcineva",
        "alte",
        "altfel",
        "alti",
        "altii",
        "altul",
        "am",
        "anume",
        "apoi",
        "ar",
        "are",
        "as",
        "asa",
        "asta",
        "astea",
        "astfel",
        "asupra",
        "atare",
        "atat",
        "atata",
        "atatea",
        "atatia",
        "ati",
        "atit",
        "atita",
        "atitea",
        "atitia",
        "atunci",
        "au",
        "avea",
        "avem",
        "aveţi",
        "avut",
        "aş",
        "aţi",
        "ba",
        "ca",
        "cam",
        "cand",
        "care",
        "careia",
        "carora",
        "caruia",
        "cat",
        "cât",
        "câte",
        "catre",
        "câtva",
        "câţi",
        "ce",
        "cea",
        "ceea",
        "cei",
        "ceilalti",
        "cel",
        "cele",
        "celor",
        "ceva",
        "chiar",
        "ci",
        "cind",
        "cînd",
        "cine",
        "cineva",
        "cit",
        "cît",
        "cita",
        "cite",
        "cîte",
        "citeva",
        "citi",
        "citiva",
        "cîtva",
        "cîţi",
        "cu",
        "cui",
        "cum",
        "cumva",
        "că",
        "căci",
        "cărei",
        "căror",
        "cărui",
        "către",
        "da",
        "daca",
        "dacă",
        "dar",
        "dat",
        "dată",
        "dau",
        "de",
        "deasupra",
        "deci",
        "decit",
        "deja",
        "desi",
        "despre",
        "deşi",
        "din",
        "dintr",
        "dintr-",
        "dintre",
        "doar",
        "doi",
        "doilea",
        "două",
        "drept",
        "dupa",
        "după",
        "dă",
        "e",
        "ea",
        "ei",
        "el",
        "ele",
        "era",
        "eram",
        "este",
        "eu",
        "eşti",
        "face",
        "fara",
        "fata",
        "fel",
        "fi",
        "fie",
        "fiecare",
        "fii",
        "fim",
        "fiu",
        "fiţi",
        "foarte",
        "fost",
        "fără",
        "i",
        "ia",
        "iar",
        "ii",
        "îi",
        "il",
        "îl",
        "imi",
        "îmi",
        "in",
        "în",
        "inainte",
        "inapoi",
        "inca",
        "incit",
        "insa",
        "intr",
        "intre",
        "isi",
        "iti",
        "îţi",
        "la",
        "lângă",
        "le",
        "li",
        "lîngă",
        "lor",
        "lui",
        "m",
        "ma",
        "mai",
        "mâine",
        "mea",
        "mei",
        "mele",
        "mereu",
        "meu",
        "mi",
        "mie",
        "mîine",
        "mine",
        "mod",
        "mult",
        "multa",
        "multe",
        "multi",
        "multă",
        "mulţi",
        "mă",
        "ne",
        "ni",
        "nici",
        "nimeni",
        "nimic",
        "niste",
        "nişte",
        "noastre",
        "noastră",
        "noi",
        "nostri",
        "nostru",
        "nou",
        "noua",
        "nouă",
        "noştri",
        "nu",
        "numai",
        "o",
        "or",
        "ori",
        "oricând",
        "oricare",
        "oricât",
        "orice",
        "oricînd",
        "oricine",
        "oricît",
        "oricum",
        "oriunde",
        "pai",
        "până",
        "parca",
        "patra",
        "patru",
        "pe",
        "pentru",
        "peste",
        "pic",
        "pina",
        "pînă",
        "poate",
        "pot",
        "prea",
        "prima",
        "primul",
        "prin",
        "printr-",
        "putini",
        "puţin",
        "puţina",
        "puţină",
        "sa",
        "sa-mi",
        "sa-ti",
        "sai",
        "sale",
        "sau",
        "se",
        "si",
        "sint",
        "sintem",
        "spate",
        "spre",
        "sub",
        "sunt",
        "suntem",
        "sunteţi",
        "sus",
        "să",
        "săi",
        "său",
        "t",
        "ta",
        "tale",
        "te",
        "ti",
        "tine",
        "toata",
        "toate",
        "toată",
        "tocmai",
        "tot",
        "toti",
        "totul",
        "totusi",
        "totuşi",
        "toţi",
        "trei",
        "treia",
        "treilea",
        "tu",
        "tuturor",
        "tăi",
        "tău",
        "u",
        "ul",
        "ului",
        "un",
        "una",
        "unde",
        "undeva",
        "unei",
        "uneia",
        "unele",
        "uneori",
        "unii",
        "unor",
        "unora",
        "unu",
        "unui",
        "unuia",
        "unul",
        "v",
        "va",
        "vi",
        "voastre",
        "voastră",
        "voi",
        "vom",
        "vor",
        "vostru",
        "vouă",
        "voştri",
        "vreo",
        "vreun",
        "vă",
        "zi",
        "zice",
        "şi",
        "ţi",
        "ţie",
        "ăla",
        "ălea",
        "ăsta",
        "ăstea",
        "ăştia",
    )
}

fn russian(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "а",
        "в",
        "ж",
        "и",
        "к",
        "о",
        "с",
        "у",
        "я",
        "бы",
        "во",
        "вы",
        "да",
        "до",
        "ее",
        "ей",
        "же",
        "за",
        "из",
        "им",
        "их",
        "ли",
        "мы",
        "на",
        "не",
        "ни",
        "но",
        "ну",
        "об",
        "он",
        "от",
        "по",
        "со",
        "то",
        "ты",
        "уж",
        "без",
        "был",
        "вам",
        "вас",
        "вот",
        "все",
        "всю",
        "где",
        "два",
        "для",
        "его",
        "ему",
        "еще",
        "или",
        "как",
        "кто",
        "мне",
        "мой",
        "моя",
        "над",
        "нас",
        "нее",
        "ней",
        "нет",
        "ним",
        "них",
        "она",
        "они",
        "под",
        "при",
        "про",
        "раз",
        "сам",
        "так",
        "там",
        "тем",
        "том",
        "тот",
        "три",
        "тут",
        "уже",
        "чем",
        "что",
        "эти",
        "эту",
        "была",
        "были",
        "было",
        "быть",
        "ведь",
        "всех",
        "даже",
        "если",
        "есть",
        "куда",
        "меня",
        "надо",
        "него",
        "один",
        "свою",
        "себе",
        "себя",
        "тебя",
        "того",
        "тоже",
        "хоть",
        "чего",
        "чтоб",
        "чуть",
        "этой",
        "этом",
        "этот",
        "более",
        "будет",
        "будто",
        "вдруг",
        "всего",
        "зачем",
        "здесь",
        "какая",
        "какой",
        "когда",
        "лучше",
        "между",
        "много",
        "может",
        "можно",
        "опять",
        "перед",
        "после",
        "потом",
        "почти",
        "разве",
        "такой",
        "тогда",
        "через",
        "чтобы",
        "этого",
        "больше",
        "всегда",
        "другой",
        "иногда",
        "нельзя",
        "нибудь",
        "ничего",
        "потому",
        "сейчас",
        "совсем",
        "теперь",
        "только",
        "хорошо",
        "впрочем",
        "конечно",
        "наконец",
        "никогда",
    )
}

fn spanish(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "a",
        "al",
        "algo",
        "algunas",
        "algunos",
        "ante",
        "antes",
        "como",
        "con",
        "contra",
        "cual",
        "cuando",
        "de",
        "del",
        "desde",
        "donde",
        "durante",
        "e",
        "el",
        "él",
        "ella",
        "ellas",
        "ellos",
        "en",
        "entre",
        "era",
        "erais",
        "éramos",
        "eran",
        "eras",
        "eres",
        "es",
        "esa",
        "esas",
        "ese",
        "eso",
        "esos",
        "esta",
        "está",
        "estaba",
        "estabais",
        "estábamos",
        "estaban",
        "estabas",
        "estad",
        "estada",
        "estadas",
        "estado",
        "estados",
        "estáis",
        "estamos",
        "están",
        "estando",
        "estar",
        "estará",
        "estarán",
        "estarás",
        "estaré",
        "estaréis",
        "estaremos",
        "estaría",
        "estaríais",
        "estaríamos",
        "estarían",
        "estarías",
        "estas",
        "estás",
        "este",
        "esté",
        "estéis",
        "estemos",
        "estén",
        "estés",
        "esto",
        "estos",
        "estoy",
        "estuve",
        "estuviera",
        "estuvierais",
        "estuviéramos",
        "estuvieran",
        "estuvieras",
        "estuvieron",
        "estuviese",
        "estuvieseis",
        "estuviésemos",
        "estuviesen",
        "estuvieses",
        "estuvimos",
        "estuviste",
        "estuvisteis",
        "estuvo",
        "fue",
        "fuera",
        "fuerais",
        "fuéramos",
        "fueran",
        "fueras",
        "fueron",
        "fuese",
        "fueseis",
        "fuésemos",
        "fuesen",
        "fueses",
        "fui",
        "fuimos",
        "fuiste",
        "fuisteis",
        "ha",
        "habéis",
        "había",
        "habíais",
        "habíamos",
        "habían",
        "habías",
        "habida",
        "habidas",
        "habido",
        "habidos",
        "habiendo",
        "habrá",
        "habrán",
        "habrás",
        "habré",
        "habréis",
        "habremos",
        "habría",
        "habríais",
        "habríamos",
        "habrían",
        "habrías",
        "han",
        "has",
        "hasta",
        "hay",
        "haya",
        "hayáis",
        "hayamos",
        "hayan",
        "hayas",
        "he",
        "hemos",
        "hube",
        "hubiera",
        "hubierais",
        "hubiéramos",
        "hubieran",
        "hubieras",
        "hubieron",
        "hubiese",
        "hubieseis",
        "hubiésemos",
        "hubiesen",
        "hubieses",
        "hubimos",
        "hubiste",
        "hubisteis",
        "hubo",
        "la",
        "las",
        "le",
        "les",
        "lo",
        "los",
        "más",
        "me",
        "mi",
        "mí",
        "mía",
        "mías",
        "mío",
        "míos",
        "mis",
        "mucho",
        "muchos",
        "muy",
        "nada",
        "ni",
        "no",
        "nos",
        "nosotras",
        "nosotros",
        "nuestra",
        "nuestras",
        "nuestro",
        "nuestros",
        "o",
        "os",
        "otra",
        "otras",
        "otro",
        "otros",
        "para",
        "pero",
        "poco",
        "por",
        "porque",
        "que",
        "qué",
        "quien",
        "quienes",
        "se",
        "sea",
        "seáis",
        "seamos",
        "sean",
        "seas",
        "sentid",
        "sentida",
        "sentidas",
        "sentido",
        "sentidos",
        "será",
        "serán",
        "serás",
        "seré",
        "seréis",
        "seremos",
        "sería",
        "seríais",
        "seríamos",
        "serían",
        "serías",
        "sí",
        "siente",
        "sin",
        "sintiendo",
        "sobre",
        "sois",
        "somos",
        "son",
        "soy",
        "su",
        "sus",
        "suya",
        "suyas",
        "suyo",
        "suyos",
        "también",
        "tanto",
        "te",
        "tendrá",
        "tendrán",
        "tendrás",
        "tendré",
        "tendréis",
        "tendremos",
        "tendría",
        "tendríais",
        "tendríamos",
        "tendrían",
        "tendrías",
        "tened",
        "tenéis",
        "tenemos",
        "tenga",
        "tengáis",
        "tengamos",
        "tengan",
        "tengas",
        "tengo",
        "tenía",
        "teníais",
        "teníamos",
        "tenían",
        "tenías",
        "tenida",
        "tenidas",
        "tenido",
        "tenidos",
        "teniendo",
        "ti",
        "tiene",
        "tienen",
        "tienes",
        "todo",
        "todos",
        "tu",
        "tú",
        "tus",
        "tuve",
        "tuviera",
        "tuvierais",
        "tuviéramos",
        "tuvieran",
        "tuvieras",
        "tuvieron",
        "tuviese",
        "tuvieseis",
        "tuviésemos",
        "tuviesen",
        "tuvieses",
        "tuvimos",
        "tuviste",
        "tuvisteis",
        "tuvo",
        "tuya",
        "tuyas",
        "tuyo",
        "tuyos",
        "un",
        "una",
        "uno",
        "unos",
        "vosostras",
        "vosostros",
        "vuestra",
        "vuestras",
        "vuestro",
        "vuestros",
        "y",
        "ya",
        "yo",
    )
}

fn swedish(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "alla",
        "allt",
        "än",
        "är",
        "åt",
        "att",
        "av",
        "blev",
        "bli",
        "blir",
        "blivit",
        "då",
        "där",
        "de",
        "dem",
        "den",
        "denna",
        "deras",
        "dess",
        "dessa",
        "det",
        "detta",
        "dig",
        "din",
        "dina",
        "ditt",
        "du",
        "efter",
        "ej",
        "eller",
        "en",
        "er",
        "era",
        "ert",
        "ett",
        "för",
        "från",
        "ha",
        "hade",
        "han",
        "hans",
        "har",
        "här",
        "henne",
        "hennes",
        "hon",
        "honom",
        "hur",
        "i",
        "icke",
        "ingen",
        "inom",
        "inte",
        "jag",
        "ju",
        "kan",
        "kunde",
        "man",
        "med",
        "mellan",
        "men",
        "mig",
        "min",
        "mina",
        "mitt",
        "mot",
        "mycket",
        "någon",
        "något",
        "några",
        "när",
        "ni",
        "nu",
        "och",
        "om",
        "oss",
        "över",
        "på",
        "så",
        "sådan",
        "sådana",
        "sådant",
        "samma",
        "sedan",
        "sig",
        "sin",
        "sina",
        "sitta",
        "själv",
        "skulle",
        "som",
        "till",
        "under",
        "upp",
        "ut",
        "utan",
        "vad",
        "var",
        "vår",
        "vara",
        "våra",
        "varför",
        "varit",
        "varje",
        "vars",
        "vart",
        "vårt",
        "vem",
        "vi",
        "vid",
        "vilka",
        "vilkas",
        "vilken",
        "vilket",
    )
}

fn turkish(input: &str) -> bool {
    hashify::set!(
        input.as_bytes(),
        "acaba",
        "ama",
        "aslında",
        "az",
        "bazı",
        "belki",
        "biri",
        "birkaç",
        "birşey",
        "biz",
        "bu",
        "çok",
        "çünkü",
        "da",
        "daha",
        "de",
        "defa",
        "diye",
        "en",
        "eğer",
        "gibi",
        "hem",
        "hep",
        "hepsi",
        "her",
        "hiç",
        "için",
        "ile",
        "ise",
        "kez",
        "ki",
        "kim",
        "mu",
        "mü",
        "mı",
        "nasıl",
        "ne",
        "neden",
        "nerde",
        "nerede",
        "nereye",
        "niçin",
        "niye",
        "o",
        "sanki",
        "siz",
        "tüm",
        "ve",
        "veya",
        "ya",
        "yani",
        "şey",
        "şu",
    )
}

/*
Not yet available for auto-detection

static KAZAKH: Set<&'static str> = phf_set! {
    "",
    "е",
    "о",
    "я",
    "ә",
    "ай",
    "ал",
    "ау",
    "ах",
    "ей",
    "еш",
    "ие",
    "кә",
    "ой",
    "ол",
    "ох",
    "па",
    "уа",
    "эй",
    "эх",
    "әй",
    "өз",
    "өй",
    "ана",
    "арс",
    "аһа",
    "бар",
    "беу",
    "біз",
    "бұл",
    "жоқ",
    "кәһ",
    "мен",
    "моһ",
    "осы",
    "оһо",
    "пай",
    "сен",
    "сол",
    "соң",
    "сіз",
    "тек",
    "тәк",
    "уай",
    "уау",
    "ура",
    "шек",
    "ырс",
    "ырқ",
    "ыңқ",
    "ірк",
    "қап",
    "құр",
    "үйт",
    "әні",
    "өзі",
    "арс-ұрс",
    "пай-пай",
    "паһ-паһ",
    "қош-қош",
    "анау",
    "барқ",
    "бері",
    "бойы",
    "болп",
    "борт",
    "былп",
    "бүйт",
    "бәрі",
    "гүрс",
    "гөрі",
    "дүрс",
    "дүңк",
    "емес",
    "жалп",
    "желп",
    "жуық",
    "кірт",
    "күрт",
    "күңк",
    "кәне",
    "кәні",
    "маңқ",
    "морт",
    "мына",
    "мышы",
    "мыңқ",
    "міне",
    "одан",
    "олар",
    "онда",
    "оның",
    "оған",
    "пфша",
    "пырс",
    "пішә",
    "сарт",
    "саңқ",
    "сона",
    "сыңқ",
    "тарс",
    "таяу",
    "тағы",
    "таңқ",
    "тырс",
    "тыңқ",
    "түге",
    "шаңқ",
    "шырт",
    "шіңк",
    "шәйт",
    "ғана",
    "қана",
    "қолп",
    "қорс",
    "қоса",
    "қыңқ",
    "үшін",
    "әйда",
    "әрне",
    "өзге",
    "өзім",
    "өзің",
    "жалт-жалт",
    "жалт-жұлт",
    "сарт-сұрт",
    "тарс-тұрс",
    "шаңқ-шаңқ",
    "шаңқ-шұңқ",
    "қалт-қалт",
    "қалт-құлт",
    "қаңқ-қаңқ",
    "қаңқ-құңқ",
    "барша",
    "бетер",
    "бізге",
    "бірақ",
    "бірге",
    "біреу",
    "бүкіл",
    "бұрын",
    "дейін",
    "ешбір",
    "ешкім",
    "кейін",
    "күллі",
    "күшім",
    "маған",
    "менде",
    "менен",
    "менің",
    "мынау",
    "пішту",
    "сайын",
    "салым",
    "саған",
    "сенде",
    "сенен",
    "сенің",
    "солай",
    "сонау",
    "сорап",
    "сізге",
    "таман",
    "тарта",
    "түгел",
    "шақты",
    "шейін",
    "ғұрлы",
    "қарай",
    "қатар",
    "құрау",
    "әрбір",
    "әрине",
    "әркім",
    "әттең",
    "әукім",
    "өзіме",
    "өзіне",
    "сенен	онан",
    "арбаң-арбаң",
    "батыр-бұтыр",
    "далаң-далаң",
    "митың-митың",
    "салаң-сұлаң",
    "құрау-құрау",
    "ыржың-тыржың",
    "алайда",
    "алатау",
    "алақай",
    "арнайы",
    "арқылы",
    "барлық",
    "бізбен",
    "бізден",
    "біздер",
    "біздің",
    "бұндай",
    "дәнеңе",
    "ештеме",
    "кейбір",
    "кәнеки",
    "мұндай",
    "оларға",
    "онымен",
    "осылай",
    "осынау",
    "себебі",
    "сияқты",
    "сондай",
    "сізбен",
    "сізден",
    "сіздер",
    "сіздің",
    "тағыда",
    "туралы",
    "шамалы",
    "шіркін",
    "ғұрлым",
    "қаралы",
    "әлдене",
    "өзінің",
    "бүгжең-бүгжең",
    "тарбаң-тарбаң",
    "қайқаң-құйқаң",
    "қаңғыр-күңгір",
    "бойымен",
    "бірдеме",
    "бірнеше",
    "ешқайсы",
    "ешқашан",
    "менімен",
    "олардан",
    "олардың",
    "олармен",
    "осындай",
    "сенімен",
    "сонымен",
    "япырмай",
    "әйтпесе",
    "әлдекім",
    "әншейін",
    "әрқайсы",
    "әрқалай",
    "өзімнің",
    "өйткені",
    "әттеген-ай",
    "арсалаң-арсалаң",
    "ербелең-ербелең",
    "қызараң-қызараң",
    "айтпақшы",
    "біздерге",
    "дегенмен",
    "ешқандай",
    "кейбіреу",
    "масқарай",
    "мәссаған",
    "ойпырмай",
    "сіздерге",
    "қайсыбір",
    "әлденеше",
    "алдақашан",
    "біздерден",
    "біздердің",
    "біздермен",
    "бәрекелді",
    "сондықтан",
    "сіздерден",
    "сіздердің",
    "сіздермен",
    "әйткенмен",
    "әлдеқалай",
    "әлдеқашан",
    "әттегенай",
    "әлдеқайдан",
    "астапыралла",
    "жаракімалла",
};
*/
