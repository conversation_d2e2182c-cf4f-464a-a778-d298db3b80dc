[package]
name = "dav"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
dav-proto = { path =  "../dav-proto" }
common = { path = "../common" }
store = { path = "../store" }
utils = { path = "../utils" }
groupware = { path = "../groupware" }
directory = { path = "../directory" }
http_proto = { path = "../http-proto" }
jmap_proto = { path =  "../jmap-proto" }
trc = { path = "../trc" }
calcard = { version = "0.1.3", features = ["rkyv"] }
hashify = { version = "0.2" }
hyper = { version = "1.0.1", features = ["server", "http1", "http2"] }
percent-encoding = "2.3.1"
rkyv = { version = "0.8.10", features = ["little_endian"] }
compact_str = "0.9.0"
chrono = "0.4.40"

[dev-dependencies]

[features]
test_mode = []
enterprise = []
