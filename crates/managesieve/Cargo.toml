[package]
name = "managesieve"
version = "0.12.4"
edition = "2024"
resolver = "2"

[dependencies]
imap_proto = { path = "../imap-proto" }
imap = { path = "../imap" }
jmap_proto = { path = "../jmap-proto" }
directory = { path = "../directory" }
common = { path = "../common" }
store = { path = "../store" }
utils = { path = "../utils" }
email = { path = "../email" }
trc = { path = "../trc" }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
sieve-rs = { version = "0.7", features = ["rkyv"] } 
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
rustls-pemfile = "2.0"
tokio = { version = "1.45", features = ["full"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }
parking_lot = "0.12"
ahash = { version = "0.8" }
md5 = "0.7.0"
compact_str = "0.9.0"
rkyv = { version = "0.8.10", features = ["little_endian"] }

[features]
test_mode = []
enterprise = []
