[package]
name = "common"
version = "0.12.4"
edition = "2024"
resolver = "2"
build = "build.rs"

[dependencies]
utils = { path = "../utils" }
nlp = { path = "../nlp" }
store = { path = "../store" }
trc = { path = "../trc" }
directory = { path = "../directory" }
jmap_proto = { path = "../jmap-proto" }
imap_proto = { path = "../imap-proto" }
sieve-rs = { version = "0.7", features = ["rkyv", "serde"] }
mail-parser = { version = "0.11", features = ["full_encoding"] } 
mail-builder = { version = "0.4" }
mail-auth = { version = "0.7.1" }
mail-send = { version = "0.5", default-features = false, features = ["cram-md5", "ring", "tls12"] }
smtp-proto = { version = "0.1", features = ["rkyv"] }
dns-update = { version = "0.1" }
calcard = { version = "0.1.3", features = ["rkyv"] }
ahash = { version = "0.8.2", features = ["serde"] }
parking_lot = "0.12.1"
regex = "1.7.0"
proxy-header = { version = "0.1.0", features = ["tokio"] }
arc-swap = "1.6.0"
rustls = { version = "0.23.5", default-features = false, features = ["std", "ring", "tls12"] }
rustls-pemfile = "2.0"
rustls-pki-types = { version = "1" }
ring = { version = "0.17" }
tokio = { version = "1.45", features = ["net", "macros"] }
tokio-rustls = { version = "0.26", default-features = false, features = ["ring", "tls12"] }
futures = "0.3"
rcgen = "0.12"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls-webpki-roots", "http2", "stream"]}
serde = { version = "1.0", features = ["derive"]}
serde_json = "1.0"
base64 = "0.22"
x509-parser = "0.17.0"
pem = "3.0"
chrono = { version = "0.4", features = ["serde"] }
hyper = { version = "1.0.1", features = ["server", "http1", "http2"] }
opentelemetry = { version = "0.29" }
opentelemetry_sdk = { version = "0.29" }
opentelemetry-otlp = { version = "0.29", default-features = false, features = ["reqwest-client", "http-proto", "trace", "metrics", "logs", "internal-logs", "grpc-tonic", "tls-webpki-roots", "reqwest-rustls-webpki-roots"] }
opentelemetry-semantic-conventions = { version = "0.29.0" }
prometheus = { version = "0.14", default-features = false }
imagesize = "0.14"
sha1 = "0.10"
sha2 = "0.10.6"
md5 = "0.7.0"
whatlang = "0.16"
idna = "1.0"
decancer = "3.0.1"
unicode-security = "0.1.0"
infer = "0.19"
bincode = { version = "2.0", features = ["serde"] }
hostname = "0.4.0"
zip = "4.0"
pwhash = "1.0.0"
xxhash-rust = { version = "0.8.5", features = ["xxh3"] }
psl = "2"
aes-gcm-siv = "0.11.1"
biscuit = "0.7.0"
rsa = "0.9.2"
p256 = { version = "0.13", features = ["ecdh"] }
p384 = { version = "0.13", features = ["ecdh"] }
num_cpus = "1.13.1"
hashify = "0.2"
rkyv = { version = "0.8.10", features = ["little_endian"] }
indexmap = "2.7.1"
tinyvec = "1.9.0"
compact_str = { version = "0.9.0", features = ["rkyv", "serde"] }

[target.'cfg(unix)'.dependencies]
privdrop = "0.5.3"
libc = "0.2.126"

[features]
test_mode = []
enterprise = []
foundation = []

[dev-dependencies]
tokio = { version = "1.45", features = ["full"] }
